<template>
  <div class="about-page">
    <div class="container">
      <div class="page-header">
        <h1>关于 X Oberon</h1>
        <p>传承经典，创新未来的巧克力品牌</p>
      </div>
      
      <div class="about-content">
        <section class="brand-story">
          <h2>品牌故事</h2>
          <p>X Oberon 诞生于对完美巧克力的执着追求。我们相信，每一块巧克力都应该是艺术品，每一口都应该是感官的盛宴。</p>
          <p>从精选世界各地的优质可可豆，到传统手工制作工艺，我们始终坚持品质至上的理念，为您带来最纯正的巧克力体验。</p>
        </section>
        
        <section class="our-mission">
          <h2>我们的使命</h2>
          <div class="mission-grid">
            <div class="mission-item">
              <div class="mission-icon">🌱</div>
              <h3>可持续发展</h3>
              <p>与可可农合作，支持可持续农业，保护环境的同时确保原料品质。</p>
            </div>
            <div class="mission-item">
              <div class="mission-icon">👨‍🍳</div>
              <h3>匠心工艺</h3>
              <p>传承传统制作工艺，每一块巧克力都经过精心调制和手工制作。</p>
            </div>
            <div class="mission-item">
              <div class="mission-icon">❤️</div>
              <h3>分享快乐</h3>
              <p>将甜蜜和快乐传递给每一位顾客，让巧克力成为生活中的美好时光。</p>
            </div>
          </div>
        </section>
        
        <section class="quality-promise">
          <h2>品质承诺</h2>
          <ul>
            <li>精选比利时进口可可豆</li>
            <li>无添加人工色素和防腐剂</li>
            <li>严格的质量控制体系</li>
            <li>新鲜制作，当日发货</li>
            <li>专业包装，确保品质</li>
          </ul>
        </section>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'About'
}
</script>

<style lang="scss" scoped>
.about-page {
  padding: 40px 0;
  
  .page-header {
    text-align: center;
    margin-bottom: 60px;
    
    h1 {
      font-size: 36px;
      font-weight: 800;
      color: var(--color-ink);
      margin-bottom: 16px;
    }
    
    p {
      font-size: 18px;
      color: #666;
    }
  }
  
  .about-content {
    max-width: 800px;
    margin: 0 auto;
    
    section {
      margin-bottom: 60px;
      
      h2 {
        font-size: 28px;
        font-weight: 700;
        color: var(--color-ink);
        margin-bottom: 24px;
        text-align: center;
      }
      
      p {
        font-size: 16px;
        line-height: 1.8;
        color: #666;
        margin-bottom: 16px;
      }
    }
    
    .mission-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 32px;
      margin-top: 40px;
      
      .mission-item {
        text-align: center;
        padding: 24px;
        border-radius: var(--radius-lg);
        background: white;
        box-shadow: var(--shadow-1);
        
        .mission-icon {
          font-size: 48px;
          margin-bottom: 16px;
        }
        
        h3 {
          font-size: 20px;
          font-weight: 600;
          color: var(--color-ink);
          margin-bottom: 12px;
        }
        
        p {
          font-size: 14px;
          color: #666;
          margin: 0;
        }
      }
    }
    
    .quality-promise {
      ul {
        list-style: none;
        padding: 0;
        
        li {
          padding: 12px 0;
          border-bottom: 1px solid #f0f0f0;
          font-size: 16px;
          color: #666;
          position: relative;
          padding-left: 24px;
          
          &:before {
            content: '✓';
            position: absolute;
            left: 0;
            color: var(--color-success);
            font-weight: bold;
          }
          
          &:last-child {
            border-bottom: none;
          }
        }
      }
    }
  }
}

@include respond-to('mobile') {
  .about-page {
    .mission-grid {
      grid-template-columns: 1fr;
      gap: 20px;
    }
  }
}
</style>
