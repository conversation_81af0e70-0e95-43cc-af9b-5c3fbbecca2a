package api

import (
	"chocolate-shop/internal/handlers"
	"chocolate-shop/internal/middleware"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/jmoiron/sqlx"
)

func SetupRoutes(router *gin.Engine, db *sqlx.DB, rdb *redis.Client) {
	// 创建处理器
	userHandler := handlers.NewUserHandler(db, rdb)
	productHandler := handlers.NewProductHandler(db, rdb)
	cartHandler := handlers.NewCartHandler(db, rdb)
	orderHandler := handlers.NewOrderHandler(db, rdb)

	// API v1 路由组
	v1 := router.Group("/api/v1")
	{
		// 公开路由
		auth := v1.Group("/auth")
		{
			auth.POST("/register", userHandler.Register)
			auth.POST("/login", userHandler.Login)
		}

		// 产品路由
		products := v1.Group("/products")
		{
			products.GET("", productHandler.GetProducts)
			products.GET("/:id", productHandler.GetProduct)
			products.GET("/category/:category", productHandler.GetProductsByCategory)
			products.GET("/search", productHandler.SearchProducts)
		}

		// 需要认证的路由
		protected := v1.Group("")
		protected.Use(middleware.AuthMiddleware(rdb))
		{
			// 用户路由
			users := protected.Group("/users")
			{
				users.GET("/profile", userHandler.GetProfile)
				users.PUT("/profile", userHandler.UpdateProfile)
			}

			// 购物车路由
			cart := protected.Group("/cart")
			{
				cart.GET("", cartHandler.GetCart)
				cart.POST("/add", cartHandler.AddToCart)
				cart.PUT("/update/:id", cartHandler.UpdateCartItem)
				cart.DELETE("/remove/:id", cartHandler.RemoveFromCart)
				cart.DELETE("/clear", cartHandler.ClearCart)
			}

			// 订单路由
			orders := protected.Group("/orders")
			{
				orders.GET("", orderHandler.GetOrders)
				orders.GET("/:id", orderHandler.GetOrder)
				orders.POST("", orderHandler.CreateOrder)
				orders.PUT("/:id/status", orderHandler.UpdateOrderStatus)
			}

			// 认证路由
			auth := protected.Group("/auth")
			{
				auth.POST("/logout", userHandler.Logout)
			}
		}
	}

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"message": "X Oberon Chocolate Shop API is running",
		})
	})
}
