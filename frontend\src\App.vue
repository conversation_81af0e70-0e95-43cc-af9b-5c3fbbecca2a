<template>
  <div id="app">
    <div class="collage sprinkles"></div>
    <Header />
    <main>
      <router-view />
    </main>
    <Footer />
    
    <!-- 全局通知 -->
    <Notification />
    
    <!-- 购物车侧边栏 -->
    <CartSidebar />
  </div>
</template>

<script>
import Header from '@/components/Layout/Header.vue'
import Footer from '@/components/Layout/Footer.vue'
import Notification from '@/components/Common/Notification.vue'
import CartSidebar from '@/components/Cart/CartSidebar.vue'

export default {
  name: 'App',
  components: {
    Header,
    Footer,
    Notification,
    CartSidebar
  },
  created() {
    // 初始化用户状态
    this.$store.dispatch('auth/initAuth')
    // 初始化购物车
    this.$store.dispatch('cart/loadCart')
  }
}
</script>

<style lang="scss">
#app {
  font-family: Inter, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Noto Sans", "Apple Color Emoji", "Segoe UI Emoji";
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--color-ink);
  background: var(--color-bg);
  min-height: 100vh;
  position: relative;
}

.collage.sprinkles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  background: 
    radial-gradient(5px 5px at 20% 30%, #000 98%, transparent 99%),
    radial-gradient(5px 5px at 40% 70%, #000 98%, transparent 99%),
    radial-gradient(5px 5px at 60% 40%, #000 98%, transparent 99%),
    radial-gradient(5px 5px at 80% 75%, #000 98%, transparent 99%),
    radial-gradient(5px 5px at 50% 10%, #000 98%, transparent 99%);
  opacity: .2;
  filter: blur(.2px);
}
</style>
