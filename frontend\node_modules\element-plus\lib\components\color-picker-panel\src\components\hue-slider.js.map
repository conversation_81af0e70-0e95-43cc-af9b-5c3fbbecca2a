{"version": 3, "file": "hue-slider.js", "sources": ["../../../../../../../packages/components/color-picker-panel/src/components/hue-slider.vue"], "sourcesContent": ["<template>\n  <div :class=\"[ns.b(), ns.is('vertical', vertical)]\">\n    <div ref=\"bar\" :class=\"ns.e('bar')\" @click=\"handleClick\" />\n    <div\n      ref=\"thumb\"\n      :class=\"ns.e('thumb')\"\n      :style=\"{\n        left: thumbLeft + 'px',\n        top: thumbTop + 'px',\n      }\"\n    />\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  onMounted,\n  ref,\n  watch,\n} from 'vue'\nimport { getClientXY } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { draggable } from '../utils/draggable'\n\nimport type { PropType } from 'vue'\nimport type Color from '../utils/color'\n\nexport default defineComponent({\n  name: 'ElColorHueSlider',\n\n  props: {\n    color: {\n      type: Object as PropType<Color>,\n      required: true,\n    },\n\n    vertical: <PERSON><PERSON><PERSON>,\n    disabled: <PERSON>ole<PERSON>,\n  },\n  setup(props) {\n    const ns = useNamespace('color-hue-slider')\n    const instance = getCurrentInstance()!\n\n    // ref\n    const thumb = ref<HTMLElement>()\n    const bar = ref<HTMLElement>()\n    // data\n    const thumbLeft = ref(0)\n    const thumbTop = ref(0)\n    // computed\n    const hueValue = computed(() => {\n      return props.color.get('hue')\n    })\n    // watch\n    watch(\n      () => hueValue.value,\n      () => {\n        update()\n      }\n    )\n\n    // methods\n    function handleClick(event: MouseEvent | TouchEvent) {\n      if (props.disabled) return\n      const target = event.target\n\n      if (target !== thumb.value) {\n        handleDrag(event)\n      }\n    }\n\n    function handleDrag(event: MouseEvent | TouchEvent) {\n      if (!bar.value || !thumb.value || props.disabled) return\n\n      const el = instance.vnode.el as HTMLElement\n      const rect = el.getBoundingClientRect()\n      const { clientX, clientY } = getClientXY(event)\n      let hue\n\n      if (!props.vertical) {\n        let left = clientX - rect.left\n        left = Math.min(left, rect.width - thumb.value.offsetWidth / 2)\n        left = Math.max(thumb.value.offsetWidth / 2, left)\n\n        hue = Math.round(\n          ((left - thumb.value.offsetWidth / 2) /\n            (rect.width - thumb.value.offsetWidth)) *\n            360\n        )\n      } else {\n        let top = clientY - rect.top\n\n        top = Math.min(top, rect.height - thumb.value.offsetHeight / 2)\n        top = Math.max(thumb.value.offsetHeight / 2, top)\n        hue = Math.round(\n          ((top - thumb.value.offsetHeight / 2) /\n            (rect.height - thumb.value.offsetHeight)) *\n            360\n        )\n      }\n      props.color.set('hue', hue)\n    }\n\n    function getThumbLeft() {\n      if (!thumb.value) return 0\n\n      const el = instance.vnode.el\n\n      if (props.vertical) return 0\n      const hue = props.color.get('hue')\n\n      if (!el) return 0\n      return Math.round(\n        (hue * (el.offsetWidth - thumb.value.offsetWidth / 2)) / 360\n      )\n    }\n\n    function getThumbTop() {\n      if (!thumb.value) return 0\n\n      const el = instance.vnode.el as HTMLElement\n      if (!props.vertical) return 0\n      const hue = props.color.get('hue')\n\n      if (!el) return 0\n      return Math.round(\n        (hue * (el.offsetHeight - thumb.value.offsetHeight / 2)) / 360\n      )\n    }\n\n    function update() {\n      thumbLeft.value = getThumbLeft()\n      thumbTop.value = getThumbTop()\n    }\n\n    // mounded\n    onMounted(() => {\n      if (!bar.value || !thumb.value || props.disabled) return\n\n      const dragConfig = {\n        drag: (event: MouseEvent | TouchEvent) => {\n          handleDrag(event)\n        },\n        end: (event: MouseEvent | TouchEvent) => {\n          handleDrag(event)\n        },\n      }\n\n      draggable(bar.value, dragConfig)\n      draggable(thumb.value, dragConfig)\n      update()\n    })\n\n    return {\n      bar,\n      thumb,\n      thumbLeft,\n      thumbTop,\n      hueValue,\n      handleClick,\n      update,\n      ns,\n    }\n  },\n})\n</script>\n"], "names": ["defineComponent", "useNamespace", "getCurrentInstance", "ref", "computed", "watch", "getClientXY", "onMounted", "draggable", "_createElementVNode", "_normalizeClass", "_normalizeStyle", "_export_sfc"], "mappings": ";;;;;;;;;;AA8BA,MAAK,YAAaA,mBAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,kBAAA;AAAA,EAEN,KAAO,EAAA;AAAA,IACL,KAAO,EAAA;AAAA,MACL,IAAM,EAAA,MAAA;AAAA,MACN,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,IAEA,QAAU,EAAA,OAAA;AAAA,IACV,QAAU,EAAA,OAAA;AAAA,GACZ;AAAA,EACA,MAAM,KAAO,EAAA;AACX,IAAM,MAAA,EAAA,GAAKC,mBAAa,kBAAkB,CAAA,CAAA;AAC1C,IAAA,MAAM,WAAWC,sBAAmB,EAAA,CAAA;AAGpC,IAAA,MAAM,QAAQC,OAAiB,EAAA,CAAA;AAC/B,IAAA,MAAM,MAAMA,OAAiB,EAAA,CAAA;AAE7B,IAAM,MAAA,SAAA,GAAYA,QAAI,CAAC,CAAA,CAAA;AACvB,IAAM,MAAA,QAAA,GAAWA,QAAI,CAAC,CAAA,CAAA;AAEtB,IAAM,MAAA,QAAA,GAAWC,aAAS,MAAM;AAC9B,MAAO,OAAA,KAAA,CAAM,KAAM,CAAA,GAAA,CAAI,KAAK,CAAA,CAAA;AAAA,KAC7B,CAAA,CAAA;AAED,IAAAC,SAAA,CAAA,MAAA,QAAA,CAAA,KAAA,EAAA,MAAA;AAAA,MACE,MAAM,EAAS,CAAA;AAAA,KAAA,CACf,CAAM;AACJ,IAAO,SAAA,WAAA,CAAA,KAAA,EAAA;AAAA,MACT,IAAA,KAAA,CAAA,QAAA;AAAA,QACF,OAAA;AAGA,MAAA,MAAA,cAAqD,CAAA,MAAA,CAAA;AACnD,MAAA,IAAI,MAAM,KAAU,KAAA,CAAA,KAAA,EAAA;AACpB,QAAA,gBAAqB,CAAA,CAAA;AAErB,OAAI;AACF,KAAA;AAAgB,IAClB,SAAA,UAAA,CAAA,KAAA,EAAA;AAAA,MACF,IAAA,CAAA,GAAA,CAAA,KAAA,IAAA,CAAA,KAAA,CAAA,KAAA,IAAA,KAAA,CAAA,QAAA;AAEA,QAAA;AACE,MAAA,MAAS,EAAA,GAAA,QAAU,CAAM,KAAA,CAAA,EAAA,CAAA;AAEzB,MAAM,MAAA,IAAA,2BAAoB,EAAA,CAAA;AAC1B,MAAM,MAAA,EAAA,SAAgC,OAAA,EAAA,GAAAC,oBAAA,CAAA,KAAA,CAAA,CAAA;AACtC,MAAA,IAAA,GAAQ,CAAA;AACR,MAAI,IAAA,CAAA,KAAA,CAAA,QAAA,EAAA;AAEJ,QAAI,WAAiB,OAAA,GAAA,IAAA,CAAA,IAAA,CAAA;AACnB,QAAI,IAAA,GAAA,IAAO,WAAe,IAAA,CAAA,KAAA,GAAA,KAAA,CAAA,KAAA,CAAA,WAAA,GAAA,CAAA,CAAA,CAAA;AAC1B,QAAO,IAAA,GAAA,IAAA,CAAK,IAAI,KAAM,CAAA,KAAK,YAAc,GAAA,CAAA,EAAA;AACzC,QAAA,GAAA,GAAA,UAAgB,CAAA,CAAA,IAAM,GAAM,KAAA,CAAA,KAAA,CAAA,WAAqB,GAAA,CAAA,KAAA,IAAA,CAAA,KAAA,GAAA,KAAA,CAAA,KAAA,CAAA,WAAA,CAAA,GAAA,GAAA,CAAA,CAAA;AAEjD,OAAA,MAAA;AAAW,QACP,IAAA,GAAA,GAAO,OAAY,GAAA,IAAA,CAAA,GAAA,CAAA;AAEnB,QACJ,GAAA,GAAA,IAAA,CAAA,GAAA,CAAA,GAAA,EAAA,IAAA,CAAA,MAAA,GAAA,KAAA,CAAA,KAAA,CAAA,YAAA,GAAA,CAAA,CAAA,CAAA;AAAA,QACK,GAAA,GAAA,IAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,CAAA,YAAA,GAAA,CAAA,EAAA,GAAA,CAAA,CAAA;AACL,QAAI,GAAA,GAAA,IAAM,WAAe,GAAA,KAAA,CAAA,KAAA,CAAA,YAAA,GAAA,CAAA,KAAA,IAAA,CAAA,MAAA,GAAA,KAAA,CAAA,KAAA,CAAA,YAAA,CAAA,GAAA,GAAA,CAAA,CAAA;AAEzB,OAAM;AACN,MAAA,KAAA,CAAA,SAAe,CAAA,KAAA,EAAM,GAAM,CAAA,CAAA;AAC3B,KAAA;AAAW,IACP,SAAA,YAAkB,GAAA;AAElB,MACJ,IAAA,CAAA,KAAA,CAAA,KAAA;AAAA,QACF,OAAA,CAAA,CAAA;AACA,MAAM,MAAA,EAAA,GAAA,QAAU,CAAA,KAAU,CAAA,EAAA,CAAA;AAAA,MAC5B,IAAA,KAAA,CAAA,QAAA;AAEA,QAAA,OAAwB,CAAA,CAAA;AACtB,MAAI,MAAO,GAAA,GAAA,KAAO,CAAO,KAAA,CAAA,GAAA,CAAA,KAAA,CAAA,CAAA;AAEzB,MAAM,IAAA,CAAA,EAAA;AAEN,QAAI,OAAA,CAAM;AACV,MAAA,OAAY,IAAA,CAAA,KAAA,CAAM,GAAM,IAAA,EAAA,CAAI,WAAK,GAAA,KAAA,CAAA,KAAA,CAAA,WAAA,GAAA,CAAA,CAAA,GAAA,GAAA,CAAA,CAAA;AAEjC,KAAI;AACJ,IAAA,SAAO,WAAK,GAAA;AAAA,MAAA,UACC,CAAA,KAAA;AAA8C,QAC3D,OAAA,CAAA,CAAA;AAAA,MACF,MAAA,EAAA,GAAA,QAAA,CAAA,KAAA,CAAA,EAAA,CAAA;AAEA,MAAA,IAAA,CAAA,KAAuB,CAAA,QAAA;AACrB,QAAI,OAAO,CAAA,CAAA;AAEX,MAAM,MAAA,GAAA,cAAoB,CAAA,GAAA,CAAA,KAAA,CAAA,CAAA;AAC1B,MAAI,IAAA,CAAC,EAAM;AACX,QAAA,OAAY,CAAA,CAAA;AAEZ,MAAI,WAAY,CAAA,KAAA,CAAA,GAAA,IAAA,EAAA,CAAA,YAAA,GAAA,KAAA,CAAA,KAAA,CAAA,YAAA,GAAA,CAAA,CAAA,GAAA,GAAA,CAAA,CAAA;AAChB,KAAA;AAAY,IAAA,eACC,GAAA;AAAgD,MAC7D,SAAA,CAAA,KAAA,GAAA,YAAA,EAAA,CAAA;AAAA,MACF,QAAA,CAAA,KAAA,GAAA,WAAA,EAAA,CAAA;AAEA,KAAA;AACE,IAAAC,aAAA,CAAA;AACA,MAAA,IAAA,CAAA,GAAA,CAAS,SAAoB,CAAA,KAAA,CAAA,KAAA,IAAA,KAAA,CAAA,QAAA;AAAA,QAC/B,OAAA;AAGA,MAAA,MAAA,UAAgB,GAAA;AACd,QAAA,IAAS,EAAA,CAAA,KAAA,KAAU;AAEnB,UAAA,UAAmB,CAAA,KAAA,CAAA,CAAA;AAAA,SACjB;AACE,QAAA,GAAA,EAAA,CAAA,KAAA,KAAgB;AAAA,UAClB,UAAA,CAAA,KAAA,CAAA,CAAA;AAAA,SACA;AACE,OAAA,CAAA;AAAgB,MAClBC,mBAAA,CAAA,GAAA,CAAA,KAAA,EAAA,UAAA,CAAA,CAAA;AAAA,MACFA,mBAAA,CAAA,KAAA,CAAA,KAAA,EAAA,UAAA,CAAA,CAAA;AAEA,MAAU,MAAA,EAAA,CAAA;AACV,KAAU,CAAA,CAAA;AACV,IAAO,OAAA;AAAA,MACR,GAAA;AAED,MAAO,KAAA;AAAA,MACL,SAAA;AAAA,MACA,QAAA;AAAA,MACA,QAAA;AAAA,MACA,WAAA;AAAA,MACA,MAAA;AAAA,MACA,EAAA;AAAA,KACA,CAAA;AAAA,GACA;AAAA,CACF,CAAA,CAAA;AAEJ,SAAC,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;;;AAtKC,GAAA,EAAA;AAAA,IAUMC,sBAAA,CAAA,KAAA,EAAA;AAAA,MAAA,GAAA,EAAA,KAAA;AAAA,MAVA,KAAA,EAAKC,uBAAM,CAAA,EAAA,CAAA,CAAA,CAAA,KAAQ,CAAA,CAAA;AAAuB,MAAA,OAAA,EAAA,IAAA,CAAA,WAAA;;0BACa,CAAA,KAAA,EAAA;AAAA,MAAA,GAAlD,EAAA,OAAA;AAAA,MAAO,KAAA,EAAAA,kBAAO,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAG,OAAC,CAAA,CAAA;AAAA,MAAA,KAAiB,EAAAC,kBAAA,CAAA;AAAA,QAAA,IAAA,EAAA,IAAA,CAAA,SAAA,GAAA,IAAA;AAC5C,QAAA,GAAA,EAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AAAA,OAOE,CAAA;AAAA,KAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AAAA,GAAA,EAAA,CAAA,CAAA,CAAA;AANI,CACH;AACK,gBAAA,gBAA2BC,iCAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,gBAAA,CAAA,CAAA,CAAA;;;;"}