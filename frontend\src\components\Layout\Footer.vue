<template>
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-brand">
          <span class="brand-mark">🍫</span>
          <span class="brand-text">X Oberon</span>
        </div>
        <div class="footer-links">
          <router-link to="/products">巧克力</router-link>
          <router-link to="/about">关于我们</router-link>
          <router-link to="/contact">联系我们</router-link>
        </div>
        <div class="footer-copyright">
          © 2025 X Oberon 巧克力商店
        </div>
      </div>
    </div>
  </footer>
</template>

<script>
export default {
  name: 'Footer'
}
</script>

<style lang="scss" scoped>
.footer {
  background: var(--color-ink);
  color: white;
  padding: 40px 0;
  margin-top: 80px;
  
  .footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 24px;
    
    .footer-brand {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 20px;
      font-weight: 700;
      
      .brand-mark {
        font-size: 24px;
      }
    }
    
    .footer-links {
      display: flex;
      gap: 24px;
      
      a {
        color: rgba(255,255,255,.7);
        text-decoration: none;
        transition: color .2s ease;
        
        &:hover {
          color: white;
        }
      }
    }
    
    .footer-copyright {
      color: rgba(255,255,255,.7);
      font-size: 14px;
    }
  }
}

@include respond-to('mobile') {
  .footer {
    .footer-content {
      flex-direction: column;
      text-align: center;
      gap: 16px;
      
      .footer-links {
        flex-direction: column;
        gap: 12px;
      }
    }
  }
}
</style>
