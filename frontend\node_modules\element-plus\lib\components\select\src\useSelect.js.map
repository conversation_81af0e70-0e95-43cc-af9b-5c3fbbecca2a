{"version": 3, "file": "useSelect.js", "sources": ["../../../../../../packages/components/select/src/useSelect.ts"], "sourcesContent": ["import {\n  Component,\n  computed,\n  nextTick,\n  onMounted,\n  reactive,\n  ref,\n  watch,\n  watchEffect,\n} from 'vue'\nimport {\n  findLastIndex,\n  get,\n  isEqual,\n  debounce as lodashDebounce,\n} from 'lodash-unified'\nimport { useResizeObserver } from '@vueuse/core'\nimport {\n  ValidateComponentsMap,\n  debugWarn,\n  ensureArray,\n  isArray,\n  isClient,\n  isFunction,\n  isIOS,\n  isNumber,\n  isObject,\n  isPlainObject,\n  isUndefined,\n  scrollIntoView,\n} from '@element-plus/utils'\nimport {\n  CHANGE_EVENT,\n  EVENT_CODE,\n  MINIMUM_INPUT_WIDTH,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport {\n  useComposition,\n  useEmptyValues,\n  useFocusController,\n  useId,\n  useLocale,\n  useNamespace,\n} from '@element-plus/hooks'\nimport {\n  useFormItem,\n  useFormItemInputId,\n  useFormSize,\n} from '@element-plus/components/form'\n\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\nimport type { ScrollbarInstance } from '@element-plus/components/scrollbar'\nimport type { SelectEmits, SelectProps } from './select'\nimport type {\n  OptionBasic,\n  OptionPublicInstance,\n  OptionValue,\n  SelectStates,\n} from './type'\n\nexport const useSelect = (props: SelectProps, emit: SelectEmits) => {\n  const { t } = useLocale()\n  const contentId = useId()\n  const nsSelect = useNamespace('select')\n  const nsInput = useNamespace('input')\n\n  const states = reactive<SelectStates>({\n    inputValue: '',\n    options: new Map(),\n    cachedOptions: new Map(),\n    optionValues: [], // sorted value of options\n    selected: [],\n    selectionWidth: 0,\n    collapseItemWidth: 0,\n    selectedLabel: '',\n    hoveringIndex: -1,\n    previousQuery: null,\n    inputHovering: false,\n    menuVisibleOnFocus: false,\n    isBeforeHide: false,\n  })\n\n  // template refs\n  const selectRef = ref<HTMLElement>()\n  const selectionRef = ref<HTMLElement>()\n  const tooltipRef = ref<TooltipInstance>()\n  const tagTooltipRef = ref<TooltipInstance>()\n  const inputRef = ref<HTMLInputElement>()\n  const prefixRef = ref<HTMLElement>()\n  const suffixRef = ref<HTMLElement>()\n  const menuRef = ref<HTMLElement>()\n  const tagMenuRef = ref<HTMLElement>()\n  const collapseItemRef = ref<HTMLElement>()\n  const scrollbarRef = ref<ScrollbarInstance>()\n  // the controller of the expanded popup\n  const expanded = ref(false)\n  const hoverOption = ref()\n\n  const { form, formItem } = useFormItem()\n  const { inputId } = useFormItemInputId(props, {\n    formItemContext: formItem,\n  })\n  const { valueOnClear, isEmptyValue } = useEmptyValues(props)\n\n  const {\n    isComposing,\n    handleCompositionStart,\n    handleCompositionUpdate,\n    handleCompositionEnd,\n  } = useComposition({\n    afterComposition: (e) => onInput(e),\n  })\n\n  const selectDisabled = computed(() => props.disabled || !!form?.disabled)\n\n  const { wrapperRef, isFocused, handleBlur } = useFocusController(inputRef, {\n    disabled: selectDisabled,\n    afterFocus() {\n      if (props.automaticDropdown && !expanded.value) {\n        expanded.value = true\n        states.menuVisibleOnFocus = true\n      }\n    },\n    beforeBlur(event) {\n      return (\n        tooltipRef.value?.isFocusInsideContent(event) ||\n        tagTooltipRef.value?.isFocusInsideContent(event)\n      )\n    },\n    afterBlur() {\n      expanded.value = false\n      states.menuVisibleOnFocus = false\n      if (props.validateEvent) {\n        formItem?.validate?.('blur').catch((err) => debugWarn(err))\n      }\n    },\n  })\n\n  const hasModelValue = computed(() => {\n    return isArray(props.modelValue)\n      ? props.modelValue.length > 0\n      : !isEmptyValue(props.modelValue)\n  })\n\n  const needStatusIcon = computed(() => form?.statusIcon ?? false)\n\n  const showClearBtn = computed(() => {\n    return (\n      props.clearable &&\n      !selectDisabled.value &&\n      hasModelValue.value &&\n      (isFocused.value || states.inputHovering)\n    )\n  })\n  const iconComponent = computed(() =>\n    props.remote && props.filterable && !props.remoteShowSuffix\n      ? ''\n      : props.suffixIcon\n  )\n  const iconReverse = computed(() =>\n    nsSelect.is('reverse', !!(iconComponent.value && expanded.value))\n  )\n\n  const validateState = computed(() => formItem?.validateState || '')\n  const validateIcon = computed(\n    () =>\n      validateState.value &&\n      (ValidateComponentsMap[validateState.value] as Component)\n  )\n\n  const debounce = computed(() => (props.remote ? 300 : 0))\n\n  const isRemoteSearchEmpty = computed(\n    () => props.remote && !states.inputValue && states.options.size === 0\n  )\n\n  const emptyText = computed(() => {\n    if (props.loading) {\n      return props.loadingText || t('el.select.loading')\n    } else {\n      if (\n        props.filterable &&\n        states.inputValue &&\n        states.options.size > 0 &&\n        filteredOptionsCount.value === 0\n      ) {\n        return props.noMatchText || t('el.select.noMatch')\n      }\n      if (states.options.size === 0) {\n        return props.noDataText || t('el.select.noData')\n      }\n    }\n    return null\n  })\n\n  const filteredOptionsCount = computed(\n    () => optionsArray.value.filter((option) => option.visible).length\n  )\n\n  const optionsArray = computed(() => {\n    const list = Array.from(states.options.values())\n    const newList: OptionPublicInstance[] = []\n    states.optionValues.forEach((item) => {\n      const index = list.findIndex((i) => i.value === item)\n      if (index > -1) {\n        newList.push(list[index])\n      }\n    })\n    return newList.length >= list.length ? newList : list\n  })\n\n  const cachedOptionsArray = computed(() =>\n    Array.from(states.cachedOptions.values())\n  )\n\n  const showNewOption = computed(() => {\n    const hasExistingOption = optionsArray.value\n      .filter((option) => {\n        return !option.created\n      })\n      .some((option) => {\n        return option.currentLabel === states.inputValue\n      })\n    return (\n      props.filterable &&\n      props.allowCreate &&\n      states.inputValue !== '' &&\n      !hasExistingOption\n    )\n  })\n\n  const updateOptions = () => {\n    if (props.filterable && isFunction(props.filterMethod)) return\n    if (props.filterable && props.remote && isFunction(props.remoteMethod))\n      return\n    optionsArray.value.forEach((option) => {\n      option.updateOption?.(states.inputValue)\n    })\n  }\n\n  const selectSize = useFormSize()\n\n  const collapseTagSize = computed(() =>\n    ['small'].includes(selectSize.value) ? 'small' : 'default'\n  )\n\n  const dropdownMenuVisible = computed({\n    get() {\n      return expanded.value && !isRemoteSearchEmpty.value\n    },\n    set(val: boolean) {\n      expanded.value = val\n    },\n  })\n\n  const shouldShowPlaceholder = computed(() => {\n    if (props.multiple && !isUndefined(props.modelValue)) {\n      return ensureArray(props.modelValue).length === 0 && !states.inputValue\n    }\n    const value = isArray(props.modelValue)\n      ? props.modelValue[0]\n      : props.modelValue\n    return props.filterable || isUndefined(value) ? !states.inputValue : true\n  })\n\n  const currentPlaceholder = computed(() => {\n    const _placeholder = props.placeholder ?? t('el.select.placeholder')\n    return props.multiple || !hasModelValue.value\n      ? _placeholder\n      : states.selectedLabel\n  })\n\n  // iOS Safari does not handle click events when a mouseenter event is registered and a DOM-change happens in a child\n  // We use a Vue custom event binding to only register the event on non-iOS devices\n  // ref.: https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/HandlingEvents/HandlingEvents.html\n  // Github Issue: https://github.com/vuejs/vue/issues/9859\n  const mouseEnterEventName = computed(() => (isIOS ? null : 'mouseenter'))\n\n  watch(\n    () => props.modelValue,\n    (val, oldVal) => {\n      if (props.multiple) {\n        if (props.filterable && !props.reserveKeyword) {\n          states.inputValue = ''\n          handleQueryChange('')\n        }\n      }\n      setSelected()\n      if (!isEqual(val, oldVal) && props.validateEvent) {\n        formItem?.validate('change').catch((err) => debugWarn(err))\n      }\n    },\n    {\n      flush: 'post',\n      deep: true,\n    }\n  )\n\n  watch(\n    () => expanded.value,\n    (val) => {\n      if (val) {\n        handleQueryChange(states.inputValue)\n      } else {\n        states.inputValue = ''\n        states.previousQuery = null\n        states.isBeforeHide = true\n      }\n      emit('visible-change', val)\n    }\n  )\n\n  watch(\n    // fix `Array.prototype.push/splice/..` cannot trigger non-deep watcher\n    // https://github.com/vuejs/vue-next/issues/2116\n    () => states.options.entries(),\n    () => {\n      if (!isClient) return\n      // tooltipRef.value?.updatePopper?.()\n      setSelected()\n      if (\n        props.defaultFirstOption &&\n        (props.filterable || props.remote) &&\n        filteredOptionsCount.value\n      ) {\n        checkDefaultFirstOption()\n      }\n    },\n    {\n      flush: 'post',\n    }\n  )\n\n  watch([() => states.hoveringIndex, optionsArray], ([val]) => {\n    if (isNumber(val) && val > -1) {\n      hoverOption.value = optionsArray.value[val] || {}\n    } else {\n      hoverOption.value = {}\n    }\n    optionsArray.value.forEach((option) => {\n      option.hover = hoverOption.value === option\n    })\n  })\n\n  watchEffect(() => {\n    // Anything could cause options changed, then update options\n    // If you want to control it by condition, write here\n    if (states.isBeforeHide) return\n    updateOptions()\n  })\n\n  const handleQueryChange = (val: string) => {\n    if (states.previousQuery === val || isComposing.value) {\n      return\n    }\n    states.previousQuery = val\n    if (props.filterable && isFunction(props.filterMethod)) {\n      props.filterMethod(val)\n    } else if (\n      props.filterable &&\n      props.remote &&\n      isFunction(props.remoteMethod)\n    ) {\n      props.remoteMethod(val)\n    }\n    if (\n      props.defaultFirstOption &&\n      (props.filterable || props.remote) &&\n      filteredOptionsCount.value\n    ) {\n      nextTick(checkDefaultFirstOption)\n    } else {\n      nextTick(updateHoveringIndex)\n    }\n  }\n\n  /**\n   * find and highlight first option as default selected\n   * @remark\n   * - if the first option in dropdown list is user-created,\n   *   it would be at the end of the optionsArray\n   *   so find it and set hover.\n   *   (NOTE: there must be only one user-created option in dropdown list with query)\n   * - if there's no user-created option in list, just find the first one as usual\n   *   (NOTE: exclude options that are disabled or in disabled-group)\n   */\n  const checkDefaultFirstOption = () => {\n    const optionsInDropdown = optionsArray.value.filter(\n      (n) => n.visible && !n.disabled && !n.states.groupDisabled\n    )\n    const userCreatedOption = optionsInDropdown.find((n) => n.created)\n    const firstOriginOption = optionsInDropdown[0]\n    const valueList = optionsArray.value.map((item) => item.value)\n    states.hoveringIndex = getValueIndex(\n      valueList,\n      userCreatedOption || firstOriginOption\n    )\n  }\n\n  const setSelected = () => {\n    if (!props.multiple) {\n      const value = isArray(props.modelValue)\n        ? props.modelValue[0]\n        : props.modelValue\n      const option = getOption(value)\n      states.selectedLabel = option.currentLabel\n      states.selected = [option]\n      return\n    } else {\n      states.selectedLabel = ''\n    }\n    const result: SelectStates['selected'] = []\n    if (!isUndefined(props.modelValue)) {\n      ensureArray(props.modelValue).forEach((value) => {\n        result.push(getOption(value))\n      })\n    }\n    states.selected = result\n  }\n\n  const getOption = (value: OptionValue) => {\n    let option\n    const isObjectValue = isPlainObject(value)\n\n    for (let i = states.cachedOptions.size - 1; i >= 0; i--) {\n      const cachedOption = cachedOptionsArray.value[i]\n      const isEqualValue = isObjectValue\n        ? get(cachedOption.value, props.valueKey) === get(value, props.valueKey)\n        : cachedOption.value === value\n      if (isEqualValue) {\n        option = {\n          value,\n          currentLabel: cachedOption.currentLabel,\n          get isDisabled() {\n            return cachedOption.isDisabled\n          },\n        }\n        break\n      }\n    }\n    if (option) return option\n    const label = isObjectValue ? value.label : value ?? ''\n    const newOption = {\n      value,\n      currentLabel: label,\n    }\n    return newOption\n  }\n\n  const updateHoveringIndex = () => {\n    states.hoveringIndex = optionsArray.value.findIndex((item) =>\n      states.selected.some(\n        (selected) => getValueKey(selected) === getValueKey(item)\n      )\n    )\n  }\n\n  const resetSelectionWidth = () => {\n    states.selectionWidth = Number.parseFloat(\n      window.getComputedStyle(selectionRef.value!).width\n    )\n  }\n\n  const resetCollapseItemWidth = () => {\n    states.collapseItemWidth =\n      collapseItemRef.value!.getBoundingClientRect().width\n  }\n\n  const updateTooltip = () => {\n    tooltipRef.value?.updatePopper?.()\n  }\n\n  const updateTagTooltip = () => {\n    tagTooltipRef.value?.updatePopper?.()\n  }\n\n  const onInputChange = () => {\n    if (states.inputValue.length > 0 && !expanded.value) {\n      expanded.value = true\n    }\n    handleQueryChange(states.inputValue)\n  }\n\n  const onInput = (event: Event) => {\n    states.inputValue = (event.target as HTMLInputElement).value\n    if (props.remote) {\n      debouncedOnInputChange()\n    } else {\n      return onInputChange()\n    }\n  }\n\n  const debouncedOnInputChange = lodashDebounce(() => {\n    onInputChange()\n  }, debounce.value)\n\n  const emitChange = (val: OptionValue | OptionValue[]) => {\n    if (!isEqual(props.modelValue, val)) {\n      emit(CHANGE_EVENT, val)\n    }\n  }\n\n  const getLastNotDisabledIndex = (value: OptionValue[]) =>\n    findLastIndex(value, (it) => {\n      const option = states.cachedOptions.get(it)\n      return option && !option.disabled && !option.states.groupDisabled\n    })\n\n  const deletePrevTag = (e: KeyboardEvent) => {\n    if (!props.multiple) return\n    if (e.code === EVENT_CODE.delete) return\n    if ((e.target as HTMLInputElement).value.length <= 0) {\n      const value = ensureArray(props.modelValue).slice()\n      const lastNotDisabledIndex = getLastNotDisabledIndex(value)\n      if (lastNotDisabledIndex < 0) return\n      const removeTagValue = value[lastNotDisabledIndex]\n      value.splice(lastNotDisabledIndex, 1)\n      emit(UPDATE_MODEL_EVENT, value)\n      emitChange(value)\n      emit('remove-tag', removeTagValue)\n    }\n  }\n\n  const deleteTag = (\n    event: MouseEvent,\n    tag: OptionPublicInstance | OptionBasic\n  ) => {\n    const index = states.selected.indexOf(tag)\n    if (index > -1 && !selectDisabled.value) {\n      const value = ensureArray(props.modelValue).slice()\n      value.splice(index, 1)\n      emit(UPDATE_MODEL_EVENT, value)\n      emitChange(value)\n      emit('remove-tag', tag.value)\n    }\n    event.stopPropagation()\n    focus()\n  }\n\n  const deleteSelected = (event: Event) => {\n    event.stopPropagation()\n    const value = props.multiple ? [] : valueOnClear.value\n    if (props.multiple) {\n      for (const item of states.selected) {\n        if (item.isDisabled) value.push(item.value)\n      }\n    }\n    emit(UPDATE_MODEL_EVENT, value)\n    emitChange(value)\n    states.hoveringIndex = -1\n    expanded.value = false\n    emit('clear')\n    focus()\n  }\n\n  const handleOptionSelect = (option: OptionPublicInstance) => {\n    if (props.multiple) {\n      const value = ensureArray(props.modelValue ?? []).slice()\n      const optionIndex = getValueIndex(value, option)\n      if (optionIndex > -1) {\n        value.splice(optionIndex, 1)\n      } else if (\n        props.multipleLimit <= 0 ||\n        value.length < props.multipleLimit\n      ) {\n        value.push(option.value)\n      }\n      emit(UPDATE_MODEL_EVENT, value)\n      emitChange(value)\n      if (option.created) {\n        handleQueryChange('')\n      }\n      if (props.filterable && !props.reserveKeyword) {\n        states.inputValue = ''\n      }\n    } else {\n      emit(UPDATE_MODEL_EVENT, option.value)\n      emitChange(option.value)\n      expanded.value = false\n    }\n    focus()\n    if (expanded.value) return\n    nextTick(() => {\n      scrollToOption(option)\n    })\n  }\n\n  const getValueIndex = (arr: OptionValue[], option: OptionPublicInstance) => {\n    if (isUndefined(option)) return -1\n    if (!isObject(option.value)) return arr.indexOf(option.value)\n\n    return arr.findIndex((item) => {\n      return isEqual(get(item, props.valueKey), getValueKey(option))\n    })\n  }\n\n  const scrollToOption = (\n    option:\n      | OptionPublicInstance\n      | OptionPublicInstance[]\n      | SelectStates['selected']\n  ) => {\n    const targetOption = isArray(option) ? option[0] : option\n    let target = null\n\n    if (targetOption?.value) {\n      const options = optionsArray.value.filter(\n        (item) => item.value === targetOption.value\n      )\n      if (options.length > 0) {\n        target = options[0].$el\n      }\n    }\n\n    if (tooltipRef.value && target) {\n      const menu = tooltipRef.value?.popperRef?.contentRef?.querySelector?.(\n        `.${nsSelect.be('dropdown', 'wrap')}`\n      )\n      if (menu) {\n        scrollIntoView(menu as HTMLElement, target)\n      }\n    }\n    scrollbarRef.value?.handleScroll()\n  }\n\n  const onOptionCreate = (vm: OptionPublicInstance) => {\n    states.options.set(vm.value, vm)\n    states.cachedOptions.set(vm.value, vm)\n  }\n\n  const onOptionDestroy = (key: OptionValue, vm: OptionPublicInstance) => {\n    if (states.options.get(key) === vm) {\n      states.options.delete(key)\n    }\n  }\n\n  const popperRef = computed(() => {\n    return tooltipRef.value?.popperRef?.contentRef\n  })\n\n  const handleMenuEnter = () => {\n    states.isBeforeHide = false\n    nextTick(() => {\n      scrollbarRef.value?.update()\n      scrollToOption(states.selected)\n    })\n  }\n\n  const focus = () => {\n    inputRef.value?.focus()\n  }\n\n  const blur = () => {\n    if (expanded.value) {\n      expanded.value = false\n      nextTick(() => inputRef.value?.blur())\n      return\n    }\n    inputRef.value?.blur()\n  }\n\n  const handleClearClick = (event: Event) => {\n    deleteSelected(event)\n  }\n\n  const handleClickOutside = (event: Event) => {\n    expanded.value = false\n\n    if (isFocused.value) {\n      const _event = new FocusEvent('blur', event)\n      nextTick(() => handleBlur(_event))\n    }\n  }\n\n  const handleEsc = () => {\n    if (states.inputValue.length > 0) {\n      states.inputValue = ''\n    } else {\n      expanded.value = false\n    }\n  }\n\n  const toggleMenu = () => {\n    if (selectDisabled.value) return\n\n    // We only set the inputHovering state to true on mouseenter event on iOS devices\n    // To keep the state updated we set it here to true\n    if (isIOS) states.inputHovering = true\n\n    if (states.menuVisibleOnFocus) {\n      // controlled by automaticDropdown\n      states.menuVisibleOnFocus = false\n    } else {\n      expanded.value = !expanded.value\n    }\n  }\n\n  const selectOption = () => {\n    if (!expanded.value) {\n      toggleMenu()\n    } else {\n      const option = optionsArray.value[states.hoveringIndex]\n      if (option && !option.isDisabled) {\n        handleOptionSelect(option)\n      }\n    }\n  }\n\n  const getValueKey = (\n    item: OptionPublicInstance | SelectStates['selected'][0]\n  ) => {\n    return isObject(item.value) ? get(item.value, props.valueKey) : item.value\n  }\n\n  const optionsAllDisabled = computed(() =>\n    optionsArray.value\n      .filter((option) => option.visible)\n      .every((option) => option.isDisabled)\n  )\n\n  const showTagList = computed(() => {\n    if (!props.multiple) {\n      return []\n    }\n    return props.collapseTags\n      ? states.selected.slice(0, props.maxCollapseTags)\n      : states.selected\n  })\n\n  const collapseTagList = computed(() => {\n    if (!props.multiple) {\n      return []\n    }\n    return props.collapseTags\n      ? states.selected.slice(props.maxCollapseTags)\n      : []\n  })\n\n  const navigateOptions = (direction: 'prev' | 'next') => {\n    if (!expanded.value) {\n      expanded.value = true\n      return\n    }\n    if (\n      states.options.size === 0 ||\n      filteredOptionsCount.value === 0 ||\n      isComposing.value\n    )\n      return\n\n    if (!optionsAllDisabled.value) {\n      if (direction === 'next') {\n        states.hoveringIndex++\n        if (states.hoveringIndex === states.options.size) {\n          states.hoveringIndex = 0\n        }\n      } else if (direction === 'prev') {\n        states.hoveringIndex--\n        if (states.hoveringIndex < 0) {\n          states.hoveringIndex = states.options.size - 1\n        }\n      }\n      const option = optionsArray.value[states.hoveringIndex]\n      if (option.isDisabled || !option.visible) {\n        navigateOptions(direction)\n      }\n      nextTick(() => scrollToOption(hoverOption.value))\n    }\n  }\n\n  const getGapWidth = () => {\n    if (!selectionRef.value) return 0\n    const style = window.getComputedStyle(selectionRef.value)\n    return Number.parseFloat(style.gap || '6px')\n  }\n\n  // computed style\n  const tagStyle = computed(() => {\n    const gapWidth = getGapWidth()\n    const inputSlotWidth = props.filterable ? gapWidth + MINIMUM_INPUT_WIDTH : 0\n    const maxWidth =\n      collapseItemRef.value && props.maxCollapseTags === 1\n        ? states.selectionWidth -\n          states.collapseItemWidth -\n          gapWidth -\n          inputSlotWidth\n        : states.selectionWidth - inputSlotWidth\n    return { maxWidth: `${maxWidth}px` }\n  })\n\n  const collapseTagStyle = computed(() => {\n    return { maxWidth: `${states.selectionWidth}px` }\n  })\n\n  const popupScroll = (data: { scrollTop: number; scrollLeft: number }) => {\n    emit('popup-scroll', data)\n  }\n\n  useResizeObserver(selectionRef, resetSelectionWidth)\n  useResizeObserver(wrapperRef, updateTooltip)\n  useResizeObserver(tagMenuRef, updateTagTooltip)\n  useResizeObserver(collapseItemRef, resetCollapseItemWidth)\n\n  // #21498\n  let stop: (() => void) | undefined\n  watch(\n    () => dropdownMenuVisible.value,\n    (newVal) => {\n      if (newVal) {\n        stop = useResizeObserver(menuRef, updateTooltip).stop\n      } else {\n        stop?.()\n        stop = undefined\n      }\n    }\n  )\n\n  onMounted(() => {\n    setSelected()\n  })\n\n  return {\n    inputId,\n    contentId,\n    nsSelect,\n    nsInput,\n    states,\n    isFocused,\n    expanded,\n    optionsArray,\n    hoverOption,\n    selectSize,\n    filteredOptionsCount,\n    updateTooltip,\n    updateTagTooltip,\n    debouncedOnInputChange,\n    onInput,\n    deletePrevTag,\n    deleteTag,\n    deleteSelected,\n    handleOptionSelect,\n    scrollToOption,\n    hasModelValue,\n    shouldShowPlaceholder,\n    currentPlaceholder,\n    mouseEnterEventName,\n    needStatusIcon,\n    showClearBtn,\n    iconComponent,\n    iconReverse,\n    validateState,\n    validateIcon,\n    showNewOption,\n    updateOptions,\n    collapseTagSize,\n    setSelected,\n    selectDisabled,\n    emptyText,\n    handleCompositionStart,\n    handleCompositionUpdate,\n    handleCompositionEnd,\n    onOptionCreate,\n    onOptionDestroy,\n    handleMenuEnter,\n    focus,\n    blur,\n    handleClearClick,\n    handleClickOutside,\n    handleEsc,\n    toggleMenu,\n    selectOption,\n    getValueKey,\n    navigateOptions,\n    dropdownMenuVisible,\n    showTagList,\n    collapseTagList,\n    popupScroll,\n\n    // computed style\n    tagStyle,\n    collapseTagStyle,\n\n    // DOM ref\n    popperRef,\n    inputRef,\n    tooltipRef,\n    tagTooltipRef,\n    prefixRef,\n    suffixRef,\n    selectRef,\n    wrapperRef,\n    selectionRef,\n    scrollbarRef,\n    menuRef,\n    tagMenuRef,\n    collapseItemRef,\n  }\n}\n"], "names": ["useLocale", "useId", "useNamespace", "reactive", "ref", "form", "useFormItem", "useFormItemInputId", "useEmptyValues", "useComposition", "computed", "useFocusController", "debugWarn", "isArray", "ValidateComponentsMap", "isFunction", "useFormSize", "isUndefined", "ensureArray", "isIOS", "watch", "isEqual", "isClient", "isNumber", "watchEffect", "nextTick", "isPlainObject", "get", "lodashDebounce", "CHANGE_EVENT", "findLastIndex", "EVENT_CODE", "UPDATE_MODEL_EVENT", "event", "isObject", "scrollIntoView", "MINIMUM_INPUT_WIDTH", "useResizeObserver", "onMounted"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAiDY,MAAC,SAAS,GAAG,CAAC,KAAK,EAAE,IAAI,KAAK;AAC1C,EAAE,MAAM,EAAE,CAAC,EAAE,GAAGA,eAAS,EAAE,CAAC;AAC5B,EAAE,MAAM,SAAS,GAAGC,aAAK,EAAE,CAAC;AAC5B,EAAE,MAAM,QAAQ,GAAGC,oBAAY,CAAC,QAAQ,CAAC,CAAC;AAC1C,EAAE,MAAM,OAAO,GAAGA,oBAAY,CAAC,OAAO,CAAC,CAAC;AACxC,EAAE,MAAM,MAAM,GAAGC,YAAQ,CAAC;AAC1B,IAAI,UAAU,EAAE,EAAE;AAClB,IAAI,OAAO,kBAAkB,IAAI,GAAG,EAAE;AACtC,IAAI,aAAa,kBAAkB,IAAI,GAAG,EAAE;AAC5C,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,QAAQ,EAAE,EAAE;AAChB,IAAI,cAAc,EAAE,CAAC;AACrB,IAAI,iBAAiB,EAAE,CAAC;AACxB,IAAI,aAAa,EAAE,EAAE;AACrB,IAAI,aAAa,EAAE,CAAC,CAAC;AACrB,IAAI,aAAa,EAAE,IAAI;AACvB,IAAI,aAAa,EAAE,KAAK;AACxB,IAAI,kBAAkB,EAAE,KAAK;AAC7B,IAAI,YAAY,EAAE,KAAK;AACvB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,SAAS,GAAGC,OAAG,EAAE,CAAC;AAC1B,EAAE,MAAM,YAAY,GAAGA,OAAG,EAAE,CAAC;AAC7B,EAAE,MAAM,UAAU,GAAGA,OAAG,EAAE,CAAC;AAC3B,EAAE,MAAM,aAAa,GAAGA,OAAG,EAAE,CAAC;AAC9B,EAAE,MAAM,QAAQ,GAAGA,OAAG,EAAE,CAAC;AACzB,EAAE,MAAM,SAAS,GAAGA,OAAG,EAAE,CAAC;AAC1B,EAAE,MAAM,SAAS,GAAGA,OAAG,EAAE,CAAC;AAC1B,EAAE,MAAM,OAAO,GAAGA,OAAG,EAAE,CAAC;AACxB,EAAE,MAAM,UAAU,GAAGA,OAAG,EAAE,CAAC;AAC3B,EAAE,MAAM,eAAe,GAAGA,OAAG,EAAE,CAAC;AAChC,EAAE,MAAM,YAAY,GAAGA,OAAG,EAAE,CAAC;AAC7B,EAAE,MAAM,QAAQ,GAAGA,OAAG,CAAC,KAAK,CAAC,CAAC;AAC9B,EAAE,MAAM,WAAW,GAAGA,OAAG,EAAE,CAAC;AAC5B,EAAE,MAAM,QAAEC,MAAI,EAAE,QAAQ,EAAE,GAAGC,uBAAW,EAAE,CAAC;AAC3C,EAAE,MAAM,EAAE,OAAO,EAAE,GAAGC,8BAAkB,CAAC,KAAK,EAAE;AAChD,IAAI,eAAe,EAAE,QAAQ;AAC7B,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,GAAGC,sBAAc,CAAC,KAAK,CAAC,CAAC;AAC/D,EAAE,MAAM;AACR,IAAI,WAAW;AACf,IAAI,sBAAsB;AAC1B,IAAI,uBAAuB;AAC3B,IAAI,oBAAoB;AACxB,GAAG,GAAGC,sBAAc,CAAC;AACrB,IAAI,gBAAgB,EAAE,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC;AACvC,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,cAAc,GAAGC,YAAQ,CAAC,MAAM,KAAK,CAAC,QAAQ,IAAI,CAAC,EAAEL,MAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;AACrG,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,GAAGM,0BAAkB,CAAC,QAAQ,EAAE;AAC7E,IAAI,QAAQ,EAAE,cAAc;AAC5B,IAAI,UAAU,GAAG;AACjB,MAAM,IAAI,KAAK,CAAC,iBAAiB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;AACtD,QAAQ,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC;AAC9B,QAAQ,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC;AACzC,OAAO;AACP,KAAK;AACL,IAAI,UAAU,CAAC,KAAK,EAAE;AACtB,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC;AACjB,MAAM,OAAO,CAAC,CAAC,EAAE,GAAG,UAAU,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,oBAAoB,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,GAAG,aAAa,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;AAC3K,KAAK;AACL,IAAI,SAAS,GAAG;AAChB,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7B,MAAM,MAAM,CAAC,kBAAkB,GAAG,KAAK,CAAC;AACxC,MAAM,IAAI,KAAK,CAAC,aAAa,EAAE;AAC/B,QAAQ,CAAC,EAAE,GAAG,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAKC,eAAS,CAAI,CAAC,CAAC,CAAC;AACzI,OAAO;AACP,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,aAAa,GAAGF,YAAQ,CAAC,MAAM;AACvC,IAAI,OAAOG,cAAO,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AACrG,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,cAAc,GAAGH,YAAQ,CAAC,MAAM;AACxC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,CAAC,EAAE,GAAGL,MAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAI,CAAC,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC;AAC/E,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,YAAY,GAAGK,YAAQ,CAAC,MAAM;AACtC,IAAI,OAAO,KAAK,CAAC,SAAS,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,aAAa,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK,IAAI,MAAM,CAAC,aAAa,CAAC,CAAC;AACxH,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,aAAa,GAAGA,YAAQ,CAAC,MAAM,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;AAC5H,EAAE,MAAM,WAAW,GAAGA,YAAQ,CAAC,MAAM,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,EAAE,aAAa,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACxG,EAAE,MAAM,aAAa,GAAGA,YAAQ,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,aAAa,KAAK,EAAE,CAAC,CAAC;AACnG,EAAE,MAAM,YAAY,GAAGA,YAAQ,CAAC,MAAM,aAAa,CAAC,KAAK,IAAII,0BAAqB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;AACzG,EAAE,MAAM,QAAQ,GAAGJ,YAAQ,CAAC,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;AAC1D,EAAE,MAAM,mBAAmB,GAAGA,YAAQ,CAAC,MAAM,KAAK,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC;AAC9G,EAAE,MAAM,SAAS,GAAGA,YAAQ,CAAC,MAAM;AACnC,IAAI,IAAI,KAAK,CAAC,OAAO,EAAE;AACvB,MAAM,OAAO,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,mBAAmB,CAAC,CAAC;AACzD,KAAK,MAAM;AACX,MAAM,IAAI,KAAK,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,IAAI,oBAAoB,CAAC,KAAK,KAAK,CAAC,EAAE;AAChH,QAAQ,OAAO,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,mBAAmB,CAAC,CAAC;AAC3D,OAAO;AACP,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,EAAE;AACrC,QAAQ,OAAO,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,kBAAkB,CAAC,CAAC;AACzD,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,oBAAoB,GAAGA,YAAQ,CAAC,MAAM,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC;AAC5G,EAAE,MAAM,YAAY,GAAGA,YAAQ,CAAC,MAAM;AACtC,IAAI,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;AACrD,IAAI,MAAM,OAAO,GAAG,EAAE,CAAC;AACvB,IAAI,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAC1C,MAAM,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC;AAC5D,MAAM,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACtB,QAAQ,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAClC,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC;AAC1D,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,kBAAkB,GAAGA,YAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AACvF,EAAE,MAAM,aAAa,GAAGA,YAAQ,CAAC,MAAM;AACvC,IAAI,MAAM,iBAAiB,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK;AACpE,MAAM,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC;AAC7B,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK;AACxB,MAAM,OAAO,MAAM,CAAC,YAAY,KAAK,MAAM,CAAC,UAAU,CAAC;AACvD,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,WAAW,IAAI,MAAM,CAAC,UAAU,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC;AACnG,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,aAAa,GAAG,MAAM;AAC9B,IAAI,IAAI,KAAK,CAAC,UAAU,IAAIK,iBAAU,CAAC,KAAK,CAAC,YAAY,CAAC;AAC1D,MAAM,OAAO;AACb,IAAI,IAAI,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM,IAAIA,iBAAU,CAAC,KAAK,CAAC,YAAY,CAAC;AAC1E,MAAM,OAAO;AACb,IAAI,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AAC3C,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;AACvF,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAGC,8BAAW,EAAE,CAAC;AACnC,EAAE,MAAM,eAAe,GAAGN,YAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS,CAAC,CAAC;AACrG,EAAE,MAAM,mBAAmB,GAAGA,YAAQ,CAAC;AACvC,IAAI,GAAG,GAAG;AACV,MAAM,OAAO,QAAQ,CAAC,KAAK,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;AAC1D,KAAK;AACL,IAAI,GAAG,CAAC,GAAG,EAAE;AACb,MAAM,QAAQ,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3B,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,qBAAqB,GAAGA,YAAQ,CAAC,MAAM;AAC/C,IAAI,IAAI,KAAK,CAAC,QAAQ,IAAI,CAACO,iBAAW,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;AAC1D,MAAM,OAAOC,uBAAW,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;AAC9E,KAAK;AACL,IAAI,MAAM,KAAK,GAAGL,cAAO,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC;AACrF,IAAI,OAAO,KAAK,CAAC,UAAU,IAAII,iBAAW,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;AAC9E,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,kBAAkB,GAAGP,YAAQ,CAAC,MAAM;AAC5C,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,YAAY,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,WAAW,KAAK,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC,uBAAuB,CAAC,CAAC;AAC5F,IAAI,OAAO,KAAK,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,YAAY,GAAG,MAAM,CAAC,aAAa,CAAC;AACxF,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,mBAAmB,GAAGA,YAAQ,CAAC,MAAMS,UAAK,GAAG,IAAI,GAAG,YAAY,CAAC,CAAC;AAC1E,EAAEC,SAAK,CAAC,MAAM,KAAK,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK;AACjD,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;AACxB,MAAM,IAAI,KAAK,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;AACrD,QAAQ,MAAM,CAAC,UAAU,GAAG,EAAE,CAAC;AAC/B,QAAQ,iBAAiB,CAAC,EAAE,CAAC,CAAC;AAC9B,OAAO;AACP,KAAK;AACL,IAAI,WAAW,EAAE,CAAC;AAClB,IAAI,IAAI,CAACC,qBAAO,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,KAAK,CAAC,aAAa,EAAE;AACtD,MAAM,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAKT,eAAS,CAAI,CAAC,CAAC,CAAC;AAC7F,KAAK;AACL,GAAG,EAAE;AACL,IAAI,KAAK,EAAE,MAAM;AACjB,IAAI,IAAI,EAAE,IAAI;AACd,GAAG,CAAC,CAAC;AACL,EAAEQ,SAAK,CAAC,MAAM,QAAQ,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK;AACvC,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,iBAAiB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AAC3C,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,UAAU,GAAG,EAAE,CAAC;AAC7B,MAAM,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC;AAClC,MAAM,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC;AACjC,KAAK;AACL,IAAI,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;AAChC,GAAG,CAAC,CAAC;AACL,EAAEA,SAAK,CAAC,MAAM,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,MAAM;AAC9C,IAAI,IAAI,CAACE,aAAQ;AACjB,MAAM,OAAO;AACb,IAAI,WAAW,EAAE,CAAC;AAClB,IAAI,IAAI,KAAK,CAAC,kBAAkB,KAAK,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,oBAAoB,CAAC,KAAK,EAAE;AACtG,MAAM,uBAAuB,EAAE,CAAC;AAChC,KAAK;AACL,GAAG,EAAE;AACL,IAAI,KAAK,EAAE,MAAM;AACjB,GAAG,CAAC,CAAC;AACL,EAAEF,SAAK,CAAC,CAAC,MAAM,MAAM,CAAC,aAAa,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK;AAC/D,IAAI,IAAIG,cAAQ,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE;AACnC,MAAM,WAAW,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;AACxD,KAAK,MAAM;AACX,MAAM,WAAW,CAAC,KAAK,GAAG,EAAE,CAAC;AAC7B,KAAK;AACL,IAAI,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AAC3C,MAAM,MAAM,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,KAAK,MAAM,CAAC;AAClD,KAAK,CAAC,CAAC;AACP,GAAG,CAAC,CAAC;AACL,EAAEC,eAAW,CAAC,MAAM;AACpB,IAAI,IAAI,MAAM,CAAC,YAAY;AAC3B,MAAM,OAAO;AACb,IAAI,aAAa,EAAE,CAAC;AACpB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,iBAAiB,GAAG,CAAC,GAAG,KAAK;AACrC,IAAI,IAAI,MAAM,CAAC,aAAa,KAAK,GAAG,IAAI,WAAW,CAAC,KAAK,EAAE;AAC3D,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,CAAC,aAAa,GAAG,GAAG,CAAC;AAC/B,IAAI,IAAI,KAAK,CAAC,UAAU,IAAIT,iBAAU,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;AAC5D,MAAM,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AAC9B,KAAK,MAAM,IAAI,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM,IAAIA,iBAAU,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;AACnF,MAAM,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AAC9B,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,kBAAkB,KAAK,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,oBAAoB,CAAC,KAAK,EAAE;AACtG,MAAMU,YAAQ,CAAC,uBAAuB,CAAC,CAAC;AACxC,KAAK,MAAM;AACX,MAAMA,YAAQ,CAAC,mBAAmB,CAAC,CAAC;AACpC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,uBAAuB,GAAG,MAAM;AACxC,IAAI,MAAM,iBAAiB,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AACpH,IAAI,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC;AACvE,IAAI,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;AACnD,IAAI,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC;AACnE,IAAI,MAAM,CAAC,aAAa,GAAG,aAAa,CAAC,SAAS,EAAE,iBAAiB,IAAI,iBAAiB,CAAC,CAAC;AAC5F,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,MAAM;AAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;AACzB,MAAM,MAAM,KAAK,GAAGZ,cAAO,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC;AACvF,MAAM,MAAM,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;AACtC,MAAM,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;AACjD,MAAM,MAAM,CAAC,QAAQ,GAAG,CAAC,MAAM,CAAC,CAAC;AACjC,MAAM,OAAO;AACb,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,aAAa,GAAG,EAAE,CAAC;AAChC,KAAK;AACL,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC;AACtB,IAAI,IAAI,CAACI,iBAAW,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;AACxC,MAAMC,uBAAW,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AACvD,QAAQ,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;AACtC,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC;AAC7B,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,CAAC,KAAK,KAAK;AAC/B,IAAI,IAAI,MAAM,CAAC;AACf,IAAI,MAAM,aAAa,GAAGQ,oBAAa,CAAC,KAAK,CAAC,CAAC;AAC/C,IAAI,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,aAAa,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAC7D,MAAM,MAAM,YAAY,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACvD,MAAM,MAAM,YAAY,GAAG,aAAa,GAAGC,iBAAG,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAKA,iBAAG,CAAC,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,GAAG,YAAY,CAAC,KAAK,KAAK,KAAK,CAAC;AACjJ,MAAM,IAAI,YAAY,EAAE;AACxB,QAAQ,MAAM,GAAG;AACjB,UAAU,KAAK;AACf,UAAU,YAAY,EAAE,YAAY,CAAC,YAAY;AACjD,UAAU,IAAI,UAAU,GAAG;AAC3B,YAAY,OAAO,YAAY,CAAC,UAAU,CAAC;AAC3C,WAAW;AACX,SAAS,CAAC;AACV,QAAQ,MAAM;AACd,OAAO;AACP,KAAK;AACL,IAAI,IAAI,MAAM;AACd,MAAM,OAAO,MAAM,CAAC;AACpB,IAAI,MAAM,KAAK,GAAG,aAAa,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,EAAE,CAAC;AAC3E,IAAI,MAAM,SAAS,GAAG;AACtB,MAAM,KAAK;AACX,MAAM,YAAY,EAAE,KAAK;AACzB,KAAK,CAAC;AACN,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG,CAAC;AACJ,EAAE,MAAM,mBAAmB,GAAG,MAAM;AACpC,IAAI,MAAM,CAAC,aAAa,GAAG,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,QAAQ,KAAK,WAAW,CAAC,QAAQ,CAAC,KAAK,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACnJ,GAAG,CAAC;AACJ,EAAE,MAAM,mBAAmB,GAAG,MAAM;AACpC,IAAI,MAAM,CAAC,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;AACjG,GAAG,CAAC;AACJ,EAAE,MAAM,sBAAsB,GAAG,MAAM;AACvC,IAAI,MAAM,CAAC,iBAAiB,GAAG,eAAe,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,KAAK,CAAC;AACnF,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,MAAM;AAC9B,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,UAAU,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACrG,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,MAAM;AACjC,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,aAAa,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxG,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,MAAM;AAC9B,IAAI,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;AACzD,MAAM,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC;AAC5B,KAAK;AACL,IAAI,iBAAiB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AACzC,GAAG,CAAC;AACJ,EAAE,MAAM,OAAO,GAAG,CAAC,KAAK,KAAK;AAC7B,IAAI,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;AAC3C,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE;AACtB,MAAM,sBAAsB,EAAE,CAAC;AAC/B,KAAK,MAAM;AACX,MAAM,OAAO,aAAa,EAAE,CAAC;AAC7B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,sBAAsB,GAAGC,sBAAc,CAAC,MAAM;AACtD,IAAI,aAAa,EAAE,CAAC;AACpB,GAAG,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;AACrB,EAAE,MAAM,UAAU,GAAG,CAAC,GAAG,KAAK;AAC9B,IAAI,IAAI,CAACP,qBAAO,CAAC,KAAK,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE;AACzC,MAAM,IAAI,CAACQ,kBAAY,EAAE,GAAG,CAAC,CAAC;AAC9B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,uBAAuB,GAAG,CAAC,KAAK,KAAKC,2BAAa,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK;AAC1E,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAChD,IAAI,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;AACtE,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,aAAa,GAAG,CAAC,CAAC,KAAK;AAC/B,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ;AACvB,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,CAAC,IAAI,KAAKC,eAAU,CAAC,MAAM;AACpC,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;AACpC,MAAM,MAAM,KAAK,GAAGb,uBAAW,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,KAAK,EAAE,CAAC;AAC1D,MAAM,MAAM,oBAAoB,GAAG,uBAAuB,CAAC,KAAK,CAAC,CAAC;AAClE,MAAM,IAAI,oBAAoB,GAAG,CAAC;AAClC,QAAQ,OAAO;AACf,MAAM,MAAM,cAAc,GAAG,KAAK,CAAC,oBAAoB,CAAC,CAAC;AACzD,MAAM,KAAK,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;AAC5C,MAAM,IAAI,CAACc,wBAAkB,EAAE,KAAK,CAAC,CAAC;AACtC,MAAM,UAAU,CAAC,KAAK,CAAC,CAAC;AACxB,MAAM,IAAI,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;AACzC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,CAACC,OAAK,EAAE,GAAG,KAAK;AACpC,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC/C,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;AAC7C,MAAM,MAAM,KAAK,GAAGf,uBAAW,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,KAAK,EAAE,CAAC;AAC1D,MAAM,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC7B,MAAM,IAAI,CAACc,wBAAkB,EAAE,KAAK,CAAC,CAAC;AACtC,MAAM,UAAU,CAAC,KAAK,CAAC,CAAC;AACxB,MAAM,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;AACpC,KAAK;AACL,IAAIC,OAAK,CAAC,eAAe,EAAE,CAAC;AAC5B,IAAI,KAAK,EAAE,CAAC;AACZ,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAACA,OAAK,KAAK;AACpC,IAAIA,OAAK,CAAC,eAAe,EAAE,CAAC;AAC5B,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,QAAQ,GAAG,EAAE,GAAG,YAAY,CAAC,KAAK,CAAC;AAC3D,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;AACxB,MAAM,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,QAAQ,EAAE;AAC1C,QAAQ,IAAI,IAAI,CAAC,UAAU;AAC3B,UAAU,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACjC,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAACD,wBAAkB,EAAE,KAAK,CAAC,CAAC;AACpC,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;AACtB,IAAI,MAAM,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;AAC9B,IAAI,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;AAClB,IAAI,KAAK,EAAE,CAAC;AACZ,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,CAAC,MAAM,KAAK;AACzC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;AACxB,MAAM,MAAM,KAAK,GAAGd,uBAAW,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;AACnF,MAAM,MAAM,WAAW,GAAG,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACvD,MAAM,IAAI,WAAW,GAAG,CAAC,CAAC,EAAE;AAC5B,QAAQ,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;AACrC,OAAO,MAAM,IAAI,KAAK,CAAC,aAAa,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,aAAa,EAAE;AACjF,QAAQ,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACjC,OAAO;AACP,MAAM,IAAI,CAACc,wBAAkB,EAAE,KAAK,CAAC,CAAC;AACtC,MAAM,UAAU,CAAC,KAAK,CAAC,CAAC;AACxB,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;AAC1B,QAAQ,iBAAiB,CAAC,EAAE,CAAC,CAAC;AAC9B,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;AACrD,QAAQ,MAAM,CAAC,UAAU,GAAG,EAAE,CAAC;AAC/B,OAAO;AACP,KAAK,MAAM;AACX,MAAM,IAAI,CAACA,wBAAkB,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;AAC7C,MAAM,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC/B,MAAM,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7B,KAAK;AACL,IAAI,KAAK,EAAE,CAAC;AACZ,IAAI,IAAI,QAAQ,CAAC,KAAK;AACtB,MAAM,OAAO;AACb,IAAIP,YAAQ,CAAC,MAAM;AACnB,MAAM,cAAc,CAAC,MAAM,CAAC,CAAC;AAC7B,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,CAAC,GAAG,EAAE,MAAM,KAAK;AACzC,IAAI,IAAIR,iBAAW,CAAC,MAAM,CAAC;AAC3B,MAAM,OAAO,CAAC,CAAC,CAAC;AAChB,IAAI,IAAI,CAACiB,eAAQ,CAAC,MAAM,CAAC,KAAK,CAAC;AAC/B,MAAM,OAAO,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACvC,IAAI,OAAO,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK;AACnC,MAAM,OAAOb,qBAAO,CAACM,iBAAG,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;AACrE,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAAC,MAAM,KAAK;AACrC,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC3B,IAAI,MAAM,YAAY,GAAGd,cAAO,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;AAC9D,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC;AACtB,IAAI,IAAI,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,YAAY,CAAC,KAAK,EAAE;AAC5D,MAAM,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,KAAK,YAAY,CAAC,KAAK,CAAC,CAAC;AAC7F,MAAM,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AAC9B,QAAQ,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAChC,OAAO;AACP,KAAK;AACL,IAAI,IAAI,UAAU,CAAC,KAAK,IAAI,MAAM,EAAE;AACpC,MAAM,MAAM,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,UAAU,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3O,MAAM,IAAI,IAAI,EAAE;AAChB,QAAQsB,qBAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACrC,OAAO;AACP,KAAK;AACL,IAAI,CAAC,EAAE,GAAG,YAAY,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,EAAE,CAAC;AACnE,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAAC,EAAE,KAAK;AACjC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACrC,IAAI,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,eAAe,GAAG,CAAC,GAAG,EAAE,EAAE,KAAK;AACvC,IAAI,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE;AACxC,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACjC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAGzB,YAAQ,CAAC,MAAM;AACnC,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,OAAO,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,UAAU,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC;AAC3G,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,eAAe,GAAG,MAAM;AAChC,IAAI,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC;AAChC,IAAIe,YAAQ,CAAC,MAAM;AACnB,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,CAAC,EAAE,GAAG,YAAY,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;AAC/D,MAAM,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACtC,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,MAAM,KAAK,GAAG,MAAM;AACtB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;AACxD,GAAG,CAAC;AACJ,EAAE,MAAM,IAAI,GAAG,MAAM;AACrB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE;AACxB,MAAM,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7B,MAAMA,YAAQ,CAAC,MAAM;AACrB,QAAQ,IAAI,GAAG,CAAC;AAChB,QAAQ,OAAO,CAAC,GAAG,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;AACpE,OAAO,CAAC,CAAC;AACT,MAAM,OAAO;AACb,KAAK;AACL,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;AACvD,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,CAAC,KAAK,KAAK;AACtC,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC;AAC1B,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,CAAC,KAAK,KAAK;AACxC,IAAI,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,IAAI,IAAI,SAAS,CAAC,KAAK,EAAE;AACzB,MAAM,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACnD,MAAMA,YAAQ,CAAC,MAAM,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;AACzC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,MAAM;AAC1B,IAAI,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AACtC,MAAM,MAAM,CAAC,UAAU,GAAG,EAAE,CAAC;AAC7B,KAAK,MAAM;AACX,MAAM,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,MAAM;AAC3B,IAAI,IAAI,cAAc,CAAC,KAAK;AAC5B,MAAM,OAAO;AACb,IAAI,IAAIN,UAAK;AACb,MAAM,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC;AAClC,IAAI,IAAI,MAAM,CAAC,kBAAkB,EAAE;AACnC,MAAM,MAAM,CAAC,kBAAkB,GAAG,KAAK,CAAC;AACxC,KAAK,MAAM;AACX,MAAM,QAAQ,CAAC,KAAK,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;AACvC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;AACzB,MAAM,UAAU,EAAE,CAAC;AACnB,KAAK,MAAM;AACX,MAAM,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AAC9D,MAAM,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;AACxC,QAAQ,kBAAkB,CAAC,MAAM,CAAC,CAAC;AACnC,OAAO;AACP,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,IAAI,KAAK;AAChC,IAAI,OAAOe,eAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAGP,iBAAG,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;AAC/E,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAGjB,YAAQ,CAAC,MAAM,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;AACxI,EAAE,MAAM,WAAW,GAAGA,YAAQ,CAAC,MAAM;AACrC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;AACzB,MAAM,OAAO,EAAE,CAAC;AAChB,KAAK;AACL,IAAI,OAAO,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC;AAClG,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,eAAe,GAAGA,YAAQ,CAAC,MAAM;AACzC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;AACzB,MAAM,OAAO,EAAE,CAAC;AAChB,KAAK;AACL,IAAI,OAAO,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC;AAClF,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,eAAe,GAAG,CAAC,SAAS,KAAK;AACzC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;AACzB,MAAM,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC;AAC5B,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,oBAAoB,CAAC,KAAK,KAAK,CAAC,IAAI,WAAW,CAAC,KAAK;AAC1F,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE;AACnC,MAAM,IAAI,SAAS,KAAK,MAAM,EAAE;AAChC,QAAQ,MAAM,CAAC,aAAa,EAAE,CAAC;AAC/B,QAAQ,IAAI,MAAM,CAAC,aAAa,KAAK,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE;AAC1D,UAAU,MAAM,CAAC,aAAa,GAAG,CAAC,CAAC;AACnC,SAAS;AACT,OAAO,MAAM,IAAI,SAAS,KAAK,MAAM,EAAE;AACvC,QAAQ,MAAM,CAAC,aAAa,EAAE,CAAC;AAC/B,QAAQ,IAAI,MAAM,CAAC,aAAa,GAAG,CAAC,EAAE;AACtC,UAAU,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC;AACzD,SAAS;AACT,OAAO;AACP,MAAM,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AAC9D,MAAM,IAAI,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;AAChD,QAAQ,eAAe,CAAC,SAAS,CAAC,CAAC;AACnC,OAAO;AACP,MAAMe,YAAQ,CAAC,MAAM,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;AACxD,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,MAAM;AAC5B,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK;AAC3B,MAAM,OAAO,CAAC,CAAC;AACf,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC9D,IAAI,OAAO,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC;AACjD,GAAG,CAAC;AACJ,EAAE,MAAM,QAAQ,GAAGf,YAAQ,CAAC,MAAM;AAClC,IAAI,MAAM,QAAQ,GAAG,WAAW,EAAE,CAAC;AACnC,IAAI,MAAM,cAAc,GAAG,KAAK,CAAC,UAAU,GAAG,QAAQ,GAAG0B,wBAAmB,GAAG,CAAC,CAAC;AACjF,IAAI,MAAM,QAAQ,GAAG,eAAe,CAAC,KAAK,IAAI,KAAK,CAAC,eAAe,KAAK,CAAC,GAAG,MAAM,CAAC,cAAc,GAAG,MAAM,CAAC,iBAAiB,GAAG,QAAQ,GAAG,cAAc,GAAG,MAAM,CAAC,cAAc,GAAG,cAAc,CAAC;AAClM,IAAI,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;AACzC,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,gBAAgB,GAAG1B,YAAQ,CAAC,MAAM;AAC1C,IAAI,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC,EAAE,CAAC;AACtD,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,WAAW,GAAG,CAAC,IAAI,KAAK;AAChC,IAAI,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AAC/B,GAAG,CAAC;AACJ,EAAE2B,sBAAiB,CAAC,YAAY,EAAE,mBAAmB,CAAC,CAAC;AACvD,EAAEA,sBAAiB,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;AAC/C,EAAEA,sBAAiB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;AAClD,EAAEA,sBAAiB,CAAC,eAAe,EAAE,sBAAsB,CAAC,CAAC;AAC7D,EAAE,IAAI,IAAI,CAAC;AACX,EAAEjB,SAAK,CAAC,MAAM,mBAAmB,CAAC,KAAK,EAAE,CAAC,MAAM,KAAK;AACrD,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,IAAI,GAAGiB,sBAAiB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC;AAC5D,KAAK,MAAM;AACX,MAAM,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,EAAE,CAAC;AACrC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC;AACpB,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAEC,aAAS,CAAC,MAAM;AAClB,IAAI,WAAW,EAAE,CAAC;AAClB,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,OAAO;AACX,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,MAAM;AACV,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,oBAAoB;AACxB,IAAI,aAAa;AACjB,IAAI,gBAAgB;AACpB,IAAI,sBAAsB;AAC1B,IAAI,OAAO;AACX,IAAI,aAAa;AACjB,IAAI,SAAS;AACb,IAAI,cAAc;AAClB,IAAI,kBAAkB;AACtB,IAAI,cAAc;AAClB,IAAI,aAAa;AACjB,IAAI,qBAAqB;AACzB,IAAI,kBAAkB;AACtB,IAAI,mBAAmB;AACvB,IAAI,cAAc;AAClB,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,WAAW;AACf,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,aAAa;AACjB,IAAI,eAAe;AACnB,IAAI,WAAW;AACf,IAAI,cAAc;AAClB,IAAI,SAAS;AACb,IAAI,sBAAsB;AAC1B,IAAI,uBAAuB;AAC3B,IAAI,oBAAoB;AACxB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAI,KAAK;AACT,IAAI,IAAI;AACR,IAAI,gBAAgB;AACpB,IAAI,kBAAkB;AACtB,IAAI,SAAS;AACb,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,eAAe;AACnB,IAAI,mBAAmB;AACvB,IAAI,WAAW;AACf,IAAI,eAAe;AACnB,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,gBAAgB;AACpB,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,UAAU;AACd,IAAI,aAAa;AACjB,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,OAAO;AACX,IAAI,UAAU;AACd,IAAI,eAAe;AACnB,GAAG,CAAC;AACJ;;;;"}