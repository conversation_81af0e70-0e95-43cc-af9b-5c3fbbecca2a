{"version": 3, "file": "splitter2.js", "sources": ["../../../../../../packages/components/splitter/src/splitter.vue"], "sourcesContent": ["<script setup lang=\"ts\">\nimport {\n  computed,\n  getCurrentInstance,\n  provide,\n  reactive,\n  toRef,\n  watch,\n} from 'vue'\nimport { useNamespace, useOrderedChildren } from '@element-plus/hooks'\nimport { useContainer, useResize, useSize } from './hooks'\nimport { splitterProps } from './splitter'\nimport { type PanelItemState, splitterRootContextKey } from './type'\n\nconst ns = useNamespace('splitter')\n\ndefineOptions({\n  name: 'ElSplitter',\n})\n\nconst emits = defineEmits<{\n  (e: 'resizeStart', index: number, sizes: number[]): void\n  (e: 'resize', index: number, sizes: number[]): void\n  (e: 'resizeEnd', index: number, sizes: number[]): void\n  (e: 'collapse', index: number, type: 'start' | 'end', sizes: number[]): void\n}>()\n\nconst props = defineProps(splitterProps)\nconst layout = toRef(props, 'layout')\nconst lazy = toRef(props, 'lazy')\n\nconst { containerEl, containerSize } = useContainer(layout)\n\nconst {\n  removeChild: unregisterPanel,\n  children: panels,\n  addChild: registerPanel,\n  ChildrenSorter: PanelsSorter,\n} = useOrderedChildren<PanelItemState>(getCurrentInstance()!, 'ElSplitterPanel')\n\nwatch(panels, () => {\n  panels.value.forEach((instance: PanelItemState, index: number) => {\n    instance.setIndex(index)\n  })\n})\n\nconst { percentSizes, pxSizes } = useSize(panels, containerSize)\n\nconst {\n  lazyOffset,\n  movingIndex,\n  onMoveStart,\n  onMoving,\n  onMoveEnd,\n  onCollapse,\n} = useResize(panels, containerSize, pxSizes, lazy)\n\nconst splitterStyles = computed(() => {\n  return {\n    [`--${ns.b()}-bar-offset`]: lazy.value\n      ? `${lazyOffset.value}px`\n      : undefined,\n  }\n})\n\nconst onResizeStart = (index: number) => {\n  onMoveStart(index)\n  emits('resizeStart', index, pxSizes.value)\n}\n\nconst onResize = (index: number, offset: number) => {\n  onMoving(index, offset)\n\n  if (!lazy.value) {\n    emits('resize', index, pxSizes.value)\n  }\n}\n\nconst onResizeEnd = (index: number) => {\n  onMoveEnd()\n  emits('resizeEnd', index, pxSizes.value)\n}\n\nconst onCollapsible = (index: number, type: 'start' | 'end') => {\n  onCollapse(index, type)\n  emits('collapse', index, type, pxSizes.value)\n}\n\nprovide(\n  splitterRootContextKey,\n  reactive({\n    panels,\n    percentSizes,\n    pxSizes,\n    layout,\n    lazy,\n    movingIndex,\n    containerSize,\n    onMoveStart: onResizeStart,\n    onMoving: onResize,\n    onMoveEnd: onResizeEnd,\n    onCollapse: onCollapsible,\n    registerPanel,\n    unregisterPanel,\n  })\n)\n</script>\n\n<template>\n  <div\n    ref=\"containerEl\"\n    :class=\"[ns.b(), ns.e(layout)]\"\n    :style=\"splitterStyles\"\n  >\n    <slot />\n    <panels-sorter />\n    <!-- Prevent iframe touch events from breaking -->\n    <div v-if=\"movingIndex\" :class=\"[ns.e('mask'), ns.e(`mask-${layout}`)]\" />\n  </div>\n</template>\n"], "names": ["useNamespace", "toRef", "useContainer", "useOrderedChildren", "getCurrentInstance", "watch", "useSize", "useResize", "computed", "provide", "splitterRootContextKey", "reactive", "_openBlock", "_createElementBlock"], "mappings": ";;;;;;;;;;;;;;uCAgBc,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAJA,IAAM,MAAA,EAAA,GAAKA,mBAAa,UAAU,CAAA,CAAA;AAclC,IAAM,MAAA,MAAA,GAASC,SAAM,CAAA,KAAA,EAAO,QAAQ,CAAA,CAAA;AACpC,IAAM,MAAA,IAAA,GAAOA,SAAM,CAAA,KAAA,EAAO,MAAM,CAAA,CAAA;AAEhC,IAAA,MAAM,EAAE,WAAA,EAAa,aAAc,EAAA,GAAIC,0BAAa,MAAM,CAAA,CAAA;AAE1D,IAAM,MAAA;AAAA,MACJ,WAAa,EAAA,eAAA;AAAA,MACb,QAAU,EAAA,MAAA;AAAA,MACV,QAAU,EAAA,aAAA;AAAA,MACV,cAAgB,EAAA,YAAA;AAAA,KACd,GAAAC,0BAAA,CAAmCC,sBAAmB,EAAA,EAAI,iBAAiB,CAAA,CAAA;AAE/E,IAAAC,SAAA,CAAM,QAAQ,MAAM;AAClB,MAAA,MAAA,CAAO,KAAM,CAAA,OAAA,CAAQ,CAAC,QAAA,EAA0B,KAAkB,KAAA;AAChE,QAAA,QAAA,CAAS,SAAS,KAAK,CAAA,CAAA;AAAA,OACxB,CAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAED,IAAA,MAAM,EAAE,YAAc,EAAA,OAAA,EAAY,GAAAC,eAAA,CAAQ,QAAQ,aAAa,CAAA,CAAA;AAE/D,IAAM,MAAA;AAAA,MACJ,UAAA;AAAA,MACA,WAAA;AAAA,MACA,WAAA;AAAA,MACA,QAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA;AAAA,KACE,GAAAC,mBAAA,CAAU,MAAQ,EAAA,aAAA,EAAe,SAAS,IAAI,CAAA,CAAA;AAElD,IAAM,MAAA,cAAA,GAAiBC,aAAS,MAAM;AACpC,MAAO,OAAA;AAAA,QACL,CAAC,CAAA,EAAA,EAAK,EAAG,CAAA,CAAA,EAAG,CAAA,WAAA,CAAa,GAAG,IAAA,CAAK,KAC7B,GAAA,CAAA,EAAG,UAAW,CAAA,KAAK,CACnB,EAAA,CAAA,GAAA,KAAA,CAAA;AAAA,OACN,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,aAAA,GAAgB,CAAC,KAAkB,KAAA;AACvC,MAAA,WAAA,CAAY,KAAK,CAAA,CAAA;AACjB,MAAM,KAAA,CAAA,aAAA,EAAe,KAAO,EAAA,OAAA,CAAQ,KAAK,CAAA,CAAA;AAAA,KAC3C,CAAA;AAEA,IAAM,MAAA,QAAA,GAAW,CAAC,KAAA,EAAe,MAAmB,KAAA;AAClD,MAAA,QAAA,CAAS,OAAO,MAAM,CAAA,CAAA;AAEtB,MAAI,IAAA,CAAC,KAAK,KAAO,EAAA;AACf,QAAM,KAAA,CAAA,QAAA,EAAU,KAAO,EAAA,OAAA,CAAQ,KAAK,CAAA,CAAA;AAAA,OACtC;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,WAAA,GAAc,CAAC,KAAkB,KAAA;AACrC,MAAU,SAAA,EAAA,CAAA;AACV,MAAM,KAAA,CAAA,WAAA,EAAa,KAAO,EAAA,OAAA,CAAQ,KAAK,CAAA,CAAA;AAAA,KACzC,CAAA;AAEA,IAAM,MAAA,aAAA,GAAgB,CAAC,KAAA,EAAe,IAA0B,KAAA;AAC9D,MAAA,UAAA,CAAW,OAAO,IAAI,CAAA,CAAA;AACtB,MAAA,KAAA,CAAM,UAAY,EAAA,KAAA,EAAO,IAAM,EAAA,OAAA,CAAQ,KAAK,CAAA,CAAA;AAAA,KAC9C,CAAA;AAEA,IAAAC,WAAA,CAAAC,2BAAA,EAAAC,YAAA,CAAA;AAAA,MACE,MAAA;AAAA,MACA,YAAS;AAAA,MACP,OAAA;AAAA,MACA,MAAA;AAAA,MACA,IAAA;AAAA,MACA,WAAA;AAAA,MACA,aAAA;AAAA,MACA,WAAA,EAAA,aAAA;AAAA,MACA,QAAA,EAAA,QAAA;AAAA,MAAA,SACa,EAAA,WAAA;AAAA,MAAA,UACH,EAAA,aAAA;AAAA,MAAA,aACC;AAAA,MAAA,eACC;AAAA,KACZ,CAAA,CAAA,CAAA;AAAA,IACA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACF,OAACC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,QACH,OAAA,EAAA,aAAA;;;;;;;;;;;;;;;;;;;;"}