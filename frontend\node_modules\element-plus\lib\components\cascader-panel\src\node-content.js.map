{"version": 3, "file": "node-content.js", "sources": ["../../../../../../packages/components/cascader-panel/src/node-content.tsx"], "sourcesContent": ["import { Comment, defineComponent, inject } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { isArray } from '@element-plus/utils'\nimport { CASCADER_PANEL_INJECTION_KEY } from './types'\n\nimport type { PropType, VNode } from 'vue'\nimport type { CascaderNode } from './types'\n\nfunction isVNodeEmpty(vnodes?: VNode[] | VNode) {\n  return !!(isArray(vnodes)\n    ? vnodes.every(({ type }) => type === Comment)\n    : vnodes?.type === Comment)\n}\n\nexport default defineComponent({\n  name: 'NodeContent',\n  props: {\n    node: {\n      type: Object as PropType<CascaderNode>,\n      required: true,\n    },\n  },\n  setup(props) {\n    const ns = useNamespace('cascader-node')\n    const { renderLabelFn } = inject(CASCADER_PANEL_INJECTION_KEY)!\n    const { node } = props\n    const { data, label: nodeLabel } = node\n\n    const label = () => {\n      const renderLabel = renderLabelFn?.({ node, data })\n      return isVNodeEmpty(renderLabel) ? nodeLabel : renderLabel ?? nodeLabel\n    }\n    return () => <span class={ns.e('label')}>{label()}</span>\n  },\n})\n"], "names": ["isVNodeEmpty", "vnodes", "type", "defineComponent", "name", "props", "node", "required", "useNamespace", "inject", "renderLabelFn", "label", "nodeLabel", "renderLabel", "data"], "mappings": ";;;;;;;;;;AAQA,EAASA,OAAAA,CAAAA,EAAAA,cAAAA,CAAAA,MAAaC,CAAAA,GAA0B,MAAA,CAAA,KAAA,CAAA,CAAA;IACvC,IAAA;AACaC,GAAAA,KAAAA,IAAAA,KAAAA,WAAAA,CAAAA,GAAAA,CAAAA,MAAAA,IAAAA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,MAAAA,CAAAA,IAAAA,MAAAA,WAAAA,CAAAA,CAAAA;;AAErB,kBAAAC,mBAAA,CAAA;;AAED,EAAA,KAAA,EAAA;AACEC,IAAAA,IAAM,EADuB;AAE7BC,MAAAA,IAAO,EAAA,MAAA;AACLC,MAAAA,QAAM,EAAA,IAAA;AACJJ,KAAAA;AACAK,GAAAA;AAFI,EAAA,KAAA,CAAA,KAAA,EAAA;IAHqB,MAAA,EAAA,GAAAC,kBAAA,CAAA,eAAA,CAAA,CAAA;;MAQxB,aAAQ;AACX,KAAA,GAAAC,UAAQ,CAAGD,kCAAY,CAAvB,CAAA;IACA,MAAM;AAAEE,MAAAA,IAAAA;KAAkBD,GAAAA,KAAAA,CAAM;IAChC,MAAM;AAAEH,MAAAA,IAAAA;AAAF,MAAA,KAAN,EAAA,SAAA;KACM,GAAA,IAAA,CAAA;UAAA,KAAA,GAAA,MAAA;AAAQK,MAAAA,MAAOC,WAAAA,GAAAA,aAAAA,IAAAA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,aAAAA,CAAAA;AAAf,QAA6BN,IAAnC;;OAEMK,CAAAA,CAAAA;MACJ,OAAME,YAAcH,CAAAA,WAAAA,CAAAA,GAAgB,SAAA,GAAA,WAAA,IAAA,IAAA,GAAA,WAAA,GAAA,SAAA,CAAA;;AAAQI,IAAAA,OAAAA,MAAAA,eAAAA,CAAAA,MAAAA,EAAAA;AAAR,MAAA,OAApC,EAAA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA;OACOd,CAAAA,KAAAA,EAAAA,CAAAA,CAAAA,CAAAA;;;;;;"}