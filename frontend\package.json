{"name": "x-oberon-chocolate-frontend", "version": "1.0.0", "description": "X Oberon 巧克力商店前端", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "dev": "npm run serve"}, "dependencies": {"vue": "^3.3.0", "vue-router": "^4.2.0", "vuex": "^4.1.0", "axios": "^1.5.0", "element-plus": "^2.4.0", "@element-plus/icons-vue": "^2.1.0", "socket.io-client": "^4.7.0", "js-cookie": "^3.0.5"}, "devDependencies": {"@vue/cli-plugin-eslint": "^5.0.0", "@vue/cli-plugin-router": "^5.0.0", "@vue/cli-plugin-vuex": "^5.0.0", "@vue/cli-service": "^5.0.0", "@vue/eslint-config-standard": "^8.0.0", "eslint": "^8.0.0", "eslint-plugin-import": "^2.25.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-vue": "^9.0.0", "sass": "^1.69.0", "sass-loader": "^13.3.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}