{"version": 3, "file": "instance.js", "sources": ["../../../../../../packages/components/message/src/instance.ts"], "sourcesContent": ["import { shallowReactive } from 'vue'\n\nimport type { ComponentInternalInstance, VNode } from 'vue'\nimport type { Mutable } from '@element-plus/utils'\nimport type { MessageHandler, MessagePlacement, MessageProps } from './message'\n\nexport type MessageContext = {\n  id: string\n  vnode: VNode\n  handler: MessageHandler\n  vm: ComponentInternalInstance\n  props: Mutable<MessageProps>\n}\n\nexport const placementInstances = shallowReactive(\n  {} as Record<MessagePlacement, MessageContext[]>\n)\n\nexport const getOrCreatePlacementInstances = (placement: MessagePlacement) => {\n  if (!placementInstances[placement]) {\n    placementInstances[placement] = shallowReactive([])\n  }\n  return placementInstances[placement]\n}\n\nexport const getInstance = (id: string, placement: MessagePlacement) => {\n  const instances = placementInstances[placement] || []\n  const idx = instances.findIndex((instance) => instance.id === id)\n  const current = instances[idx]\n  let prev: MessageContext | undefined\n  if (idx > 0) {\n    prev = instances[idx - 1]\n  }\n  return { current, prev }\n}\n\nexport const getLastOffset = (\n  id: string,\n  placement: MessagePlacement\n): number => {\n  const { prev } = getInstance(id, placement)\n  if (!prev) return 0\n  return prev.vm.exposed!.bottom.value\n}\n\nexport const getOffsetOrSpace = (\n  id: string,\n  offset: number,\n  placement: MessagePlacement\n) => {\n  const instances = placementInstances[placement] || []\n  const idx = instances.findIndex((instance) => instance.id === id)\n  return idx > 0 ? 16 : offset\n}\n"], "names": ["shallowReactive"], "mappings": ";;;;;;AACY,MAAC,kBAAkB,GAAGA,mBAAe,CAAC,EAAE,EAAE;AAC1C,MAAC,6BAA6B,GAAG,CAAC,SAAS,KAAK;AAC5D,EAAE,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE;AACtC,IAAI,kBAAkB,CAAC,SAAS,CAAC,GAAGA,mBAAe,CAAC,EAAE,CAAC,CAAC;AACxD,GAAG;AACH,EAAE,OAAO,kBAAkB,CAAC,SAAS,CAAC,CAAC;AACvC,EAAE;AACU,MAAC,WAAW,GAAG,CAAC,EAAE,EAAE,SAAS,KAAK;AAC9C,EAAE,MAAM,SAAS,GAAG,kBAAkB,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;AACxD,EAAE,MAAM,GAAG,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;AACpE,EAAE,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;AACjC,EAAE,IAAI,IAAI,CAAC;AACX,EAAE,IAAI,GAAG,GAAG,CAAC,EAAE;AACf,IAAI,IAAI,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAC9B,GAAG;AACH,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAC3B,EAAE;AACU,MAAC,aAAa,GAAG,CAAC,EAAE,EAAE,SAAS,KAAK;AAChD,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,WAAW,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;AAC9C,EAAE,IAAI,CAAC,IAAI;AACX,IAAI,OAAO,CAAC,CAAC;AACb,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;AACtC,EAAE;AACU,MAAC,gBAAgB,GAAG,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,KAAK;AAC3D,EAAE,MAAM,SAAS,GAAG,kBAAkB,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;AACxD,EAAE,MAAM,GAAG,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;AACpE,EAAE,OAAO,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC;AAC/B;;;;;;;;"}