@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

$directions: rtl, ltr, ttb, btt;

@include b(drawer) {
  @include set-component-css-var('drawer', $drawer);
}

@include b(drawer) {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: getCssVar('drawer', 'bg-color');
  display: flex;
  flex-direction: column;
  box-shadow: getCssVar('box-shadow', 'dark');
  overflow: hidden;

  transition: all getCssVar('transition-duration');

  @each $direction in $directions {
    .#{$direction} {
      transform: translate(0, 0);
    }
  }

  &__sr-focus:focus {
    outline: none !important;
  }

  &__header {
    align-items: center;
    color: getCssVar('text-color', 'primary');
    display: flex;
    margin-bottom: 32px;
    padding: getCssVar('drawer-padding-primary');
    padding-bottom: 0;
    & > :first-child {
      flex: 1;
    }
  }

  &__title {
    margin: 0;
    flex: 1;
    line-height: inherit;
    font-size: 16px;
  }

  @include e(footer) {
    padding: getCssVar('drawer-padding-primary');
    padding-top: 10px;
    text-align: right;
  }

  &__close-btn {
    display: inline-flex;
    border: none;
    cursor: pointer;
    font-size: getCssVar('font-size-extra-large');
    color: inherit;
    background-color: transparent;
    outline: none;
    &:focus,
    &:hover {
      i {
        color: getCssVar('color-primary');
      }
    }
  }

  &__body {
    flex: 1;
    padding: getCssVar('drawer-padding-primary');
    overflow: auto;
    & > * {
      box-sizing: border-box;
    }
  }
}

.#{$namespace}-drawer-splitter {
  $bar: #{$namespace}-splitter-bar;

  > .#{$bar} {
    .#{$bar}__disable {
      display: none;
    }

    .#{$bar}__dragger {
      &-horizontal::before {
        width: 1px;
      }

      &-vertical::before {
        height: 1px;
      }

      &:not(&-active, &:hover)::before {
        background-color: transparent;
      }
    }
  }
}

.#{$namespace}-drawer-fade {
  &-enter-active,
  &-leave-active {
    transition: all getCssVar('transition-duration');

    .#{$namespace}-splitter-bar__dragger {
      display: none;
    }
  }

  &-enter-from,
  &-enter-active,
  &-enter-to,
  &-leave-from,
  &-leave-active,
  &-leave-to {
    overflow: hidden !important;
  }

  &-enter-from,
  &-leave-to {
    background-color: transparent !important;
  }

  &-enter-from,
  &-leave-to {
    @each $direction in $directions {
      .#{$direction} {
        @if $direction == ltr {
          transform: translateX(-100%);
        }

        @if $direction == rtl {
          transform: translateX(100%);
        }

        @if $direction == ttb {
          transform: translateY(-100%);
        }

        @if $direction == btt {
          transform: translateY(100%);
        }
      }
    }
  }
}
