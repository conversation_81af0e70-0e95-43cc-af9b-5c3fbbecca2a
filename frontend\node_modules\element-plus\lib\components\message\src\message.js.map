{"version": 3, "file": "message.js", "sources": ["../../../../../../packages/components/message/src/message.vue"], "sourcesContent": ["<template>\n  <transition\n    :name=\"ns.b('fade')\"\n    @before-enter=\"isStartTransition = true\"\n    @before-leave=\"onClose\"\n    @after-leave=\"$emit('destroy')\"\n  >\n    <div\n      v-show=\"visible\"\n      :id=\"id\"\n      ref=\"messageRef\"\n      :class=\"[\n        ns.b(),\n        { [ns.m(type)]: type },\n        ns.is('closable', showClose),\n        ns.is('plain', plain),\n        ns.is('bottom', verticalProperty === 'bottom'),\n        horizontalClass,\n        customClass,\n      ]\"\n      :style=\"customStyle\"\n      role=\"alert\"\n      @mouseenter=\"clearTimer\"\n      @mouseleave=\"startTimer\"\n    >\n      <el-badge\n        v-if=\"repeatNum > 1\"\n        :value=\"repeatNum\"\n        :type=\"badgeType\"\n        :class=\"ns.e('badge')\"\n      />\n      <el-icon v-if=\"iconComponent\" :class=\"[ns.e('icon'), typeClass]\">\n        <component :is=\"iconComponent\" />\n      </el-icon>\n      <slot>\n        <p v-if=\"!dangerouslyUseHTMLString\" :class=\"ns.e('content')\">\n          {{ message }}\n        </p>\n        <!-- Caution here, message could've been compromised, never use user's input as message -->\n        <p v-else :class=\"ns.e('content')\" v-html=\"message\" />\n      </slot>\n      <el-icon v-if=\"showClose\" :class=\"ns.e('closeBtn')\" @click.stop=\"close\">\n        <Close />\n      </el-icon>\n    </div>\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, nextTick, onMounted, ref, watch } from 'vue'\nimport { useEventListener, useResizeObserver, useTimeoutFn } from '@vueuse/core'\nimport { TypeComponents, TypeComponentsMap } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport ElBadge from '@element-plus/components/badge'\nimport { useGlobalComponentSettings } from '@element-plus/components/config-provider'\nimport { ElIcon } from '@element-plus/components/icon'\nimport {\n  MESSAGE_DEFAULT_PLACEMENT,\n  messageEmits,\n  messageProps,\n} from './message'\nimport { getLastOffset, getOffsetOrSpace } from './instance'\n\nimport type { BadgeProps } from '@element-plus/components/badge'\nimport type { CSSProperties } from 'vue'\n\nconst { Close } = TypeComponents\n\ndefineOptions({\n  name: 'ElMessage',\n})\n\nconst props = defineProps(messageProps)\nconst emit = defineEmits(messageEmits)\n\nconst isStartTransition = ref(false)\n\nconst { ns, zIndex } = useGlobalComponentSettings('message')\nconst { currentZIndex, nextZIndex } = zIndex\n\nconst messageRef = ref<HTMLDivElement>()\nconst visible = ref(false)\nconst height = ref(0)\n\nlet stopTimer: (() => void) | undefined = undefined\n\nconst badgeType = computed<BadgeProps['type']>(() =>\n  props.type ? (props.type === 'error' ? 'danger' : props.type) : 'info'\n)\nconst typeClass = computed(() => {\n  const type = props.type\n  return { [ns.bm('icon', type)]: type && TypeComponentsMap[type] }\n})\nconst iconComponent = computed(\n  () => props.icon || TypeComponentsMap[props.type] || ''\n)\n\nconst placement = computed(() => props.placement || MESSAGE_DEFAULT_PLACEMENT)\n\nconst lastOffset = computed(() => getLastOffset(props.id, placement.value))\nconst offset = computed(() => {\n  return (\n    getOffsetOrSpace(props.id, props.offset, placement.value) + lastOffset.value\n  )\n})\nconst bottom = computed(() => height.value + offset.value)\nconst horizontalClass = computed(() => {\n  if (placement.value.includes('left')) return ns.is('left')\n  if (placement.value.includes('right')) return ns.is('right')\n  return ns.is('center')\n})\n\nconst verticalProperty = computed(() =>\n  placement.value.startsWith('top') ? 'top' : 'bottom'\n)\n\nconst customStyle = computed<CSSProperties>(() => ({\n  [verticalProperty.value]: `${offset.value}px`,\n  zIndex: currentZIndex.value,\n}))\n\nfunction startTimer() {\n  if (props.duration === 0) return\n  ;({ stop: stopTimer } = useTimeoutFn(() => {\n    close()\n  }, props.duration))\n}\n\nfunction clearTimer() {\n  stopTimer?.()\n}\n\nfunction close() {\n  visible.value = false\n\n  // if the message has never started a transition, we can destroy it immediately\n  nextTick(() => {\n    if (!isStartTransition.value) {\n      props.onClose?.()\n      emit('destroy')\n    }\n  })\n}\n\nfunction keydown({ code }: KeyboardEvent) {\n  if (code === EVENT_CODE.esc) {\n    // press esc to close the message\n    close()\n  }\n}\n\nonMounted(() => {\n  startTimer()\n  nextZIndex()\n  visible.value = true\n})\n\nwatch(\n  () => props.repeatNum,\n  () => {\n    clearTimer()\n    startTimer()\n  }\n)\n\nuseEventListener(document, 'keydown', keydown)\n\nuseResizeObserver(messageRef, () => {\n  height.value = messageRef.value!.getBoundingClientRect().height\n})\n\ndefineExpose({\n  visible,\n  bottom,\n  close,\n})\n</script>\n"], "names": ["TypeComponents", "ref", "useGlobalComponentSettings", "computed", "TypeComponentsMap", "MESSAGE_DEFAULT_PLACEMENT", "getLastOffset", "getOffsetOrSpace", "useTimeoutFn", "nextTick", "EVENT_CODE", "onMounted", "watch", "useEventListener", "useResizeObserver", "_openBlock", "_createBlock", "_Transition", "_unref"], "mappings": ";;;;;;;;;;;;;;;uCAoEc,CAAA;AAAA,EACZ,IAAM,EAAA,WAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAJA,IAAM,MAAA,EAAE,OAAU,GAAAA,mBAAA,CAAA;AASlB,IAAM,MAAA,iBAAA,GAAoBC,QAAI,KAAK,CAAA,CAAA;AAEnC,IAAA,MAAM,EAAE,EAAA,EAAI,MAAO,EAAA,GAAIC,2CAA2B,SAAS,CAAA,CAAA;AAC3D,IAAM,MAAA,EAAE,aAAe,EAAA,UAAA,EAAe,GAAA,MAAA,CAAA;AAEtC,IAAA,MAAM,aAAaD,OAAoB,EAAA,CAAA;AACvC,IAAM,MAAA,OAAA,GAAUA,QAAI,KAAK,CAAA,CAAA;AACzB,IAAM,MAAA,MAAA,GAASA,QAAI,CAAC,CAAA,CAAA;AAEpB,IAAA,IAAI,SAAsC,GAAA,KAAA,CAAA,CAAA;AAE1C,IAAA,MAAM,SAAY,GAAAE,YAAA,CAAA,MAAA,KAAA,CAAA,IAAA,GAAA,KAAA,CAAA,IAAA,KAAA,OAAA,GAAA,QAAA,GAAA,KAAA,CAAA,IAAA,GAAA,MAAA,CAAA,CAAA;AAAA,IAA6B,MAAA,SAC/B,GAAAA,YAAA,CAAM;AAA4C,MAClE,MAAA,IAAA,GAAA,KAAA,CAAA,IAAA,CAAA;AACA,MAAM,OAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAY,YAAe,CAAA,GAAA,IAAA,IAAAC,sBAAA,CAAA,IAAA,CAAA,EAAA,CAAA;AAC/B,KAAA,CAAA,CAAA;AACA,IAAO,MAAA,aAAS,GAAAD,YAAY,CAAC,MAAG,KAAQ,CAAkB,IAAA,IAAAC,sBAAM,CAAA,KAAA,CAAA,IAAA,CAAA,IAAA,EAAA,CAAA,CAAA;AAAA,IAClE,MAAC,SAAA,GAAAD,YAAA,CAAA,MAAA,KAAA,CAAA,SAAA,IAAAE,iCAAA,CAAA,CAAA;AACD,IAAA,MAAM,UAAgB,GAAAF,YAAA,CAAA,MAAAG,sBAAA,CAAA,KAAA,CAAA,EAAA,EAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IAAA,YACR,GAAAH,YAA0B,CAAA,MAAA;AAAe,MACvD,OAAAI,yBAAA,CAAA,KAAA,CAAA,EAAA,EAAA,KAAA,CAAA,MAAA,EAAA,SAAA,CAAA,KAAA,CAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AAEA,IAAM,MAAA,MAAA,GAAAJ,mBAA4B,MAAA,CAAA,KAAA,GAAA,YAAwB,CAAA,CAAA;AAC1D,IAAM,MAAA,kBAAkBA,YAAM,CAAA,MAAA;AAC5B,MACE,IAAA,SAAA,CAAA,KAAA,CAAA,eAA2B,CAAA;AAA4C,QAE1E,OAAA,EAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA;AACD,MAAA,IAAM,SAAS,CAAS,KAAA,CAAA,QAAM,CAAO,OAAA,CAAA;AACrC,QAAM,OAAA,EAAA,CAAA,EAAA,CAAA,OAAkB;AACtB,MAAI,OAAA,EAAA,CAAA,EAAA,CAAA,QAAyB,CAAA,CAAA;AAC7B,KAAI,CAAA,CAAA;AACJ,IAAO,MAAA,gBAAc,GAAAA,YAAA,CAAA,MAAA,SAAA,CAAA,KAAA,CAAA,UAAA,CAAA,KAAA,CAAA,GAAA,KAAA,GAAA,QAAA,CAAA,CAAA;AAAA,IACvB,MAAC,WAAA,GAAAA,YAAA,CAAA,OAAA;AAED,MAAA,CAAA,gBAAyB,CAAA,KAAA,GAAA,CAAA,EAAA,MAAA,CAAA,KAAA,CAAA,EAAA,CAAA;AAAA,MAAS,MAChC,EAAU,aAAA,CAAM,KAAW;AAAiB,KAC9C,CAAA,CAAA,CAAA;AAEA,IAAM,SAAA,UAAA;AAA6C,MACjD,IAAkB,KAAA,CAAA,QAAA,KAAK,CAAG;AAAe,eACnB;AAAA,MACtB,CAAA,EAAA,IAAA,EAAA,SAAA,EAAA,GAAAK,iBAAA,CAAA,MAAA;AAEF,QAAA,KAAS,EAAa,CAAA;AACpB,OAAI,EAAA,KAAA,CAAA;AACH,KAAA;AACC,IAAM,SAAA,UAAA,GAAA;AAAA,MACR,SAAS,IAAQ,IAAA,GAAA,KAAA,CAAA,GAAA,SAAA,EAAA,CAAA;AAAA,KACnB;AAEA,IAAA,SAAS,KAAa,GAAA;AACpB,MAAY,OAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAAA,MACdC,YAAA,CAAA,MAAA;AAEA,QAAA,IAAA,EAAiB,CAAA;AACf,QAAA,IAAA,CAAA,iBAAgB,CAAA,KAAA,EAAA;AAGhB,UAAA,CAAA,EAAA,GAAe,KAAA,CAAA,OAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACb,UAAI;AACF,SAAA;AACA,OAAA,CAAA,CAAA;AAAc,KAChB;AAAA,IAAA,SACD,OAAA,CAAA,EAAA,IAAA,EAAA,EAAA;AAAA,MACH,IAAA,IAAA,KAAAC,eAAA,CAAA,GAAA,EAAA;AAEA,QAAS,KAAA,EAAA,CAAA;AACP,OAAI;AAEF,KAAM;AAAA,IACRC,aAAA,CAAA,MAAA;AAAA,MACF,UAAA,EAAA,CAAA;AAEA,MAAA,UAAgB,EAAA,CAAA;AACd,MAAW,OAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACX,KAAW,CAAA,CAAA;AACX,IAAAC,SAAA,CAAA,MAAgB,KAAA,CAAA,SAAA,EAAA,MAAA;AAAA,MACjB,UAAA,EAAA,CAAA;AAED,MAAA,UAAA,EAAA,CAAA;AAAA,KAAA,CACE;AAAY,IAAAC,qBACN,CAAA,QAAA,EAAA,SAAA,EAAA,OAAA,CAAA,CAAA;AACJ,IAAWC,sBAAA,CAAA,UAAA,EAAA,MAAA;AACX,MAAW,MAAA,CAAA,KAAA,GAAA,UAAA,CAAA,KAAA,CAAA,qBAAA,EAAA,CAAA,MAAA,CAAA;AAAA,KACb,CAAA,CAAA;AAAA,IACF,MAAA,CAAA;AAEA,MAAiB,OAAA;AAEjB,MAAA,MAAA;AACE,MAAA,KAAA;AAAyD,KAC1D,CAAA,CAAA;AAED,IAAa,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACX,OAAAC,aAAA,EAAA,EAAAC,eAAA,CAAAC,cAAA,EAAA;AAAA,QACA,IAAA,EAAAC,SAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA;AAAA,QACA,aAAA,EAAA,CAAA,MAAA,KAAA,iBAAA,CAAA,KAAA,GAAA,IAAA;AAAA,QACD,aAAA,EAAA,IAAA,CAAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}