package handlers

import (
	"chocolate-shop/internal/models"
	"database/sql"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/jmoiron/sqlx"
)

type OrderHandler struct {
	db  *sqlx.DB
	rdb *redis.Client
}

func NewOrderHandler(db *sqlx.DB, rdb *redis.Client) *OrderHandler {
	return &OrderHandler{
		db:  db,
		rdb: rdb,
	}
}

// GetOrders 获取用户订单列表
func (h *OrderHandler) GetOrders(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "User not authenticated",
		})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 50 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize

	var orders []models.Order
	err := h.db.Select(&orders, `
		SELECT * FROM orders 
		WHERE user_id = $1 
		ORDER BY created_at DESC 
		LIMIT $2 OFFSET $3
	`, userID, pageSize, offset)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to retrieve orders",
		})
		return
	}

	// 获取总数
	var total int
	err = h.db.Get(&total, "SELECT COUNT(*) FROM orders WHERE user_id = $1", userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to get total count",
		})
		return
	}

	totalPages := (total + pageSize - 1) / pageSize

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Orders retrieved successfully",
		Data: models.PaginationResponse{
			Data:       orders,
			Page:       page,
			PageSize:   pageSize,
			Total:      total,
			TotalPages: totalPages,
		},
	})
}

// GetOrder 获取单个订单详情
func (h *OrderHandler) GetOrder(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "User not authenticated",
		})
		return
	}

	orderID := c.Param("id")

	// 获取订单信息
	var order models.Order
	err := h.db.Get(&order, "SELECT * FROM orders WHERE id = $1 AND user_id = $2", orderID, userID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Message: "Order not found",
		})
		return
	}

	// 获取订单项
	var orderItems []models.OrderItem
	err = h.db.Select(&orderItems, `
		SELECT oi.*, p.name, p.description, p.category, p.image_url
		FROM order_items oi
		JOIN products p ON oi.product_id = p.id
		WHERE oi.order_id = $1
		ORDER BY oi.created_at
	`, orderID)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to retrieve order items",
		})
		return
	}

	// 填充产品信息
	for i := range orderItems {
		orderItems[i].Product = &models.Product{
			ID:          orderItems[i].ProductID,
			Name:        orderItems[i].Product.Name,
			Description: orderItems[i].Product.Description,
			Category:    orderItems[i].Product.Category,
			ImageURL:    orderItems[i].Product.ImageURL,
		}
	}

	order.Items = orderItems

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Order retrieved successfully",
		Data:    order,
	})
}

// CreateOrder 创建订单
func (h *OrderHandler) CreateOrder(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "User not authenticated",
		})
		return
	}

	var req models.CreateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid request data: " + err.Error(),
		})
		return
	}

	// 开始事务
	tx, err := h.db.Beginx()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to start transaction",
		})
		return
	}
	defer tx.Rollback()

	// 获取购物车项
	var cartItems []models.CartItem
	err = tx.Select(&cartItems, `
		SELECT ci.*, p.price, p.stock_quantity
		FROM cart_items ci
		JOIN products p ON ci.product_id = p.id
		WHERE ci.user_id = $1 AND p.is_available = true
	`, userID)

	if err != nil || len(cartItems) == 0 {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Cart is empty or contains unavailable products",
		})
		return
	}

	// 计算总金额并检查库存
	var totalAmount float64
	for _, item := range cartItems {
		if item.Product.StockQuantity < item.Quantity {
			c.JSON(http.StatusBadRequest, models.APIResponse{
				Success: false,
				Message: "Insufficient stock for some products",
			})
			return
		}
		totalAmount += item.Product.Price * float64(item.Quantity)
	}

	// 创建订单
	var orderID int
	err = tx.QueryRow(`
		INSERT INTO orders (user_id, total_amount, shipping_address, payment_method)
		VALUES ($1, $2, $3, $4)
		RETURNING id
	`, userID, totalAmount, req.ShippingAddress, req.PaymentMethod).Scan(&orderID)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to create order",
		})
		return
	}

	// 创建订单项并更新库存
	for _, item := range cartItems {
		// 创建订单项
		_, err = tx.Exec(`
			INSERT INTO order_items (order_id, product_id, quantity, price)
			VALUES ($1, $2, $3, $4)
		`, orderID, item.ProductID, item.Quantity, item.Product.Price)

		if err != nil {
			c.JSON(http.StatusInternalServerError, models.APIResponse{
				Success: false,
				Message: "Failed to create order items",
			})
			return
		}

		// 更新库存
		_, err = tx.Exec(`
			UPDATE products 
			SET stock_quantity = stock_quantity - $1 
			WHERE id = $2
		`, item.Quantity, item.ProductID)

		if err != nil {
			c.JSON(http.StatusInternalServerError, models.APIResponse{
				Success: false,
				Message: "Failed to update stock",
			})
			return
		}
	}

	// 清空购物车
	_, err = tx.Exec("DELETE FROM cart_items WHERE user_id = $1", userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to clear cart",
		})
		return
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to commit transaction",
		})
		return
	}

	// 获取创建的订单
	var order models.Order
	err = h.db.Get(&order, "SELECT * FROM orders WHERE id = $1", orderID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to retrieve created order",
		})
		return
	}

	c.JSON(http.StatusCreated, models.APIResponse{
		Success: true,
		Message: "Order created successfully",
		Data:    order,
	})
}

// UpdateOrderStatus 更新订单状态（管理员功能）
func (h *OrderHandler) UpdateOrderStatus(c *gin.Context) {
	orderID := c.Param("id")
	
	var req struct {
		Status string `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid request data: " + err.Error(),
		})
		return
	}

	// 验证状态值
	validStatuses := map[string]bool{
		"pending":    true,
		"confirmed":  true,
		"preparing":  true,
		"shipping":   true,
		"delivered":  true,
		"cancelled":  true,
	}

	if !validStatuses[req.Status] {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid status value",
		})
		return
	}

	// 更新订单状态
	result, err := h.db.Exec(`
		UPDATE orders 
		SET status = $1, updated_at = CURRENT_TIMESTAMP 
		WHERE id = $2
	`, req.Status, orderID)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to update order status",
		})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Message: "Order not found",
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Order status updated successfully",
	})
}
