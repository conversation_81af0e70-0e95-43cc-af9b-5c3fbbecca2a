{"version": 3, "file": "panel-month-range.js", "sources": ["../../../../../../../packages/components/date-picker-panel/src/date-picker-com/panel-month-range.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[\n      ppNs.b(),\n      drpNs.b(),\n      ppNs.is('border', border),\n      ppNs.is('disabled', disabled),\n      {\n        'has-sidebar': Boolean($slots.sidebar) || hasShortcuts,\n      },\n    ]\"\n  >\n    <div :class=\"ppNs.e('body-wrapper')\">\n      <slot name=\"sidebar\" :class=\"ppNs.e('sidebar')\" />\n      <div v-if=\"hasShortcuts\" :class=\"ppNs.e('sidebar')\">\n        <button\n          v-for=\"(shortcut, key) in shortcuts\"\n          :key=\"key\"\n          type=\"button\"\n          :class=\"ppNs.e('shortcut')\"\n          :disabled=\"disabled\"\n          @click=\"handleShortcutClick(shortcut)\"\n        >\n          {{ shortcut.text }}\n        </button>\n      </div>\n      <div :class=\"ppNs.e('body')\">\n        <div :class=\"[ppNs.e('content'), drpNs.e('content')]\" class=\"is-left\">\n          <div :class=\"drpNs.e('header')\">\n            <button\n              type=\"button\"\n              :class=\"ppNs.e('icon-btn')\"\n              class=\"d-arrow-left\"\n              :disabled=\"disabled\"\n              @click=\"leftPrevYear\"\n            >\n              <slot name=\"prev-year\">\n                <el-icon><d-arrow-left /></el-icon>\n              </slot>\n            </button>\n            <button\n              v-if=\"unlinkPanels\"\n              type=\"button\"\n              :disabled=\"!enableYearArrow || disabled\"\n              :class=\"[\n                ppNs.e('icon-btn'),\n                { [ppNs.is('disabled')]: !enableYearArrow },\n              ]\"\n              class=\"d-arrow-right\"\n              @click=\"leftNextYear\"\n            >\n              <slot name=\"next-year\">\n                <el-icon><d-arrow-right /></el-icon>\n              </slot>\n            </button>\n            <div>{{ leftLabel }}</div>\n          </div>\n          <month-table\n            selection-mode=\"range\"\n            :date=\"leftDate\"\n            :min-date=\"minDate\"\n            :max-date=\"maxDate\"\n            :range-state=\"rangeState\"\n            :disabled-date=\"disabledDate\"\n            :disabled=\"disabled\"\n            :cell-class-name=\"cellClassName\"\n            @changerange=\"handleChangeRange\"\n            @pick=\"handleRangePick\"\n            @select=\"onSelect\"\n          />\n        </div>\n        <div :class=\"[ppNs.e('content'), drpNs.e('content')]\" class=\"is-right\">\n          <div :class=\"drpNs.e('header')\">\n            <button\n              v-if=\"unlinkPanels\"\n              type=\"button\"\n              :disabled=\"!enableYearArrow || disabled\"\n              :class=\"[ppNs.e('icon-btn'), { 'is-disabled': !enableYearArrow }]\"\n              class=\"d-arrow-left\"\n              @click=\"rightPrevYear\"\n            >\n              <slot name=\"prev-year\">\n                <el-icon><d-arrow-left /></el-icon>\n              </slot>\n            </button>\n            <button\n              type=\"button\"\n              :class=\"ppNs.e('icon-btn')\"\n              class=\"d-arrow-right\"\n              :disabled=\"disabled\"\n              @click=\"rightNextYear\"\n            >\n              <slot name=\"next-year\">\n                <el-icon><d-arrow-right /></el-icon>\n              </slot>\n            </button>\n            <div>{{ rightLabel }}</div>\n          </div>\n          <month-table\n            selection-mode=\"range\"\n            :date=\"rightDate\"\n            :min-date=\"minDate\"\n            :max-date=\"maxDate\"\n            :range-state=\"rangeState\"\n            :disabled-date=\"disabledDate\"\n            :disabled=\"disabled\"\n            :cell-class-name=\"cellClassName\"\n            @changerange=\"handleChangeRange\"\n            @pick=\"handleRangePick\"\n            @select=\"onSelect\"\n          />\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, ref, toRef, unref, watch } from 'vue'\nimport dayjs from 'dayjs'\nimport ElIcon from '@element-plus/components/icon'\nimport { isArray } from '@element-plus/utils'\nimport { useLocale } from '@element-plus/hooks'\nimport { DArrowLeft, DArrowRight } from '@element-plus/icons-vue'\nimport { PICKER_BASE_INJECTION_KEY } from '@element-plus/components/time-picker'\nimport {\n  correctlyParseUserInput,\n  getDefaultValue,\n  isValidRange,\n} from '../utils'\nimport {\n  panelMonthRangeEmits,\n  panelMonthRangeProps,\n} from '../props/panel-month-range'\nimport { useMonthRangeHeader } from '../composables/use-month-range-header'\nimport { useRangePicker } from '../composables/use-range-picker'\nimport { ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY } from '../constants'\nimport MonthTable from './basic-month-table.vue'\n\nimport type { Dayjs } from 'dayjs'\n\ndefineOptions({\n  name: 'DatePickerMonthRange',\n})\n\nconst props = defineProps(panelMonthRangeProps)\nconst emit = defineEmits(panelMonthRangeEmits)\nconst unit = 'year'\n\nconst { lang } = useLocale()\nconst pickerBase = inject(PICKER_BASE_INJECTION_KEY) as any\nconst isDefaultFormat = inject(\n  ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY,\n  undefined\n) as any\nconst { shortcuts, disabledDate, cellClassName } = pickerBase.props\nconst format = toRef(pickerBase.props, 'format')\nconst defaultValue = toRef(pickerBase.props, 'defaultValue')\nconst leftDate = ref(dayjs().locale(lang.value))\nconst rightDate = ref(dayjs().locale(lang.value).add(1, unit))\n\nconst {\n  minDate,\n  maxDate,\n  rangeState,\n  ppNs,\n  drpNs,\n\n  handleChangeRange,\n  handleRangeConfirm,\n  handleShortcutClick,\n  onSelect,\n  onReset,\n} = useRangePicker(props, {\n  defaultValue,\n  leftDate,\n  rightDate,\n  unit,\n  onParsedValueChanged,\n})\n\nconst hasShortcuts = computed(() => !!shortcuts.length)\n\nconst {\n  leftPrevYear,\n  rightNextYear,\n  leftNextYear,\n  rightPrevYear,\n  leftLabel,\n  rightLabel,\n  leftYear,\n  rightYear,\n} = useMonthRangeHeader({\n  unlinkPanels: toRef(props, 'unlinkPanels'),\n  leftDate,\n  rightDate,\n})\n\nconst enableYearArrow = computed(() => {\n  return props.unlinkPanels && rightYear.value > leftYear.value + 1\n})\n\ntype RangePickValue = {\n  minDate: Dayjs\n  maxDate: Dayjs\n}\n\nconst handleRangePick = (val: RangePickValue, close = true) => {\n  // const defaultTime = props.defaultTime || []\n  // const minDate_ = modifyWithTimeString(val.minDate, defaultTime[0])\n  // const maxDate_ = modifyWithTimeString(val.maxDate, defaultTime[1])\n  // todo\n  const minDate_ = val.minDate\n  const maxDate_ = val.maxDate\n  if (maxDate.value === maxDate_ && minDate.value === minDate_) {\n    return\n  }\n  emit('calendar-change', [minDate_.toDate(), maxDate_ && maxDate_.toDate()])\n  maxDate.value = maxDate_\n  minDate.value = minDate_\n\n  if (!close) return\n  handleRangeConfirm()\n}\n\nconst handleClear = () => {\n  leftDate.value = getDefaultValue(unref(defaultValue), {\n    lang: unref(lang),\n    unit: 'year',\n    unlinkPanels: props.unlinkPanels,\n  })[0]\n  rightDate.value = leftDate.value.add(1, 'year')\n  emit('pick', null)\n}\n\nconst formatToString = (value: Dayjs | Dayjs[]) => {\n  return isArray(value)\n    ? value.map((_) => _.format(format.value))\n    : value.format(format.value)\n}\n\nconst parseUserInput = (value: Dayjs | Dayjs[]) => {\n  return correctlyParseUserInput(\n    value,\n    format.value,\n    lang.value,\n    isDefaultFormat\n  )\n}\n\nfunction onParsedValueChanged(\n  minDate: Dayjs | undefined,\n  maxDate: Dayjs | undefined\n) {\n  if (props.unlinkPanels && maxDate) {\n    const minDateYear = minDate?.year() || 0\n    const maxDateYear = maxDate.year()\n    rightDate.value =\n      minDateYear === maxDateYear ? maxDate.add(1, unit) : maxDate\n  } else {\n    rightDate.value = leftDate.value.add(1, unit)\n  }\n}\n\nwatch(\n  () => props.visible,\n  (visible) => {\n    if (!visible && rangeState.value.selecting) {\n      onReset(props.parsedValue)\n      onSelect(false)\n    }\n  }\n)\n\nemit('set-picker-option', ['isValidValue', isValidRange])\nemit('set-picker-option', ['formatToString', formatToString])\nemit('set-picker-option', ['parseUserInput', parseUserInput])\nemit('set-picker-option', ['handleClear', handleClear])\n</script>\n"], "names": ["useLocale", "inject", "PICKER_BASE_INJECTION_KEY", "ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY", "toRef", "ref", "dayjs", "useRangePicker", "computed", "useMonthRangeHeader", "getDefaultValue", "unref", "isArray", "correctlyParseUserInput", "watch", "isValidRange", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;uCA6Ic,CAAA;AAAA,EACZ,IAAM,EAAA,sBAAA;AACR;;;;;;;AAMA,IAAM,MAAA,EAAE,IAAK,EAAA,GAAIA,eAAU,EAAA,CAAA;AAC3B,IAAM,MAAA,UAAA,GAAaC,WAAOC,mCAAyB,CAAA,CAAA;AACnD,IAAA,MAAM,eAAkB,GAAAD,UAAA,CAAAE,uDAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IACtB,MAAA,EAAA,SAAA,EAAA,YAAA,EAAA,aAAA,EAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AAAA,IACA,MAAA,MAAA,GAAAC,SAAA,CAAA,UAAA,CAAA,KAAA,EAAA,QAAA,CAAA,CAAA;AAAA,IACF,MAAA,YAAA,GAAAA,SAAA,CAAA,UAAA,CAAA,KAAA,EAAA,cAAA,CAAA,CAAA;AACA,IAAA,MAAM,QAAE,GAAAC,OAAyB,CAAAC,yBAAA,EAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAc;AAC/C,IAAA,MAAM,SAAS,GAAAD,OAAM,CAAWC,yBAAA,EAAA,CAAA,MAAA,CAAA,IAAe,CAAA,KAAA,CAAA,CAAA,GAAA,CAAA,CAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AAC/C,IAAA,MAAM;AACN,MAAA;AACA,MAAM,OAAA;AAEN,MAAM,UAAA;AAAA,MACJ,IAAA;AAAA,MACA,KAAA;AAAA,MACA,iBAAA;AAAA,MACA,kBAAA;AAAA,MACA,mBAAA;AAAA,MAEA,QAAA;AAAA,MACA,OAAA;AAAA,KACA,GAAAC,6BAAA,CAAA,KAAA,EAAA;AAAA,MACA,YAAA;AAAA,MACA,QAAA;AAAA,MACF;AAA0B,MACxB,IAAA;AAAA,MACA,oBAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACA,MAAA,YAAA,GAAAC,YAAA,CAAA,MAAA,CAAA,CAAA,SAAA,CAAA,MAAA,CAAA,CAAA;AAAA,IACA,MAAA;AAAA,MACD,YAAA;AAED,MAAA;AAEA,MAAM,YAAA;AAAA,MACJ,aAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA;AAAA,MACA,QAAA;AAAA,MACA,SAAA;AAAA,KACA,GAAAC,uCAAA,CAAA;AAAA,MACA,YAAA,EAAAL,SAAA,CAAA,KAAA,EAAA,cAAA,CAAA;AAAA,MACA,QAAA;AAAA,eACsB;AAAA,KACtB,CAAA,CAAA;AAAyC,IACzC,MAAA,eAAA,GAAAI,YAAA,CAAA,MAAA;AAAA,MACA,OAAA,KAAA,CAAA,YAAA,IAAA,SAAA,CAAA,KAAA,GAAA,QAAA,CAAA,KAAA,GAAA,CAAA,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,eAAA,GAAkB,WAAe,GAAA,IAAA,KAAA;AACrC,MAAA,MAAA,QAAa,GAAA,GAAA,CAAA,OAAA,CAAgB;AAAmC,MACjE,MAAA,QAAA,GAAA,GAAA,CAAA,OAAA,CAAA;AAOD,MAAA,IAAM,OAAkB,CAAA,KAAA,KAAA,QAAsB,IAAA,OAAiB,CAAA,KAAA,KAAA,QAAA,EAAA;AAK7D,QAAA;AACA,OAAA;AACA,MAAA,IAAI,CAAQ,iBAAU,EAAY,CAAA,QAAA,CAAA,MAAA,EAAQ,UAAU,IAAU,QAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA;AAC5D,MAAA,OAAA,CAAA,KAAA,GAAA,QAAA,CAAA;AAAA,MACF,OAAA,CAAA,KAAA,GAAA,QAAA,CAAA;AACA,MAAK,IAAA,CAAA,KAAA;AACL,QAAA,OAAgB;AAChB,MAAA,kBAAgB,EAAA,CAAA;AAEhB,KAAA,CAAA;AACA,IAAmB,MAAA,WAAA,GAAA,MAAA;AAAA,MACrB,QAAA,CAAA,KAAA,GAAAE,qBAAA,CAAAC,SAAA,CAAA,YAAA,CAAA,EAAA;AAEA,QAAA,oBAAoB,CAAM;AACxB,QAAA,IAAA,EAAA,MAAiB;AAAqC,QACpD,YAAY,EAAI,KAAA,CAAA,YAAA;AAAA,OAAA,CAChB,CAAM,CAAA,CAAA,CAAA;AAAA,MAAA,kBACc,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,EAAA,MAAA,CAAA,CAAA;AAAA,MACtB,IAAI,CAAA,MAAA,EAAA,IAAA,CAAA,CAAA;AACJ,KAAA,CAAA;AACA,IAAA,MAAA,cAAiB,GAAA,CAAA,KAAA,KAAA;AAAA,MACnB,OAAAC,cAAA,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,CAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAA,cAAoB,GAAA,CAAA,KACV,KAAA;AACmB,MAC/B,OAAAC,6BAAA,CAAA,KAAA,EAAA,MAAA,CAAA,KAAA,EAAA,IAAA,CAAA,KAAA,EAAA,eAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAO,SAAA,oBAAA,CAAA,QAAA,EAAA,QAAA,EAAA;AAAA,MACL,IAAA,KAAA,CAAA,YAAA,IAAA,QAAA,EAAA;AAAA,QACA,MAAO,WAAA,GAAA,CAAA,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,IAAA,EAAA,KAAA,CAAA,CAAA;AAAA,QACP,MAAK,WAAA,GAAA,QAAA,CAAA,IAAA,EAAA,CAAA;AAAA,QACL,SAAA,CAAA,KAAA,GAAA,WAAA,KAAA,WAAA,GAAA,QAAA,CAAA,GAAA,CAAA,CAAA,EAAA,IAAA,CAAA,GAAA,QAAA,CAAA;AAAA,OACF,MAAA;AAAA,QACF,SAAA,CAAA,KAAA,GAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,EAAA,IAAA,CAAA,CAAA;AAEA,OAAS;AAIP,KAAI;AACF,IAAMC,SAAA,CAAA,MAAA,KAAA,CAAA,OAAuB,EAAA,CAAA,OAAA,KAAU;AACvC,MAAM,IAAA,CAAA,OAAA,IAAA,gBAA2B,CAAA,SAAA,EAAA;AACjC,QAAA,OAAA,CAAA,iBACkB,CAAA,CAAA;AAAqC,QAClD,QAAA,CAAA,KAAA,CAAA,CAAA;AACL,OAAA;AAA4C,KAC9C,CAAA,CAAA;AAAA,IACF,IAAA,CAAA,mBAAA,EAAA,CAAA,cAAA,EAAAC,kBAAA,CAAA,CAAA,CAAA;AAEA,IAAA,IAAA,CAAA,mBAAA,EAAA,CAAA,gBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AAAA,IAAA,wBACc,EAAA,CAAA,gBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AAAA,IAAA,IACC,CAAA,mBAAA,EAAA,CAAA,aAAA,EAAA,WAAA,CAAA,CAAA,CAAA;AACX,IAAA,OAAA,CAAI,IAAC,EAAA,MAAW,KAAW;AACzB,MAAA,OAAAC,eAAyB,EAAAC,sBAAA,CAAA,KAAA,EAAA;AACzB,QAAA,KAAA,EAAAC,kBAAc,CAAA;AAAA,UAChBC,SAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA;AAAA,UACFA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EAAA;AAAA,UACFA,SAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,QAAA,EAAA,IAAA,CAAA,MAAA,CAAA;AAEA,UAA0BA,SAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,UAAiB,EAAA,IAAA,CAAA,QAAA,CAAA;AAC3C,UAA0B;AAC1B,YAA0B,aAAA,EAAA,OAAmB,CAAA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA,IAAAA,SAAe,CAAA,YAAA,CAAA;AAC5D,WAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}