{"version": 3, "file": "predefine.js", "sources": ["../../../../../../../packages/components/color-picker-panel/src/components/predefine.vue"], "sourcesContent": ["<template>\n  <div :class=\"ns.b()\">\n    <div :class=\"ns.e('colors')\">\n      <div\n        v-for=\"(item, index) in rgbaColors\"\n        :key=\"colors[index]\"\n        :class=\"[\n          ns.e('color-selector'),\n          ns.is('alpha', item.get('alpha') < 100),\n          { selected: item.selected },\n        ]\"\n        @click=\"handleSelect(index)\"\n      >\n        <div :style=\"{ backgroundColor: item.value }\" />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, inject, ref, watch, watchEffect } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { colorPickerPanelContextKey } from '../color-picker-panel'\nimport Color from '../utils/color'\n\nimport type { PropType, Ref } from 'vue'\n\nexport default defineComponent({\n  props: {\n    colors: {\n      type: Array as PropType<string[]>,\n      required: true,\n    },\n    color: {\n      type: Object as PropType<Color>,\n      required: true,\n    },\n    enableAlpha: {\n      type: Boolean,\n      required: true,\n    },\n    disabled: Boolean,\n  },\n  setup(props) {\n    const ns = useNamespace('color-predefine')\n    const { currentColor } = inject(colorPickerPanelContextKey)!\n\n    const rgbaColors = ref(parseColors(props.colors, props.color)) as Ref<\n      Color[]\n    >\n\n    watch(\n      () => currentColor.value,\n      (val) => {\n        const color = new Color({\n          value: val,\n        })\n\n        rgbaColors.value.forEach((item) => {\n          item.selected = color.compare(item)\n        })\n      }\n    )\n\n    watchEffect(() => {\n      rgbaColors.value = parseColors(props.colors, props.color)\n    })\n\n    function handleSelect(index: number) {\n      if (props.disabled) return\n      props.color.fromString(props.colors[index])\n    }\n\n    function parseColors(colors: string[], color: Color) {\n      return colors.map((value) => {\n        const c = new Color({\n          value,\n        })\n        c.selected = c.compare(color)\n        return c\n      })\n    }\n    return {\n      rgbaColors,\n      handleSelect,\n      ns,\n    }\n  },\n})\n</script>\n"], "names": ["defineComponent", "useNamespace", "inject", "colorPickerPanelContextKey", "ref", "watch", "color", "Color", "watchEffect", "_createElementVNode", "_normalizeClass", "_openBlock", "_createElementBlock", "_Fragment", "_renderList", "_normalizeStyle", "_export_sfc"], "mappings": ";;;;;;;;;;AA2BA,MAAK,YAAaA,mBAAa,CAAA;AAAA,EAC7B,KAAO,EAAA;AAAA,IACL,MAAQ,EAAA;AAAA,MACN,IAAM,EAAA,KAAA;AAAA,MACN,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,IACA,KAAO,EAAA;AAAA,MACL,IAAM,EAAA,MAAA;AAAA,MACN,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,IACA,WAAa,EAAA;AAAA,MACX,IAAM,EAAA,OAAA;AAAA,MACN,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,IACA,QAAU,EAAA,OAAA;AAAA,GACZ;AAAA,EACA,MAAM,KAAO,EAAA;AACX,IAAM,MAAA,EAAA,GAAKC,mBAAa,iBAAiB,CAAA,CAAA;AACzC,IAAA,MAAM,EAAE,YAAA,EAAiB,GAAAC,UAAA,CAAOC,2CAA0B,CAAA,CAAA;AAE1D,IAAA,MAAM,aAAaC,OAAI,CAAA,WAAA,CAAY,MAAM,MAAQ,EAAA,KAAA,CAAM,KAAK,CAAC,CAAA,CAAA;AAI7D,IAAAC,SAAA,CAAA,MAAA,YAAA,CAAA,KAAA,EAAA,CAAA,GAAA,KAAA;AAAA,MACE,MAAMC,OAAa,GAAA,IAAAC,gBAAA,CAAA;AAAA,QACV,KAAA,EAAA,GAAA;AACP,OAAM,CAAA,CAAA;AAAkB,MAAA,UACf,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,IAAA,KAAA;AAAA,QACT,IAAC,CAAA,QAAA,GAAAD,OAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA;AAED,OAAW,CAAA,CAAA;AACT,KAAK,CAAA,CAAA;AAA6B,IAAAE,eACnC,CAAA,MAAA;AAAA,MACH,UAAA,CAAA,KAAA,GAAA,WAAA,CAAA,KAAA,CAAA,MAAA,EAAA,KAAA,CAAA,KAAA,CAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAEA,IAAA,SAAA,YAAkB,CAAA,KAAA,EAAA;AAChB,MAAA,IAAA,KAAA,CAAA,QAAmB;AAAqC,QACzD,OAAA;AAED,MAAA,KAAA,CAAA,gBAAqC,CAAA,KAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACnC,KAAA;AACA,IAAA,SAAY,WAAA,CAAA,MAAiB,EAAAF,OAAA,EAAA;AAAa,MAC5C,OAAA,MAAA,CAAA,GAAA,CAAA,CAAA,KAAA,KAAA;AAEA,QAAS,MAAA,CAAA,GAAA,IAAAC;AACP,UAAO,KAAA;AACL,SAAM,CAAA,CAAA;AAAc,QAClB,CAAA,CAAA,QAAA,GAAA,CAAA,CAAA,OAAA,CAAAD,OAAA,CAAA,CAAA;AAAA,QACF,OAAC,CAAA,CAAA;AACD,OAAE,CAAA,CAAA;AACF,KAAO;AAAA,IAAA,OACR;AAAA,MACH,UAAA;AACA,MAAO,YAAA;AAAA,MACL,EAAA;AAAA,KACA,CAAA;AAAA,GACA;AAAA,CACF,CAAA,CAAA;AAEJ,SAAC,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;;;AAvFC,GAAA,EAAA;AAAA,IAeMG,sBAAA,CAAA,KAAA,EAAA;AAAA,MAAA,KAAA,EAAAC,kBAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA;AAAA,KAfA,EAAA;AAAW,OAAAC,aAAA,CAAA,IAAA,CAAA,EAAAC,sBAAA,CAAAC,YAAA,EAAA,IAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,IAAA,EAAA,KAAA,KAAA;;AACf,UAAA,GAAA,EAAA,IAAA,CAAA,MAAA,CAAA,KAAA,CAAA;AAAA,UAaM,KAAA,EAAAJ,kBAAA,CAAA;AAAA,YAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,gBAAA,CAAA;AAAA,YAbA,IAAA,CAAK,EAAE,CAAA,EAAA,CAAA,OAAA,EAAA,IAAA,CAAA,GAAA,CAAG,OAAC,CAAA,GAAA,GAAA,CAAA;AAAA,YAAA,EAAA,QAAA,EAAA,IAAA,CAAA,QAAA,EAAA;;AACf,UAAA,OAAA,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,YAAA,CAAA,KAAA,CAAA;AAAA,SAWM,EAAA;AAAA,UAAAD,sBAAA,CAAA,KAAA,EAAA;AAAA,YAVoB,KAAA,EAAAM,kBAAA,CAAA,EAAA,eAAL,EAAA,IAAA,CAAA,KAAA,EAAA,CAAA;;AAUf,SATH,EAAA,EAAA,EAAA,CAAA;AAAiB,OAAA,CAAA,EAAA,GAAA,CAAA;AACZ,KAAA,EAAA,CAAA,CAAA;AAAkB,GAAA,EAAA,CAAA,CAAA,CAAA;AAAqD,CAAA;gCAKvEC,8CAAe,CAAK,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,eAAA,CAAA,CAAA,CAAA;;;;"}