import api from '@/utils/api'

const state = {
  items: [],
  total: 0,
  count: 0,
  isVisible: false
}

const mutations = {
  SET_CART_ITEMS(state, items) {
    state.items = items
    state.count = items.reduce((total, item) => total + item.quantity, 0)
    state.total = items.reduce((total, item) => total + (item.product.price * item.quantity), 0)
  },
  
  ADD_CART_ITEM(state, item) {
    const existingItem = state.items.find(i => i.product_id === item.product_id)
    if (existingItem) {
      existingItem.quantity += item.quantity
    } else {
      state.items.push(item)
    }
    state.count = state.items.reduce((total, item) => total + item.quantity, 0)
    state.total = state.items.reduce((total, item) => total + (item.product.price * item.quantity), 0)
  },
  
  UPDATE_CART_ITEM(state, { id, quantity }) {
    const item = state.items.find(i => i.id === id)
    if (item) {
      item.quantity = quantity
    }
    state.count = state.items.reduce((total, item) => total + item.quantity, 0)
    state.total = state.items.reduce((total, item) => total + (item.product.price * item.quantity), 0)
  },
  
  REMOVE_CART_ITEM(state, id) {
    state.items = state.items.filter(item => item.id !== id)
    state.count = state.items.reduce((total, item) => total + item.quantity, 0)
    state.total = state.items.reduce((total, item) => total + (item.product.price * item.quantity), 0)
  },
  
  CLEAR_CART(state) {
    state.items = []
    state.total = 0
    state.count = 0
  },
  
  SET_CART_VISIBILITY(state, visible) {
    state.isVisible = visible
  }
}

const actions = {
  // 加载购物车
  async loadCart({ commit, rootGetters }) {
    if (!rootGetters['auth/isAuthenticated']) {
      return
    }
    
    try {
      const response = await api.get('/cart')
      commit('SET_CART_ITEMS', response.data.data)
    } catch (error) {
      console.error('Load cart error:', error)
    }
  },
  
  // 添加到购物车
  async addToCart({ commit, dispatch }, { productId, quantity = 1 }) {
    try {
      await api.post('/cart/add', {
        product_id: productId,
        quantity
      })
      
      // 重新加载购物车
      await dispatch('loadCart')
      
      // 显示成功通知
      dispatch('notifications/showNotification', {
        type: 'success',
        message: '已添加到购物车'
      }, { root: true })
      
      return { success: true }
    } catch (error) {
      const message = error.response?.data?.message || '添加到购物车失败'
      dispatch('notifications/showNotification', {
        type: 'error',
        message
      }, { root: true })
      throw new Error(message)
    }
  },
  
  // 更新购物车项数量
  async updateCartItem({ commit, dispatch }, { id, quantity }) {
    try {
      await api.put(`/cart/update/${id}`, { quantity })
      commit('UPDATE_CART_ITEM', { id, quantity })
      
      dispatch('notifications/showNotification', {
        type: 'success',
        message: '购物车已更新'
      }, { root: true })
      
      return { success: true }
    } catch (error) {
      const message = error.response?.data?.message || '更新购物车失败'
      dispatch('notifications/showNotification', {
        type: 'error',
        message
      }, { root: true })
      throw new Error(message)
    }
  },
  
  // 从购物车移除
  async removeFromCart({ commit, dispatch }, id) {
    try {
      await api.delete(`/cart/remove/${id}`)
      commit('REMOVE_CART_ITEM', id)
      
      dispatch('notifications/showNotification', {
        type: 'success',
        message: '已从购物车移除'
      }, { root: true })
      
      return { success: true }
    } catch (error) {
      const message = error.response?.data?.message || '移除失败'
      dispatch('notifications/showNotification', {
        type: 'error',
        message
      }, { root: true })
      throw new Error(message)
    }
  },
  
  // 清空购物车
  async clearCart({ commit, dispatch }) {
    try {
      await api.delete('/cart/clear')
      commit('CLEAR_CART')
      
      dispatch('notifications/showNotification', {
        type: 'success',
        message: '购物车已清空'
      }, { root: true })
      
      return { success: true }
    } catch (error) {
      const message = error.response?.data?.message || '清空购物车失败'
      dispatch('notifications/showNotification', {
        type: 'error',
        message
      }, { root: true })
      throw new Error(message)
    }
  },
  
  // 显示/隐藏购物车
  toggleCartVisibility({ commit, state }) {
    commit('SET_CART_VISIBILITY', !state.isVisible)
  },
  
  showCart({ commit }) {
    commit('SET_CART_VISIBILITY', true)
  },
  
  hideCart({ commit }) {
    commit('SET_CART_VISIBILITY', false)
  }
}

const getters = {
  cartItems: state => state.items,
  cartTotal: state => state.total,
  cartCount: state => state.count,
  isCartVisible: state => state.isVisible,
  isCartEmpty: state => state.items.length === 0
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
