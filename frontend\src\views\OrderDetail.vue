<template>
  <div class="order-detail-page">
    <div class="container">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      
      <div v-else-if="order" class="order-detail">
        <div class="order-header">
          <div class="order-info">
            <h1>订单详情</h1>
            <p>订单号: {{ order.id }}</p>
            <p>下单时间: {{ formatDate(order.created_at) }}</p>
          </div>
          <div class="order-status">
            <el-tag :type="getStatusType(order.status)" size="large">
              {{ getStatusText(order.status) }}
            </el-tag>
          </div>
        </div>
        
        <div class="order-content">
          <div class="order-items">
            <h3>订单商品</h3>
            <div class="items-list">
              <div
                v-for="item in order.items"
                :key="item.id"
                class="order-item"
              >
                <div class="item-image">
                  <img v-if="item.product?.image_url" :src="item.product.image_url" :alt="item.product.name" />
                  <div v-else class="placeholder-image">🍫</div>
                </div>
                
                <div class="item-details">
                  <h4>{{ item.product?.name || '商品' }}</h4>
                  <p>{{ item.product?.description || '' }}</p>
                  <p class="item-category">{{ item.product?.category || '' }}</p>
                </div>
                
                <div class="item-quantity">
                  <span>数量: {{ item.quantity }}</span>
                </div>
                
                <div class="item-price">
                  <span>单价: ¥{{ item.price.toFixed(2) }}</span>
                  <span class="total">小计: ¥{{ (item.price * item.quantity).toFixed(2) }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="order-summary">
            <div class="summary-card">
              <h3>订单摘要</h3>
              
              <div class="summary-row">
                <span>商品总计</span>
                <span>¥{{ order.total_amount.toFixed(2) }}</span>
              </div>
              <div class="summary-row">
                <span>配送费</span>
                <span>免费</span>
              </div>
              <div class="summary-row total-row">
                <span>订单总计</span>
                <span>¥{{ order.total_amount.toFixed(2) }}</span>
              </div>
              
              <div class="order-info-section">
                <h4>配送信息</h4>
                <p>{{ order.shipping_address }}</p>
                
                <h4>支付方式</h4>
                <p>{{ getPaymentMethodText(order.payment_method) }}</p>
                
                <h4>支付状态</h4>
                <el-tag :type="order.payment_status === 'paid' ? 'success' : 'warning'">
                  {{ order.payment_status === 'paid' ? '已支付' : '待支付' }}
                </el-tag>
              </div>
              
              <div class="order-actions">
                <el-button
                  v-if="order.status === 'pending'"
                  type="danger"
                  @click="cancelOrder"
                >
                  取消订单
                </el-button>
                <el-button @click="goBack">
                  返回订单列表
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div v-else class="not-found">
        <el-icon size="80" color="#ccc">
          <Warning />
        </el-icon>
        <h2>订单不存在</h2>
        <p>抱歉，您查找的订单不存在。</p>
        <router-link to="/orders" class="btn btn-primary">返回订单列表</router-link>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { Warning } from '@element-plus/icons-vue'

export default {
  name: 'OrderDetail',
  components: {
    Warning
  },
  setup() {
    const store = useStore()
    const route = useRoute()
    const router = useRouter()
    
    const order = computed(() => store.getters['orders/currentOrder'])
    const loading = computed(() => store.getters['orders/loading'])
    
    const cancelOrder = async () => {
      try {
        await store.dispatch('orders/updateOrderStatus', {
          orderId: order.value.id,
          status: 'cancelled'
        })
      } catch (error) {
        console.error('Cancel order error:', error)
      }
    }
    
    const goBack = () => {
      router.push('/orders')
    }
    
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleString('zh-CN')
    }
    
    const getStatusType = (status) => {
      const statusMap = {
        pending: '',
        confirmed: 'success',
        preparing: 'warning',
        shipping: 'info',
        delivered: 'success',
        cancelled: 'danger'
      }
      return statusMap[status] || ''
    }
    
    const getStatusText = (status) => {
      const statusMap = {
        pending: '待确认',
        confirmed: '已确认',
        preparing: '制作中',
        shipping: '配送中',
        delivered: '已送达',
        cancelled: '已取消'
      }
      return statusMap[status] || status
    }
    
    const getPaymentMethodText = (method) => {
      const methodMap = {
        alipay: '支付宝',
        wechat: '微信支付',
        credit_card: '信用卡'
      }
      return methodMap[method] || method
    }
    
    onMounted(async () => {
      const orderId = route.params.id
      try {
        await store.dispatch('orders/loadOrder', orderId)
      } catch (error) {
        store.dispatch('notifications/showNotification', {
          type: 'error',
          message: error.message
        })
      }
    })
    
    return {
      order,
      loading,
      cancelOrder,
      goBack,
      formatDate,
      getStatusType,
      getStatusText,
      getPaymentMethodText
    }
  }
}
</script>

<style lang="scss" scoped>
.order-detail-page {
  padding: 40px 0;
  
  .loading-container {
    padding: 40px 0;
  }
  
  .order-detail {
    max-width: 1200px;
    margin: 0 auto;
    
    .order-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 40px;
      padding-bottom: 20px;
      border-bottom: 2px solid var(--color-primary);
      
      .order-info {
        h1 {
          font-size: 32px;
          font-weight: 800;
          color: var(--color-ink);
          margin-bottom: 8px;
        }
        
        p {
          font-size: 16px;
          color: #666;
          margin: 4px 0;
        }
      }
    }
    
    .order-content {
      display: grid;
      grid-template-columns: 1fr 400px;
      gap: 40px;
      
      .order-items {
        h3 {
          font-size: 24px;
          font-weight: 700;
          color: var(--color-ink);
          margin-bottom: 20px;
        }
        
        .items-list {
          .order-item {
            display: grid;
            grid-template-columns: 80px 1fr 120px 150px;
            gap: 20px;
            align-items: center;
            padding: 20px;
            border: 1px solid #f0f0f0;
            border-radius: var(--radius-lg);
            margin-bottom: 16px;
            background: white;
            
            .item-image {
              width: 80px;
              height: 80px;
              border-radius: var(--radius-sm);
              overflow: hidden;
              
              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
              
              .placeholder-image {
                width: 100%;
                height: 100%;
                @include chocolate-gradient;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 32px;
              }
            }
            
            .item-details {
              h4 {
                font-size: 18px;
                font-weight: 600;
                color: var(--color-ink);
                margin: 0 0 4px 0;
              }
              
              p {
                font-size: 14px;
                color: #666;
                margin: 2px 0;
              }
              
              .item-category {
                font-size: 12px;
                color: var(--color-primary);
                font-weight: 600;
              }
            }
            
            .item-quantity {
              font-size: 16px;
              color: var(--color-ink);
            }
            
            .item-price {
              text-align: right;
              
              span {
                display: block;
                margin-bottom: 4px;
              }
              
              .total {
                font-size: 18px;
                font-weight: 700;
                color: var(--color-primary);
              }
            }
          }
        }
      }
      
      .order-summary {
        .summary-card {
          @include card-style;
          position: sticky;
          top: 100px;
          
          h3 {
            font-size: 20px;
            font-weight: 700;
            color: var(--color-ink);
            margin-bottom: 20px;
          }
          
          .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            
            &.total-row {
              font-size: 18px;
              font-weight: 700;
              color: var(--color-ink);
              margin-top: 12px;
              padding-top: 16px;
              border-top: 2px solid var(--color-primary);
            }
          }
          
          .order-info-section {
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid #f0f0f0;
            
            h4 {
              font-size: 16px;
              font-weight: 600;
              color: var(--color-ink);
              margin: 16px 0 8px 0;
              
              &:first-child {
                margin-top: 0;
              }
            }
            
            p {
              font-size: 14px;
              color: #666;
              margin: 0 0 8px 0;
            }
          }
          
          .order-actions {
            margin-top: 24px;
            display: flex;
            flex-direction: column;
            gap: 12px;
            
            .el-button {
              width: 100%;
            }
          }
        }
      }
    }
  }
  
  .not-found {
    text-align: center;
    padding: 80px 20px;
    
    h2 {
      font-size: 24px;
      color: var(--color-ink);
      margin: 24px 0 16px 0;
    }
    
    p {
      font-size: 16px;
      color: #666;
      margin-bottom: 32px;
    }
  }
}

@include respond-to('mobile') {
  .order-detail-page {
    .order-detail {
      .order-header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
      }
      
      .order-content {
        grid-template-columns: 1fr;
        gap: 20px;
        
        .order-items {
          .items-list {
            .order-item {
              grid-template-columns: 60px 1fr;
              gap: 12px;
              
              .item-details {
                grid-column: 2;
              }
              
              .item-quantity,
              .item-price {
                grid-column: 1 / -1;
                margin-top: 12px;
              }
              
              .item-price {
                text-align: left;
              }
            }
          }
        }
      }
    }
  }
}
</style>
