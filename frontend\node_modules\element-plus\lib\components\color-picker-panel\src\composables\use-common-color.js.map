{"version": 3, "file": "use-common-color.js", "sources": ["../../../../../../../packages/components/color-picker-panel/src/composables/use-common-color.ts"], "sourcesContent": ["import { reactive, watch } from 'vue'\nimport Color from '../utils/color'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\n\ntype CommonColorProps = {\n  modelValue?: string | null\n  showAlpha: boolean\n  colorFormat?: string\n}\ntype CommonColorEmits = (event: 'update:modelValue', ...args: any[]) => void\n\nexport const useCommonColor = <\n  P extends CommonColorProps,\n  E extends CommonColorEmits\n>(\n  props: P,\n  emit: E\n) => {\n  const color = reactive(\n    new Color({\n      enableAlpha: props.showAlpha,\n      format: props.colorFormat || '',\n      value: props.modelValue,\n    })\n  ) as Color\n\n  watch(\n    () => [props.colorFormat, props.showAlpha],\n    () => {\n      color.enableAlpha = props.showAlpha\n      color.format = props.colorFormat || color.format\n      color.doOnChange()\n      emit(UPDATE_MODEL_EVENT, color.value)\n    }\n  )\n\n  return {\n    color,\n  }\n}\n"], "names": ["color", "reactive", "Color", "watch", "UPDATE_MODEL_EVENT"], "mappings": ";;;;;;;;AAGY,MAAC,cAAc,GAAG,CAAC,KAAK,EAAE,IAAI,KAAK;AAC/C,EAAE,MAAMA,OAAK,GAAGC,YAAQ,CAAC,IAAIC,gBAAK,CAAC;AACnC,IAAI,WAAW,EAAE,KAAK,CAAC,SAAS;AAChC,IAAI,MAAM,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;AACnC,IAAI,KAAK,EAAE,KAAK,CAAC,UAAU;AAC3B,GAAG,CAAC,CAAC,CAAC;AACN,EAAEC,SAAK,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,SAAS,CAAC,EAAE,MAAM;AAC1D,IAAIH,OAAK,CAAC,WAAW,GAAG,KAAK,CAAC,SAAS,CAAC;AACxC,IAAIA,OAAK,CAAC,MAAM,GAAG,KAAK,CAAC,WAAW,IAAIA,OAAK,CAAC,MAAM,CAAC;AACrD,IAAIA,OAAK,CAAC,UAAU,EAAE,CAAC;AACvB,IAAI,IAAI,CAACI,wBAAkB,EAAEJ,OAAK,CAAC,KAAK,CAAC,CAAC;AAC1C,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,WAAIA,OAAK;AACT,GAAG,CAAC;AACJ;;;;"}