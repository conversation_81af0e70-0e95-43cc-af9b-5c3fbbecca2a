<template>
  <header class="header">
    <div class="container">
      <nav class="nav">
        <!-- Logo -->
        <router-link to="/" class="brand">
          <span class="brand-mark">🍫</span>
          <span class="brand-text">X Oberon</span>
        </router-link>
        
        <!-- 导航菜单 -->
        <div class="nav-menu">
          <router-link to="/products" class="nav-link">巧克力</router-link>
          <router-link to="/about" class="nav-link">关于我们</router-link>
          <router-link to="/contact" class="nav-link">联系我们</router-link>
        </div>
        
        <!-- 搜索框 -->
        <div class="search-box">
          <el-input
            v-model="searchQuery"
            placeholder="搜索巧克力..."
            @keyup.enter="handleSearch"
            class="search-input"
          >
            <template #suffix>
              <el-icon @click="handleSearch" class="search-icon">
                <Search />
              </el-icon>
            </template>
          </el-input>
        </div>
        
        <!-- 用户操作 -->
        <div class="nav-actions">
          <!-- 购物车 -->
          <div class="cart-button" @click="toggleCart">
            <el-badge :value="cartCount" :hidden="cartCount === 0">
              <el-icon size="24">
                <ShoppingCart />
              </el-icon>
            </el-badge>
          </div>
          
          <!-- 用户菜单 -->
          <div v-if="isAuthenticated" class="user-menu">
            <el-dropdown @command="handleUserCommand">
              <span class="user-info">
                <el-avatar :size="32">{{ userName.charAt(0).toUpperCase() }}</el-avatar>
                <span class="user-name">{{ userName }}</span>
                <el-icon><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人资料</el-dropdown-item>
                  <el-dropdown-item command="orders">我的订单</el-dropdown-item>
                  <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          
          <!-- 登录按钮 -->
          <div v-else class="auth-buttons">
            <router-link to="/login" class="btn btn-outline">登录</router-link>
            <router-link to="/register" class="btn btn-primary">注册</router-link>
          </div>
        </div>
        
        <!-- 移动端菜单按钮 -->
        <div class="mobile-menu-button" @click="toggleMobileMenu">
          <el-icon size="24">
            <Menu />
          </el-icon>
        </div>
      </nav>
      
      <!-- 移动端菜单 -->
      <div v-show="showMobileMenu" class="mobile-menu">
        <router-link to="/products" class="mobile-nav-link" @click="closeMobileMenu">巧克力</router-link>
        <router-link to="/about" class="mobile-nav-link" @click="closeMobileMenu">关于我们</router-link>
        <router-link to="/contact" class="mobile-nav-link" @click="closeMobileMenu">联系我们</router-link>
        <div v-if="!isAuthenticated" class="mobile-auth">
          <router-link to="/login" class="btn btn-outline" @click="closeMobileMenu">登录</router-link>
          <router-link to="/register" class="btn btn-primary" @click="closeMobileMenu">注册</router-link>
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import { computed, ref } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { Search, ShoppingCart, ArrowDown, Menu } from '@element-plus/icons-vue'

export default {
  name: 'Header',
  components: {
    Search,
    ShoppingCart,
    ArrowDown,
    Menu
  },
  setup() {
    const store = useStore()
    const router = useRouter()
    
    const searchQuery = ref('')
    const showMobileMenu = ref(false)
    
    // 计算属性
    const isAuthenticated = computed(() => store.getters['auth/isAuthenticated'])
    const userName = computed(() => store.getters['auth/userName'])
    const cartCount = computed(() => store.getters['cart/cartCount'])
    
    // 方法
    const handleSearch = () => {
      if (searchQuery.value.trim()) {
        router.push({
          name: 'Products',
          query: { search: searchQuery.value.trim() }
        })
        searchQuery.value = ''
      }
    }
    
    const toggleCart = () => {
      store.dispatch('cart/toggleCartVisibility')
    }
    
    const handleUserCommand = (command) => {
      switch (command) {
        case 'profile':
          router.push('/profile')
          break
        case 'orders':
          router.push('/orders')
          break
        case 'logout':
          store.dispatch('auth/logout')
          router.push('/')
          break
      }
    }
    
    const toggleMobileMenu = () => {
      showMobileMenu.value = !showMobileMenu.value
    }
    
    const closeMobileMenu = () => {
      showMobileMenu.value = false
    }
    
    return {
      searchQuery,
      showMobileMenu,
      isAuthenticated,
      userName,
      cartCount,
      handleSearch,
      toggleCart,
      handleUserCommand,
      toggleMobileMenu,
      closeMobileMenu
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  background: white;
  box-shadow: var(--shadow-1);
  position: sticky;
  top: 0;
  z-index: 1000;
  
  .nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0;
    gap: 24px;
  }
  
  .brand {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 24px;
    font-weight: 800;
    color: var(--color-ink);
    text-decoration: none;
    
    .brand-mark {
      font-size: 28px;
    }
  }
  
  .nav-menu {
    display: flex;
    gap: 32px;
    
    .nav-link {
      color: var(--color-ink);
      font-weight: 600;
      text-decoration: none;
      transition: color 0.2s ease;
      
      &:hover,
      &.router-link-active {
        color: var(--color-primary);
      }
    }
  }
  
  .search-box {
    flex: 1;
    max-width: 400px;
    
    .search-input {
      :deep(.el-input__wrapper) {
        border-radius: 24px;
        padding: 0 16px;
      }
    }
    
    .search-icon {
      cursor: pointer;
      color: var(--color-primary);
    }
  }
  
  .nav-actions {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .cart-button {
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s ease;
    
    &:hover {
      background-color: var(--color-sand);
    }
  }
  
  .user-menu {
    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 24px;
      transition: background-color 0.2s ease;
      
      &:hover {
        background-color: var(--color-sand);
      }
    }
    
    .user-name {
      font-weight: 600;
      color: var(--color-ink);
    }
  }
  
  .auth-buttons {
    display: flex;
    gap: 12px;
  }
  
  .mobile-menu-button {
    display: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    
    &:hover {
      background-color: var(--color-sand);
    }
  }
  
  .mobile-menu {
    display: none;
    flex-direction: column;
    gap: 16px;
    padding: 20px 0;
    border-top: 1px solid #e5e7eb;
    
    .mobile-nav-link {
      color: var(--color-ink);
      font-weight: 600;
      text-decoration: none;
      padding: 12px 0;
      
      &:hover,
      &.router-link-active {
        color: var(--color-primary);
      }
    }
    
    .mobile-auth {
      display: flex;
      gap: 12px;
      margin-top: 16px;
    }
  }
}

@include respond-to('mobile') {
  .header {
    .nav-menu,
    .search-box,
    .auth-buttons {
      display: none;
    }
    
    .mobile-menu-button {
      display: block;
    }
    
    .mobile-menu {
      display: flex;
    }
  }
}
</style>
