{"version": 3, "file": "method.js", "sources": ["../../../../../../packages/components/message/src/method.ts"], "sourcesContent": ["import { createVNode, isVNode, render } from 'vue'\nimport {\n  debugWarn,\n  hasOwn,\n  isBoolean,\n  isClient,\n  isElement,\n  isFunction,\n  isNumber,\n  isString,\n} from '@element-plus/utils'\nimport { messageConfig } from '@element-plus/components/config-provider'\nimport MessageConstructor from './message.vue'\nimport {\n  MESSAGE_DEFAULT_PLACEMENT,\n  messageDefaults,\n  messagePlacement,\n  messageTypes,\n} from './message'\nimport { getOrCreatePlacementInstances, placementInstances } from './instance'\n\nimport type { MessageContext } from './instance'\nimport type { AppContext } from 'vue'\nimport type {\n  Message,\n  MessageFn,\n  MessageHandler,\n  MessageOptions,\n  MessageParams,\n  MessageParamsNormalized,\n  MessagePlacement,\n  MessageType,\n} from './message'\n\nlet seed = 1\n\n// TODO: Since Notify.ts is basically the same like this file. So we could do some encapsulation against them to reduce code duplication.\n\nconst normalizeAppendTo = (normalized: MessageOptions) => {\n  const appendTo = normalized.appendTo\n  if (!appendTo) {\n    normalized.appendTo = document.body\n  } else if (isString(normalized.appendTo)) {\n    let appendTo = document.querySelector<HTMLElement>(normalized.appendTo)\n\n    // should fallback to default value with a warning\n    if (!isElement(appendTo)) {\n      debugWarn(\n        'ElMessage',\n        'the appendTo option is not an HTMLElement. Falling back to document.body.'\n      )\n      appendTo = document.body\n    }\n    normalized.appendTo = appendTo\n  }\n}\n\nconst normalizePlacement = (normalized: MessageOptions) => {\n  // if placement is not passed and global has config, use global config\n  if (\n    !normalized.placement &&\n    isString(messageConfig.placement) &&\n    messageConfig.placement\n  ) {\n    normalized.placement = messageConfig.placement as\n      | MessagePlacement\n      | undefined\n  }\n  // if placement is not passed and global has no config, use default config\n  if (!normalized.placement) {\n    normalized.placement = MESSAGE_DEFAULT_PLACEMENT\n  }\n  // if placement is not valid, use default config\n  if (!messagePlacement.includes(normalized.placement!)) {\n    debugWarn(\n      'ElMessage',\n      `Invalid placement: ${normalized.placement}. Falling back to '${MESSAGE_DEFAULT_PLACEMENT}'.`\n    )\n    normalized.placement = MESSAGE_DEFAULT_PLACEMENT\n  }\n}\n\nconst normalizeOptions = (params?: MessageParams) => {\n  const options: MessageOptions =\n    !params || isString(params) || isVNode(params) || isFunction(params)\n      ? { message: params }\n      : params\n\n  const normalized: MessageOptions = {\n    ...messageDefaults,\n    ...options,\n  }\n\n  normalizeAppendTo(normalized)\n  normalizePlacement(normalized)\n\n  // When grouping is configured globally,\n  // if grouping is manually set when calling message individually and it is not equal to the default value,\n  // the global configuration cannot override the current setting. default => false\n  if (isBoolean(messageConfig.grouping) && !normalized.grouping) {\n    normalized.grouping = messageConfig.grouping\n  }\n  if (isNumber(messageConfig.duration) && normalized.duration === 3000) {\n    normalized.duration = messageConfig.duration\n  }\n  if (isNumber(messageConfig.offset) && normalized.offset === 16) {\n    normalized.offset = messageConfig.offset\n  }\n  if (isBoolean(messageConfig.showClose) && !normalized.showClose) {\n    normalized.showClose = messageConfig.showClose\n  }\n  if (isBoolean(messageConfig.plain) && !normalized.plain) {\n    normalized.plain = messageConfig.plain\n  }\n\n  return normalized as MessageParamsNormalized\n}\n\nconst closeMessage = (instance: MessageContext) => {\n  const placement = instance.props.placement || MESSAGE_DEFAULT_PLACEMENT\n  const instances = placementInstances[placement]\n\n  const idx = instances.indexOf(instance)\n  if (idx === -1) return\n  instances.splice(idx, 1)\n  const { handler } = instance\n  handler.close()\n}\n\nconst createMessage = (\n  { appendTo, ...options }: MessageParamsNormalized,\n  context?: AppContext | null\n): MessageContext => {\n  const id = `message_${seed++}`\n  const userOnClose = options.onClose\n\n  const container = document.createElement('div')\n\n  const props = {\n    ...options,\n    // now the zIndex will be used inside the message.vue component instead of here.\n    // zIndex: nextIndex() + options.zIndex\n    id,\n    onClose: () => {\n      userOnClose?.()\n      closeMessage(instance)\n    },\n\n    // clean message element preventing mem leak\n    onDestroy: () => {\n      // since the element is destroy, then the VNode should be collected by GC as well\n      // we do not want cause any mem leak because we have returned vm as a reference to users\n      // so that we manually set it to false.\n      render(null, container)\n    },\n  }\n  const vnode = createVNode(\n    MessageConstructor,\n    props,\n    isFunction(props.message) || isVNode(props.message)\n      ? {\n          default: isFunction(props.message)\n            ? props.message\n            : () => props.message,\n        }\n      : null\n  )\n  vnode.appContext = context || message._context\n\n  render(vnode, container)\n  // instances will remove this item when close function gets called. So we do not need to worry about it.\n  appendTo.appendChild(container.firstElementChild!)\n\n  const vm = vnode.component!\n\n  const handler: MessageHandler = {\n    // instead of calling the onClose function directly, setting this value so that we can have the full lifecycle\n    // for out component, so that all closing steps will not be skipped.\n    close: () => {\n      vm.exposed!.close()\n    },\n  }\n\n  const instance: MessageContext = {\n    id,\n    vnode,\n    vm,\n    handler,\n    props: (vnode.component as any).props,\n  }\n\n  return instance\n}\n\nconst message: MessageFn &\n  Partial<Message> & { _context: AppContext | null } = (\n  options = {},\n  context\n) => {\n  if (!isClient) return { close: () => undefined }\n\n  const normalized = normalizeOptions(options)\n  const instances = getOrCreatePlacementInstances(\n    normalized.placement || MESSAGE_DEFAULT_PLACEMENT\n  )\n\n  if (normalized.grouping && instances.length) {\n    const instance = instances.find(\n      ({ vnode: vm }) => vm.props?.message === normalized.message\n    )\n    if (instance) {\n      instance.props.repeatNum += 1\n      instance.props.type = normalized.type\n      return instance.handler\n    }\n  }\n\n  if (isNumber(messageConfig.max) && instances.length >= messageConfig.max) {\n    return { close: () => undefined }\n  }\n\n  const instance = createMessage(normalized, context)\n\n  instances.push(instance)\n  return instance.handler\n}\n\nmessageTypes.forEach((type) => {\n  message[type] = (options = {}, appContext) => {\n    const normalized = normalizeOptions(options)\n    return message({ ...normalized, type }, appContext)\n  }\n})\n\nexport function closeAll(type?: MessageType): void {\n  for (const placement in placementInstances) {\n    if (hasOwn(placementInstances, placement)) {\n      // Create a copy of instances to avoid modification during iteration\n      const instances: MessageContext[] = [...placementInstances[placement]]\n      for (const instance of instances) {\n        if (!type || type === instance.props.type) {\n          instance.handler.close()\n        }\n      }\n    }\n  }\n}\n\nexport function closeAllByPlacement(placement: MessagePlacement) {\n  if (!placementInstances[placement]) return\n  // Create a copy of instances to avoid modification during iteration\n  const instances = [...placementInstances[placement]]\n  instances.forEach((instance) => instance.handler.close())\n}\n\nmessage.closeAll = closeAll\nmessage.closeAllByPlacement = closeAllByPlacement\nmessage._context = null\n\nexport default message as Message\n"], "names": ["isString", "isElement", "messageConfig", "MESSAGE_DEFAULT_PLACEMENT", "messagePlacement", "debugWarn", "isVNode", "isFunction", "messageDefaults", "isBoolean", "isNumber", "instance", "placementInstances", "render", "createVNode", "MessageConstructor", "isClient", "getOrCreatePlacementInstances", "messageTypes", "hasOwn"], "mappings": ";;;;;;;;;;;;;;AAoBA,IAAI,IAAI,GAAG,CAAC,CAAC;AACb,MAAM,iBAAiB,GAAG,CAAC,UAAU,KAAK;AAC1C,EAAE,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;AACvC,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC;AACxC,GAAG,MAAM,IAAIA,eAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;AAC5C,IAAI,IAAI,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;AAChE,IAAI,IAAI,CAACC,eAAS,CAAC,SAAS,CAAC,EAAE;AAE/B,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC;AAChC,KAAK;AACL,IAAI,UAAU,CAAC,QAAQ,GAAG,SAAS,CAAC;AACpC,GAAG;AACH,CAAC,CAAC;AACF,MAAM,kBAAkB,GAAG,CAAC,UAAU,KAAK;AAC3C,EAAE,IAAI,CAAC,UAAU,CAAC,SAAS,IAAID,eAAQ,CAACE,4BAAa,CAAC,SAAS,CAAC,IAAIA,4BAAa,CAAC,SAAS,EAAE;AAC7F,IAAI,UAAU,CAAC,SAAS,GAAGA,4BAAa,CAAC,SAAS,CAAC;AACnD,GAAG;AACH,EAAE,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE;AAC7B,IAAI,UAAU,CAAC,SAAS,GAAGC,mCAAyB,CAAC;AACrD,GAAG;AACH,EAAE,IAAI,CAACC,0BAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;AACxD,IAAIC,eAAS,CAAC,WAAW,EAAE,CAAC,mBAAmB,EAAE,UAAU,CAAC,SAAS,CAAC,mBAAmB,EAAEF,mCAAyB,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1H,IAAI,UAAU,CAAC,SAAS,GAAGA,mCAAyB,CAAC;AACrD,GAAG;AACH,CAAC,CAAC;AACF,MAAM,gBAAgB,GAAG,CAAC,MAAM,KAAK;AACrC,EAAE,MAAM,OAAO,GAAG,CAAC,MAAM,IAAIH,eAAQ,CAAC,MAAM,CAAC,IAAIM,WAAO,CAAC,MAAM,CAAC,IAAIC,iBAAU,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;AACtH,EAAE,MAAM,UAAU,GAAG;AACrB,IAAI,GAAGC,yBAAe;AACtB,IAAI,GAAG,OAAO;AACd,GAAG,CAAC;AACJ,EAAE,iBAAiB,CAAC,UAAU,CAAC,CAAC;AAChC,EAAE,kBAAkB,CAAC,UAAU,CAAC,CAAC;AACjC,EAAE,IAAIC,eAAS,CAACP,4BAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;AACjE,IAAI,UAAU,CAAC,QAAQ,GAAGA,4BAAa,CAAC,QAAQ,CAAC;AACjD,GAAG;AACH,EAAE,IAAIQ,cAAQ,CAACR,4BAAa,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC,QAAQ,KAAK,GAAG,EAAE;AACvE,IAAI,UAAU,CAAC,QAAQ,GAAGA,4BAAa,CAAC,QAAQ,CAAC;AACjD,GAAG;AACH,EAAE,IAAIQ,cAAQ,CAACR,4BAAa,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,MAAM,KAAK,EAAE,EAAE;AAClE,IAAI,UAAU,CAAC,MAAM,GAAGA,4BAAa,CAAC,MAAM,CAAC;AAC7C,GAAG;AACH,EAAE,IAAIO,eAAS,CAACP,4BAAa,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE;AACnE,IAAI,UAAU,CAAC,SAAS,GAAGA,4BAAa,CAAC,SAAS,CAAC;AACnD,GAAG;AACH,EAAE,IAAIO,eAAS,CAACP,4BAAa,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;AAC3D,IAAI,UAAU,CAAC,KAAK,GAAGA,4BAAa,CAAC,KAAK,CAAC;AAC3C,GAAG;AACH,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC;AACF,MAAM,YAAY,GAAG,CAACS,UAAQ,KAAK;AACnC,EAAE,MAAM,SAAS,GAAGA,UAAQ,CAAC,KAAK,CAAC,SAAS,IAAIR,mCAAyB,CAAC;AAC1E,EAAE,MAAM,SAAS,GAAGS,2BAAkB,CAAC,SAAS,CAAC,CAAC;AAClD,EAAE,MAAM,GAAG,GAAG,SAAS,CAAC,OAAO,CAACD,UAAQ,CAAC,CAAC;AAC1C,EAAE,IAAI,GAAG,KAAK,CAAC,CAAC;AAChB,IAAI,OAAO;AACX,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC3B,EAAE,MAAM,EAAE,OAAO,EAAE,GAAGA,UAAQ,CAAC;AAC/B,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC;AAClB,CAAC,CAAC;AACF,MAAM,aAAa,GAAG,CAAC,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,EAAE,OAAO,KAAK;AAC7D,EAAE,MAAM,EAAE,GAAG,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACjC,EAAE,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC;AACtC,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAClD,EAAE,MAAM,KAAK,GAAG;AAChB,IAAI,GAAG,OAAO;AACd,IAAI,EAAE;AACN,IAAI,OAAO,EAAE,MAAM;AACnB,MAAM,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,WAAW,EAAE,CAAC;AACnD,MAAM,YAAY,CAAC,QAAQ,CAAC,CAAC;AAC7B,KAAK;AACL,IAAI,SAAS,EAAE,MAAM;AACrB,MAAME,UAAM,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AAC9B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,KAAK,GAAGC,eAAW,CAACC,oBAAkB,EAAE,KAAK,EAAER,iBAAU,CAAC,KAAK,CAAC,OAAO,CAAC,IAAID,WAAO,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG;AAC7G,IAAI,OAAO,EAAEC,iBAAU,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,MAAM,KAAK,CAAC,OAAO;AAC5E,GAAG,GAAG,IAAI,CAAC,CAAC;AACZ,EAAE,KAAK,CAAC,UAAU,GAAG,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC;AACjD,EAAEM,UAAM,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AAC3B,EAAE,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC;AAC7B,EAAE,MAAM,OAAO,GAAG;AAClB,IAAI,KAAK,EAAE,MAAM;AACjB,MAAM,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;AACzB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,QAAQ,GAAG;AACnB,IAAI,EAAE;AACN,IAAI,KAAK;AACT,IAAI,EAAE;AACN,IAAI,OAAO;AACX,IAAI,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK;AAChC,GAAG,CAAC;AACJ,EAAE,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAC;AACG,MAAC,OAAO,GAAG,CAAC,OAAO,GAAG,EAAE,EAAE,OAAO,KAAK;AAC3C,EAAE,IAAI,CAACG,aAAQ;AACf,IAAI,OAAO,EAAE,KAAK,EAAE,MAAM,KAAK,CAAC,EAAE,CAAC;AACnC,EAAE,MAAM,UAAU,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAC/C,EAAE,MAAM,SAAS,GAAGC,sCAA6B,CAAC,UAAU,CAAC,SAAS,IAAId,mCAAyB,CAAC,CAAC;AACrG,EAAE,IAAI,UAAU,CAAC,QAAQ,IAAI,SAAS,CAAC,MAAM,EAAE;AAC/C,IAAI,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK;AACxD,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,MAAM,UAAU,CAAC,OAAO,CAAC;AACpF,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,SAAS,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC;AACrC,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;AAC7C,MAAM,OAAO,SAAS,CAAC,OAAO,CAAC;AAC/B,KAAK;AACL,GAAG;AACH,EAAE,IAAIO,cAAQ,CAACR,4BAAa,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC,MAAM,IAAIA,4BAAa,CAAC,GAAG,EAAE;AAC5E,IAAI,OAAO,EAAE,KAAK,EAAE,MAAM,KAAK,CAAC,EAAE,CAAC;AACnC,GAAG;AACH,EAAE,MAAMS,UAAQ,GAAG,aAAa,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AACtD,EAAE,SAAS,CAAC,IAAI,CAACA,UAAQ,CAAC,CAAC;AAC3B,EAAE,OAAOA,UAAQ,CAAC,OAAO,CAAC;AAC1B,EAAE;AACFO,sBAAY,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAC/B,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,EAAE,EAAE,UAAU,KAAK;AAChD,IAAI,MAAM,UAAU,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;AACjD,IAAI,OAAO,OAAO,CAAC,EAAE,GAAG,UAAU,EAAE,IAAI,EAAE,EAAE,UAAU,CAAC,CAAC;AACxD,GAAG,CAAC;AACJ,CAAC,CAAC,CAAC;AACI,SAAS,QAAQ,CAAC,IAAI,EAAE;AAC/B,EAAE,KAAK,MAAM,SAAS,IAAIN,2BAAkB,EAAE;AAC9C,IAAI,IAAIO,aAAM,CAACP,2BAAkB,EAAE,SAAS,CAAC,EAAE;AAC/C,MAAM,MAAM,SAAS,GAAG,CAAC,GAAGA,2BAAkB,CAAC,SAAS,CAAC,CAAC,CAAC;AAC3D,MAAM,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;AACxC,QAAQ,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;AACnD,UAAU,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;AACnC,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG;AACH,CAAC;AACM,SAAS,mBAAmB,CAAC,SAAS,EAAE;AAC/C,EAAE,IAAI,CAACA,2BAAkB,CAAC,SAAS,CAAC;AACpC,IAAI,OAAO;AACX,EAAE,MAAM,SAAS,GAAG,CAAC,GAAGA,2BAAkB,CAAC,SAAS,CAAC,CAAC,CAAC;AACvD,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;AAC5D,CAAC;AACD,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC5B,OAAO,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;AAClD,OAAO,CAAC,QAAQ,GAAG,IAAI;;;;;;"}