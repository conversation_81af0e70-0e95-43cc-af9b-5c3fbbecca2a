<template>
  <div class="profile-page">
    <div class="container">
      <div class="page-header">
        <h1>个人资料</h1>
        <p>管理您的账户信息</p>
      </div>
      
      <div class="profile-content">
        <div class="profile-form">
          <el-form
            ref="profileFormRef"
            :model="profileForm"
            :rules="profileRules"
            label-width="100px"
          >
            <el-form-item label="用户名" prop="username">
              <el-input v-model="profileForm.username" disabled />
            </el-form-item>
            
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="profileForm.email" disabled />
            </el-form-item>
            
            <el-form-item label="姓名" prop="full_name">
              <el-input v-model="profileForm.full_name" placeholder="请输入您的姓名" />
            </el-form-item>
            
            <el-form-item label="电话" prop="phone">
              <el-input v-model="profileForm.phone" placeholder="请输入您的电话" />
            </el-form-item>
            
            <el-form-item label="地址" prop="address">
              <el-input
                v-model="profileForm.address"
                type="textarea"
                :rows="3"
                placeholder="请输入您的地址"
              />
            </el-form-item>
            
            <el-form-item>
              <el-button
                type="primary"
                :loading="loading"
                @click="handleUpdate"
              >
                更新资料
              </el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <div class="profile-stats">
          <div class="stats-card">
            <h3>账户统计</h3>
            
            <div class="stat-item">
              <div class="stat-icon">📦</div>
              <div class="stat-info">
                <h4>总订单数</h4>
                <p>{{ orderStats.total }} 个</p>
              </div>
            </div>
            
            <div class="stat-item">
              <div class="stat-icon">✅</div>
              <div class="stat-info">
                <h4>已完成订单</h4>
                <p>{{ orderStats.delivered }} 个</p>
              </div>
            </div>
            
            <div class="stat-item">
              <div class="stat-icon">🚚</div>
              <div class="stat-info">
                <h4>配送中订单</h4>
                <p>{{ orderStats.shipping }} 个</p>
              </div>
            </div>
            
            <div class="stat-item">
              <div class="stat-icon">⏳</div>
              <div class="stat-info">
                <h4>待处理订单</h4>
                <p>{{ orderStats.pending }} 个</p>
              </div>
            </div>
            
            <div class="profile-actions">
              <router-link to="/orders" class="btn btn-primary">查看订单</router-link>
              <el-button @click="handleLogout">退出登录</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'

export default {
  name: 'Profile',
  setup() {
    const store = useStore()
    const router = useRouter()
    
    const profileFormRef = ref()
    const loading = ref(false)
    
    const profileForm = reactive({
      username: '',
      email: '',
      full_name: '',
      phone: '',
      address: ''
    })
    
    const profileRules = {
      full_name: [
        { max: 50, message: '姓名长度不能超过50个字符', trigger: 'blur' }
      ],
      phone: [
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
      ]
    }
    
    const user = computed(() => store.getters['auth/user'])
    const orderStats = computed(() => store.getters['orders/orderStats'])
    
    const loadUserData = () => {
      if (user.value) {
        profileForm.username = user.value.username || ''
        profileForm.email = user.value.email || ''
        profileForm.full_name = user.value.full_name || ''
        profileForm.phone = user.value.phone || ''
        profileForm.address = user.value.address || ''
      }
    }
    
    const handleUpdate = async () => {
      if (!profileFormRef.value) return
      
      try {
        await profileFormRef.value.validate()
        
        loading.value = true
        
        await store.dispatch('auth/updateProfile', {
          full_name: profileForm.full_name,
          phone: profileForm.phone,
          address: profileForm.address
        })
        
        store.dispatch('notifications/showNotification', {
          type: 'success',
          message: '资料更新成功'
        })
        
      } catch (error) {
        store.dispatch('notifications/showNotification', {
          type: 'error',
          message: error.message || '更新失败'
        })
      } finally {
        loading.value = false
      }
    }
    
    const resetForm = () => {
      loadUserData()
    }
    
    const handleLogout = async () => {
      try {
        await store.dispatch('auth/logout')
        router.push('/')
      } catch (error) {
        console.error('Logout error:', error)
      }
    }
    
    onMounted(() => {
      loadUserData()
      // 加载订单统计
      store.dispatch('orders/loadOrders')
    })
    
    return {
      profileFormRef,
      profileForm,
      profileRules,
      loading,
      orderStats,
      handleUpdate,
      resetForm,
      handleLogout
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-page {
  padding: 40px 0;
  
  .page-header {
    text-align: center;
    margin-bottom: 40px;
    
    h1 {
      font-size: 36px;
      font-weight: 800;
      color: var(--color-ink);
      margin-bottom: 8px;
    }
    
    p {
      font-size: 16px;
      color: #666;
    }
  }
  
  .profile-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto;
    
    .profile-form {
      @include card-style;
      
      .el-form {
        max-width: 500px;
      }
    }
    
    .profile-stats {
      .stats-card {
        @include card-style;
        
        h3 {
          font-size: 20px;
          font-weight: 700;
          color: var(--color-ink);
          margin-bottom: 24px;
          text-align: center;
        }
        
        .stat-item {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 16px 0;
          border-bottom: 1px solid #f0f0f0;
          
          &:last-child {
            border-bottom: none;
          }
          
          .stat-icon {
            font-size: 32px;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--color-sand);
            border-radius: 50%;
          }
          
          .stat-info {
            flex: 1;
            
            h4 {
              font-size: 16px;
              font-weight: 600;
              color: var(--color-ink);
              margin: 0 0 4px 0;
            }
            
            p {
              font-size: 18px;
              font-weight: 700;
              color: var(--color-primary);
              margin: 0;
            }
          }
        }
        
        .profile-actions {
          margin-top: 24px;
          display: flex;
          flex-direction: column;
          gap: 12px;
          
          .btn,
          .el-button {
            width: 100%;
          }
        }
      }
    }
  }
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: var(--color-ink);
}

:deep(.el-input__wrapper),
:deep(.el-textarea__inner) {
  border-radius: var(--radius-sm);
}

@include respond-to('mobile') {
  .profile-page {
    .profile-content {
      grid-template-columns: 1fr;
      gap: 20px;
      
      .profile-form {
        .el-form {
          max-width: none;
        }
      }
    }
  }
}
</style>
