version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15
    container_name: chocolate_postgres
    environment:
      POSTGRES_DB: chocolate_shop
      POSTGRES_USER: chocolate_user
      POSTGRES_PASSWORD: chocolate_pass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - chocolate_network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: chocolate_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - chocolate_network

  # Go API 服务
  api:
    build: .
    container_name: chocolate_api
    environment:
      PORT: 8080
      ENVIRONMENT: production
      DATABASE_URL: ******************************************************/chocolate_shop?sslmode=disable
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
    ports:
      - "8080:8080"
    depends_on:
      - postgres
      - redis
    networks:
      - chocolate_network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  chocolate_network:
    driver: bridge
