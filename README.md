# X Oberon 巧克力商店

一个基于 Go + Gin + MySQL + Redis 构建的现代化巧克力在线商店，保持原有的温暖活泼设计风格。

## 🍫 项目特色

- **精美界面**: 保持原有的温暖色调和活泼设计
- **现代技术栈**: Go + Gin + PostgreSQL + Redis
- **完整功能**: 用户认证、产品展示、购物车、订单管理
- **响应式设计**: 支持桌面和移动设备
- **实时交互**: 基于 JavaScript 的动态用户界面

## 🛠️ 技术栈

### 后端
- **Go 1.21+** - 编程语言
- **Gin** - Web框架
- **PostgreSQL** - 主数据库
- **Redis** - 缓存和会话存储
- **SQLX** - SQL扩展库
- **JWT** - 用户认证
- **Docker** - 容器化部署

### 前端
- **HTML5** - 页面结构
- **CSS3** - 样式设计（保持原有风格）
- **JavaScript ES6+** - 交互逻辑
- **Fetch API** - 后端通信

## 🚀 快速开始

### 方式一：使用启动脚本（推荐）

1. 确保已安装 Docker 和 Docker Compose
2. 双击运行 `start.bat`
3. 等待服务启动完成
4. 浏览器会自动打开前端页面

### 方式二：手动启动

#### 启动后端服务

```bash
cd backend
docker-compose up -d
```

#### 打开前端页面

直接在浏览器中打开 `index.html` 文件

## 📱 功能特性

### 用户功能
- ✅ 用户注册/登录
- ✅ 个人资料管理
- ✅ 浏览巧克力产品
- ✅ 搜索产品
- ✅ 购物车管理
- ✅ 下单购买
- ✅ 订单历史查看

### 产品功能
- ✅ 产品分类展示
- ✅ 产品详情查看
- ✅ 库存管理
- ✅ 价格展示
- ✅ 图片展示

### 系统功能
- ✅ JWT 认证
- ✅ Redis 缓存
- ✅ 数据库事务
- ✅ 错误处理
- ✅ 日志记录

## 🎨 设计特色

- **温暖色调**: 使用黄色主色调，营造温馨感
- **巧克力元素**: 巧克力盒子设计，食物图标
- **现代布局**: 卡片式设计，响应式网格
- **动画效果**: 滚动显示动画，悬停效果
- **用户友好**: 直观的操作界面，清晰的信息展示

## 📊 数据库设计

### 主要数据表
- `users` - 用户信息
- `products` - 产品信息
- `orders` - 订单信息
- `order_items` - 订单项
- `cart_items` - 购物车项

### 示例产品数据
- 经典黑巧克力 (70%可可)
- 丝滑牛奶巧克力
- 纯白巧克力
- 榛子巧克力

## 🔧 开发说明

### 后端 API 端点

```
POST /api/v1/auth/register    # 用户注册
POST /api/v1/auth/login       # 用户登录
GET  /api/v1/products         # 获取产品列表
GET  /api/v1/products/:id     # 获取产品详情
POST /api/v1/cart/add         # 添加到购物车
GET  /api/v1/cart             # 获取购物车
POST /api/v1/orders           # 创建订单
GET  /api/v1/orders           # 获取订单列表
```

### 前端架构

```
assets/
├── css/
│   └── styles.css           # 样式文件（保持原有设计）
├── js/
│   └── app.js              # 主要交互逻辑
└── images/                 # 图片资源

backend/
├── main.go                 # 应用入口
├── config/                 # 配置管理
├── internal/
│   ├── api/               # 路由定义
│   ├── handlers/          # 请求处理器
│   ├── models/            # 数据模型
│   ├── database/          # 数据库操作
│   └── redis/             # 缓存操作
└── docker-compose.yml     # 容器编排
```

## 🌐 访问地址

- **前端页面**: `file:///path/to/index.html`
- **后端API**: `http://localhost:8080`
- **API文档**: `http://localhost:8080/health`

## 🔒 安全特性

- JWT Token 认证
- 密码 bcrypt 加密
- CORS 跨域支持
- 输入数据验证
- SQL 注入防护

## 📦 部署说明

### 开发环境
1. 使用 `start.bat` 快速启动
2. 前端直接打开 HTML 文件
3. 后端使用 Docker Compose

### 生产环境
1. 配置环境变量
2. 使用 HTTPS
3. 配置反向代理
4. 数据库备份策略

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 🎯 未来计划

- [ ] 支付集成
- [ ] 邮件通知
- [ ] 管理后台
- [ ] 移动端 App
- [ ] 多语言支持

---

**X Oberon 巧克力商店** - 让每一块巧克力都充满爱意 🍫❤️
