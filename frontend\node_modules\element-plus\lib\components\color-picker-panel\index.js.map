{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/color-picker-panel/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport ColorPickerPanel from './src/color-picker-panel.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElColorPickerPanel: SFCWithInstall<typeof ColorPickerPanel> =\n  withInstall(ColorPickerPanel)\nexport default ElColorPickerPanel\n\nexport * from './src/color-picker-panel'\n"], "names": ["withInstall", "ColorPickerPanel"], "mappings": ";;;;;;;;AAEY,MAAC,kBAAkB,GAAGA,mBAAW,CAACC,6BAAgB;;;;;;;;;"}