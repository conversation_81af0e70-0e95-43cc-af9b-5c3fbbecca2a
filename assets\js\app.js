// X Oberon 巧克力商店 - 前端交互
class ChocolateShop {
  constructor() {
    this.apiBase = 'http://localhost:8080/api/v1';
    this.token = localStorage.getItem('auth_token');
    this.cart = [];
    this.init();
  }

  init() {
    this.setupRevealAnimation();
    this.setupEventListeners();
    this.loadProducts();
    this.updateCartCount();
  }

  // 设置滚动动画
  setupRevealAnimation() {
    const observer = new IntersectionObserver(entries => {
      for (const entry of entries) {
        if (entry.isIntersecting) {
          entry.target.classList.add('in-view');
          observer.unobserve(entry.target);
        }
      }
    }, { threshold: 0.12 });

    const revealEls = document.querySelectorAll('.reveal');
    revealEls.forEach(el => observer.observe(el));
  }

  // 设置事件监听器
  setupEventListeners() {
    // 登录按钮
    const loginBtn = document.getElementById('loginBtn');
    if (loginBtn) {
      loginBtn.addEventListener('click', () => this.showLoginModal());
    }

    // 购物车按钮
    const cartBtn = document.getElementById('cartBtn');
    if (cartBtn) {
      cartBtn.addEventListener('click', () => this.showCart());
    }

    // 搜索按钮
    const searchBtn = document.getElementById('searchBtn');
    if (searchBtn) {
      searchBtn.addEventListener('click', () => this.searchProducts());
    }
  }

  // API请求方法
  async apiRequest(endpoint, options = {}) {
    const url = `${this.apiBase}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    if (this.token) {
      config.headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'API request failed');
      }

      return data;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  // 加载产品
  async loadProducts() {
    try {
      const response = await this.apiRequest('/products?page=1&page_size=6');
      this.displayProducts(response.data.data);
    } catch (error) {
      console.error('Failed to load products:', error);
      this.showNotification('加载产品失败', 'error');
    }
  }

  // 显示产品
  displayProducts(products) {
    const container = document.querySelector('.grid.grid-3');
    if (!container) return;

    container.innerHTML = products.map(product => `
      <article class="product-card reveal" data-product-id="${product.id}">
        <div class="product-art" style="background: linear-gradient(135deg, #8B4513, #D2691E);"></div>
        <div class="product-title">${product.name}</div>
        <div class="badge">${product.category}</div>
        <div class="price">¥${product.price.toFixed(2)}</div>
        <button class="btn btn-primary" onclick="shop.addToCart(${product.id})">
          加入购物车
        </button>
      </article>
    `).join('');
  }

  // 添加到购物车
  async addToCart(productId, quantity = 1) {
    if (!this.token) {
      this.showLoginModal();
      return;
    }

    try {
      await this.apiRequest('/cart/add', {
        method: 'POST',
        body: JSON.stringify({
          product_id: productId,
          quantity: quantity
        })
      });

      this.updateCartCount();
      this.showNotification('已添加到购物车', 'success');
    } catch (error) {
      console.error('Add to cart failed:', error);
      this.showNotification('添加到购物车失败', 'error');
    }
  }

  // 更新购物车数量
  async updateCartCount() {
    if (!this.token) return;

    try {
      const response = await this.apiRequest('/cart');
      const cartCount = response.data.reduce((total, item) => total + item.quantity, 0);

      const cartCountElement = document.getElementById('cartCount');
      if (cartCountElement) {
        cartCountElement.textContent = cartCount;
      }
    } catch (error) {
      console.error('Failed to update cart count:', error);
    }
  }

  // 显示登录模态框
  showLoginModal() {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
      <div class="modal">
        <h3>登录 X Oberon</h3>
        <form id="loginForm">
          <input type="text" id="username" placeholder="用户名" required>
          <input type="password" id="password" placeholder="密码" required>
          <div class="modal-actions">
            <button type="submit" class="btn btn-primary">登录</button>
            <button type="button" class="btn" onclick="this.closest('.modal-overlay').remove()">取消</button>
          </div>
        </form>
      </div>
    `;

    document.body.appendChild(modal);

    const loginForm = document.getElementById('loginForm');
    loginForm.addEventListener('submit', (e) => this.handleLogin(e));
  }

  // 处理登录
  async handleLogin(e) {
    e.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    try {
      const response = await this.apiRequest('/auth/login', {
        method: 'POST',
        body: JSON.stringify({ username, password })
      });

      this.token = response.data.token;
      localStorage.setItem('auth_token', this.token);

      // 更新UI
      const loginBtn = document.getElementById('loginBtn');
      if (loginBtn) {
        loginBtn.textContent = response.data.user.username;
        loginBtn.onclick = () => this.logout();
      }

      // 关闭模态框
      document.querySelector('.modal-overlay').remove();

      this.updateCartCount();
      this.showNotification('登录成功', 'success');
    } catch (error) {
      console.error('Login failed:', error);
      this.showNotification('登录失败: ' + error.message, 'error');
    }
  }

  // 登出
  async logout() {
    try {
      await this.apiRequest('/auth/logout', { method: 'POST' });
    } catch (error) {
      console.error('Logout error:', error);
    }

    this.token = null;
    localStorage.removeItem('auth_token');

    const loginBtn = document.getElementById('loginBtn');
    if (loginBtn) {
      loginBtn.textContent = '登录';
      loginBtn.onclick = () => this.showLoginModal();
    }

    const cartCountElement = document.getElementById('cartCount');
    if (cartCountElement) {
      cartCountElement.textContent = '0';
    }

    this.showNotification('已登出', 'success');
  }

  // 显示通知
  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 8px;
      color: white;
      font-weight: 600;
      z-index: 10000;
      transform: translateX(100%);
      transition: transform 0.3s ease;
      background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.style.transform = 'translateX(0)';
    }, 100);

    setTimeout(() => {
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }

  // 显示购物车
  async showCart() {
    if (!this.token) {
      this.showLoginModal();
      return;
    }

    try {
      const response = await this.apiRequest('/cart');
      this.displayCart(response.data);
    } catch (error) {
      console.error('Failed to load cart:', error);
      this.showNotification('加载购物车失败', 'error');
    }
  }

  // 显示购物车内容
  displayCart(cartItems) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
    `;

    const total = cartItems.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);

    modal.innerHTML = `
      <div class="modal" style="background: white; padding: 30px; border-radius: 12px; max-width: 500px; width: 90%; max-height: 80vh; overflow-y: auto;">
        <h3 style="margin-top: 0; color: var(--color-ink);">🛒 购物车</h3>
        <div class="cart-items">
          ${cartItems.length === 0 ? '<p>购物车为空</p>' :
            cartItems.map(item => `
              <div class="cart-item" style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #eee;">
                <div>
                  <strong>${item.product.name}</strong><br>
                  <small>数量: ${item.quantity} × ¥${item.product.price.toFixed(2)}</small>
                </div>
                <div style="text-align: right;">
                  <div>¥${(item.product.price * item.quantity).toFixed(2)}</div>
                  <button onclick="shop.removeFromCart(${item.id})" style="background: #ef4444; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;">删除</button>
                </div>
              </div>
            `).join('')
          }
        </div>
        ${cartItems.length > 0 ? `
          <div class="cart-total" style="margin: 20px 0; padding: 15px; background: var(--color-sand); border-radius: 8px;">
            <strong style="font-size: 18px;">总计: ¥${total.toFixed(2)}</strong>
          </div>
          <div class="modal-actions" style="display: flex; gap: 10px; justify-content: flex-end;">
            <button class="btn btn-primary" onclick="shop.checkout()" style="padding: 10px 20px;">结账</button>
            <button class="btn" onclick="this.closest('.modal-overlay').remove()" style="padding: 10px 20px;">关闭</button>
          </div>
        ` : `
          <div class="modal-actions" style="display: flex; justify-content: flex-end; margin-top: 20px;">
            <button class="btn" onclick="this.closest('.modal-overlay').remove()" style="padding: 10px 20px;">关闭</button>
          </div>
        `}
      </div>
    `;

    document.body.appendChild(modal);
  }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
  window.shop = new ChocolateShop();
});


