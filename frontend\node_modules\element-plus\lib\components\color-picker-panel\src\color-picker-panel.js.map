{"version": 3, "file": "color-picker-panel.js", "sources": ["../../../../../../packages/components/color-picker-panel/src/color-picker-panel.ts"], "sourcesContent": ["import { isNil } from 'lodash-unified'\nimport { buildProps, definePropType, isString } from '@element-plus/utils'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\n\nimport type {\n  ComputedRef,\n  ExtractPropTypes,\n  InjectionKey,\n  __ExtractPublicPropTypes,\n} from 'vue'\nimport type ColorPickerPanel from './color-picker-panel.vue'\nimport type Color from './utils/color'\n\nexport const colorPickerPanelProps = buildProps({\n  /**\n   * @description binding value\n   */\n  modelValue: {\n    type: definePropType<string | null>(String),\n    default: undefined,\n  },\n  /**\n   * @description whether the color picker is bordered\n   */\n  border: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether to display the alpha slider\n   */\n  showAlpha: Boolean,\n  /**\n   * @description color format of v-model\n   */\n  colorFormat: String,\n  /**\n   * @description whether to disable the color picker\n   */\n  disabled: Boolean,\n  /**\n   * @description predefined color options\n   */\n  predefine: {\n    type: definePropType<string[]>(Array),\n  },\n} as const)\nexport const colorPickerPanelEmits = {\n  [UPDATE_MODEL_EVENT]: (val: string | null) => isString(val) || isNil(val),\n}\n\nexport type ColorPickerPanelProps = ExtractPropTypes<\n  typeof colorPickerPanelProps\n>\nexport type ColorPickerPanelPropsPublic = __ExtractPublicPropTypes<\n  typeof colorPickerPanelProps\n>\nexport type ColorPickerPanelEmits = typeof colorPickerPanelEmits\nexport type ColorPickerPanelInstance = InstanceType<typeof ColorPickerPanel> &\n  unknown\n\nexport interface ColorPickerPanelContext {\n  currentColor: ComputedRef<string>\n}\n\nexport interface CommonColorContext {\n  color: Color\n}\n\nexport const ROOT_COMMON_COLOR_INJECTION_KEY: InjectionKey<CommonColorContext> =\n  Symbol('colorCommonPickerKey')\nexport const colorPickerPanelContextKey: InjectionKey<ColorPickerPanelContext> =\n  Symbol('colorPickerPanelContextKey')\n"], "names": ["buildProps", "definePropType", "UPDATE_MODEL_EVENT", "isString", "isNil"], "mappings": ";;;;;;;;;AAGY,MAAC,qBAAqB,GAAGA,kBAAU,CAAC;AAChD,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAEC,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAEA,sBAAc,CAAC,KAAK,CAAC;AAC/B,GAAG;AACH,CAAC,EAAE;AACS,MAAC,qBAAqB,GAAG;AACrC,EAAE,CAACC,wBAAkB,GAAG,CAAC,GAAG,KAAKC,eAAQ,CAAC,GAAG,CAAC,IAAIC,mBAAK,CAAC,GAAG,CAAC;AAC5D,EAAE;AACU,MAAC,+BAA+B,GAAG,MAAM,CAAC,sBAAsB,EAAE;AAClE,MAAC,0BAA0B,GAAG,MAAM,CAAC,4BAA4B;;;;;;;"}