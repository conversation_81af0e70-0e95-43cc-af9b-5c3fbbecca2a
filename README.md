# X Oberon 巧克力商店 🍫

一个完整的前后端分离巧克力电商平台，采用现代化技术栈构建，保持温暖活泼的设计风格。

## 🎯 项目特色

- **🎨 精美设计**: 保持原有的温暖色调和巧克力主题
- **🔧 前后端分离**: Vue.js + Go 完全分离的现代架构
- **⚡ 高性能**: Redis缓存 + PostgreSQL + 响应式设计
- **📱 移动友好**: 完全响应式，支持所有设备
- **🛒 完整电商**: 购物车、订单、支付、评价、优惠券等完整功能

## 🍫 项目特色

- **精美界面**: 保持原有的温暖色调和活泼设计
- **现代技术栈**: Go + Gin + PostgreSQL + Redis
- **完整功能**: 用户认证、产品展示、购物车、订单管理
- **响应式设计**: 支持桌面和移动设备
- **实时交互**: 基于 JavaScript 的动态用户界面

## 🛠️ 技术栈

### 前端 (frontend/)
- **Vue.js 3** - 渐进式前端框架
- **Vue Router 4** - 单页面路由
- **Vuex 4** - 状态管理
- **Element Plus** - UI组件库
- **Axios** - HTTP客户端
- **SCSS** - CSS预处理器
- **Socket.io** - 实时通信

### 后端 (backend/)
- **Go 1.21+** - 高性能编程语言
- **Gin** - 轻量级Web框架
- **PostgreSQL** - 关系型数据库
- **Redis** - 内存缓存数据库
- **SQLX** - SQL扩展库
- **JWT** - 用户认证
- **Docker** - 容器化部署
- **WebSocket** - 实时通信

## 🚀 快速开始

### 环境要求

- **Node.js 16+** (前端开发)
- **Go 1.21+** (后端开发)
- **Docker & Docker Compose** (数据库服务)
- **Git** (版本控制)

### 方式一：一键启动（推荐）

```bash
# Windows用户
.\start.bat

# 或者手动执行以下步骤
```

### 方式二：分步启动

#### 1. 启动后端服务

```bash
# 启动数据库服务
cd backend
docker-compose up -d

# 启动Go API服务
go run main.go
```

#### 2. 启动前端服务

```bash
# 安装依赖
cd frontend
npm install

# 启动开发服务器
npm run serve
```

### 访问地址

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8080
- **API文档**: http://localhost:8080/health

## 📱 功能特性

### 用户功能
- ✅ 用户注册/登录
- ✅ 个人资料管理
- ✅ 浏览巧克力产品
- ✅ 搜索产品
- ✅ 购物车管理
- ✅ 下单购买
- ✅ 订单历史查看

### 产品功能
- ✅ 产品分类展示
- ✅ 产品详情查看
- ✅ 库存管理
- ✅ 价格展示
- ✅ 图片展示

### 系统功能
- ✅ JWT 认证
- ✅ Redis 缓存
- ✅ 数据库事务
- ✅ 错误处理
- ✅ 日志记录

## 🎨 设计特色

- **温暖色调**: 使用黄色主色调，营造温馨感
- **巧克力元素**: 巧克力盒子设计，食物图标
- **现代布局**: 卡片式设计，响应式网格
- **动画效果**: 滚动显示动画，悬停效果
- **用户友好**: 直观的操作界面，清晰的信息展示

## 📊 数据库设计

### 主要数据表
- `users` - 用户信息
- `products` - 产品信息
- `orders` - 订单信息
- `order_items` - 订单项
- `cart_items` - 购物车项

### 示例产品数据
- 经典黑巧克力 (70%可可)
- 丝滑牛奶巧克力
- 纯白巧克力
- 榛子巧克力

## 🔧 开发说明

### 后端 API 端点

```
POST /api/v1/auth/register    # 用户注册
POST /api/v1/auth/login       # 用户登录
GET  /api/v1/products         # 获取产品列表
GET  /api/v1/products/:id     # 获取产品详情
POST /api/v1/cart/add         # 添加到购物车
GET  /api/v1/cart             # 获取购物车
POST /api/v1/orders           # 创建订单
GET  /api/v1/orders           # 获取订单列表
```

### 前端架构

```
assets/
├── css/
│   └── styles.css           # 样式文件（保持原有设计）
├── js/
│   └── app.js              # 主要交互逻辑
└── images/                 # 图片资源

backend/
├── main.go                 # 应用入口
├── config/                 # 配置管理
├── internal/
│   ├── api/               # 路由定义
│   ├── handlers/          # 请求处理器
│   ├── models/            # 数据模型
│   ├── database/          # 数据库操作
│   └── redis/             # 缓存操作
└── docker-compose.yml     # 容器编排
```

## 🌐 访问地址

- **前端页面**: `file:///path/to/index.html`
- **后端API**: `http://localhost:8080`
- **API文档**: `http://localhost:8080/health`

## 🔒 安全特性

- JWT Token 认证
- 密码 bcrypt 加密
- CORS 跨域支持
- 输入数据验证
- SQL 注入防护

## 📦 部署说明

### 开发环境
1. 使用 `start.bat` 快速启动
2. 前端直接打开 HTML 文件
3. 后端使用 Docker Compose

### 生产环境
1. 配置环境变量
2. 使用 HTTPS
3. 配置反向代理
4. 数据库备份策略

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 🎯 未来计划

- [ ] 支付集成
- [ ] 邮件通知
- [ ] 管理后台
- [ ] 移动端 App
- [ ] 多语言支持

---

**X Oberon 巧克力商店** - 让每一块巧克力都充满爱意 🍫❤️
