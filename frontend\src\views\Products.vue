<template>
  <div class="products-page">
    <div class="container">
      <div class="page-header">
        <h1>巧克力产品</h1>
        <p>精选优质巧克力，每一块都是匠心之作</p>
      </div>
      
      <!-- 筛选和搜索 -->
      <div class="filters">
        <div class="category-filters">
          <el-button
            :type="selectedCategory === '' ? 'primary' : ''"
            @click="filterByCategory('')"
          >
            全部
          </el-button>
          <el-button
            v-for="category in categories"
            :key="category"
            :type="selectedCategory === category ? 'primary' : ''"
            @click="filterByCategory(category)"
          >
            {{ category }}
          </el-button>
        </div>
        
        <div class="search-filter">
          <el-input
            v-model="searchQuery"
            placeholder="搜索巧克力..."
            @keyup.enter="handleSearch"
            clearable
          >
            <template #append>
              <el-button @click="handleSearch">
                <el-icon><Search /></el-icon>
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
      
      <!-- 产品列表 -->
      <div v-loading="loading" class="products-section">
        <div v-if="products.length === 0 && !loading" class="empty-state">
          <el-icon size="64" color="#ccc">
            <Box />
          </el-icon>
          <p>暂无产品</p>
        </div>
        
        <div v-else class="grid grid-4">
          <ProductCard
            v-for="product in products"
            :key="product.id"
            :product="product"
            class="reveal"
          />
        </div>
        
        <!-- 分页 -->
        <div v-if="pagination.totalPages > 1" class="pagination">
          <el-pagination
            v-model:current-page="pagination.page"
            :page-size="pagination.pageSize"
            :total="pagination.total"
            layout="prev, pager, next, jumper"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { Search, Box } from '@element-plus/icons-vue'
import ProductCard from '@/components/Product/ProductCard.vue'

export default {
  name: 'Products',
  components: {
    ProductCard,
    Search,
    Box
  },
  setup() {
    const store = useStore()
    const route = useRoute()
    const router = useRouter()
    
    const searchQuery = ref(route.query.search || '')
    const selectedCategory = ref(route.query.category || '')
    
    const products = computed(() => store.getters['products/products'])
    const categories = computed(() => store.getters['products/categories'])
    const loading = computed(() => store.getters['products/loading'])
    const pagination = computed(() => store.getters['products/pagination'])
    
    const loadProducts = async () => {
      try {
        const params = {
          page: pagination.value.page,
          page_size: pagination.value.pageSize
        }
        
        if (searchQuery.value) {
          params.q = searchQuery.value
          await store.dispatch('products/searchProducts', params)
        } else if (selectedCategory.value) {
          await store.dispatch('products/loadProductsByCategory', {
            category: selectedCategory.value,
            params
          })
        } else {
          await store.dispatch('products/loadProducts', params)
        }
      } catch (error) {
        store.dispatch('notifications/showNotification', {
          type: 'error',
          message: error.message
        })
      }
    }
    
    const handleSearch = () => {
      router.push({
        query: { ...route.query, search: searchQuery.value, page: 1 }
      })
    }
    
    const filterByCategory = (category) => {
      selectedCategory.value = category
      router.push({
        query: { ...route.query, category, page: 1 }
      })
    }
    
    const handlePageChange = (page) => {
      router.push({
        query: { ...route.query, page }
      })
    }
    
    // 监听路由变化
    watch(() => route.query, (newQuery) => {
      searchQuery.value = newQuery.search || ''
      selectedCategory.value = newQuery.category || ''
      
      const page = parseInt(newQuery.page) || 1
      store.commit('products/SET_PAGINATION', { page })
      
      loadProducts()
    }, { immediate: true })
    
    onMounted(() => {
      // 初始化滚动动画
      const observer = new IntersectionObserver(entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('in-view')
          }
        })
      }, { threshold: 0.1 })
      
      setTimeout(() => {
        document.querySelectorAll('.reveal').forEach(el => {
          observer.observe(el)
        })
      }, 100)
    })
    
    return {
      searchQuery,
      selectedCategory,
      products,
      categories,
      loading,
      pagination,
      handleSearch,
      filterByCategory,
      handlePageChange
    }
  }
}
</script>

<style lang="scss" scoped>
.products-page {
  padding: 40px 0;
  
  .page-header {
    text-align: center;
    margin-bottom: 40px;
    
    h1 {
      font-size: 36px;
      font-weight: 800;
      color: var(--color-ink);
      margin-bottom: 16px;
    }
    
    p {
      font-size: 18px;
      color: #666;
    }
  }
  
  .filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    gap: 20px;
    
    .category-filters {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }
    
    .search-filter {
      width: 300px;
    }
  }
  
  .products-section {
    min-height: 400px;
  }
  
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    
    p {
      margin-top: 20px;
      color: #666;
      font-size: 16px;
    }
  }
  
  .pagination {
    display: flex;
    justify-content: center;
    margin-top: 40px;
  }
}

@include respond-to('mobile') {
  .products-page {
    .filters {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;
      
      .search-filter {
        width: 100%;
      }
    }
  }
}
</style>
