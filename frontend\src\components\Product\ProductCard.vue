<template>
  <div class="product-card">
    <div class="product-image" @click="goToDetail">
      <img v-if="product.image_url" :src="product.image_url" :alt="product.name" />
      <div v-else class="placeholder-image">
        <el-icon size="48"><Picture /></el-icon>
      </div>
      
      <!-- 收藏按钮 -->
      <div class="favorite-btn" @click.stop="toggleFavorite">
        <el-icon :class="{ 'is-favorite': isFavorite }">
          <Star />
        </el-icon>
      </div>
      
      <!-- 库存状态 -->
      <div v-if="product.stock_quantity <= 0" class="stock-status out-of-stock">
        缺货
      </div>
      <div v-else-if="product.stock_quantity <= 10" class="stock-status low-stock">
        仅剩 {{ product.stock_quantity }} 件
      </div>
    </div>
    
    <div class="product-info">
      <div class="product-category">{{ product.category }}</div>
      <h3 class="product-name" @click="goToDetail">{{ product.name }}</h3>
      <p class="product-description">{{ product.description }}</p>
      
      <div class="product-rating" v-if="product.rating">
        <el-rate
          v-model="product.rating"
          disabled
          show-score
          text-color="#ff9900"
          score-template="{value}"
        />
        <span class="review-count">({{ product.review_count || 0 }})</span>
      </div>
      
      <div class="product-footer">
        <div class="product-price">
          <span class="current-price">¥{{ product.price.toFixed(2) }}</span>
          <span v-if="product.original_price && product.original_price > product.price" class="original-price">
            ¥{{ product.original_price.toFixed(2) }}
          </span>
        </div>
        
        <div class="product-actions">
          <el-button
            type="primary"
            size="small"
            @click="addToCart"
            :disabled="product.stock_quantity <= 0"
            :loading="adding"
          >
            <el-icon><ShoppingCart /></el-icon>
            加入购物车
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { Picture, Star, ShoppingCart } from '@element-plus/icons-vue'

export default {
  name: 'ProductCard',
  components: {
    Picture,
    Star,
    ShoppingCart
  },
  props: {
    product: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const store = useStore()
    const router = useRouter()
    
    const adding = ref(false)
    const isFavorite = ref(false) // 这里可以从store获取收藏状态
    
    const isAuthenticated = computed(() => store.getters['auth/isAuthenticated'])
    
    const goToDetail = () => {
      router.push(`/products/${props.product.id}`)
    }
    
    const addToCart = async () => {
      if (!isAuthenticated.value) {
        store.dispatch('notifications/showNotification', {
          type: 'warning',
          message: '请先登录'
        })
        router.push('/login')
        return
      }
      
      adding.value = true
      try {
        await store.dispatch('cart/addToCart', {
          productId: props.product.id,
          quantity: 1
        })
      } catch (error) {
        console.error('Add to cart error:', error)
      } finally {
        adding.value = false
      }
    }
    
    const toggleFavorite = () => {
      if (!isAuthenticated.value) {
        store.dispatch('notifications/showNotification', {
          type: 'warning',
          message: '请先登录'
        })
        router.push('/login')
        return
      }
      
      // 这里可以实现收藏功能
      isFavorite.value = !isFavorite.value
      
      store.dispatch('notifications/showNotification', {
        type: 'success',
        message: isFavorite.value ? '已添加到收藏' : '已取消收藏'
      })
    }
    
    return {
      adding,
      isFavorite,
      goToDetail,
      addToCart,
      toggleFavorite
    }
  }
}
</script>

<style lang="scss" scoped>
.product-card {
  @include card-style;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-2);
  }
  
  .product-image {
    position: relative;
    height: 200px;
    border-radius: var(--radius-md);
    overflow: hidden;
    margin-bottom: 16px;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .placeholder-image {
      width: 100%;
      height: 100%;
      @include chocolate-gradient;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }
    
    .favorite-btn {
      position: absolute;
      top: 12px;
      right: 12px;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      
      &:hover {
        background: white;
        transform: scale(1.1);
      }
      
      .el-icon {
        color: #ccc;
        transition: color 0.2s ease;
        
        &.is-favorite {
          color: #ff6b6b;
        }
      }
    }
    
    .stock-status {
      position: absolute;
      bottom: 12px;
      left: 12px;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 600;
      
      &.out-of-stock {
        background: var(--color-error);
        color: white;
      }
      
      &.low-stock {
        background: var(--color-warning);
        color: white;
      }
    }
  }
  
  .product-info {
    .product-category {
      font-size: 12px;
      color: var(--color-primary);
      font-weight: 600;
      text-transform: uppercase;
      margin-bottom: 4px;
    }
    
    .product-name {
      font-size: 18px;
      font-weight: 700;
      color: var(--color-ink);
      margin-bottom: 8px;
      cursor: pointer;
      transition: color 0.2s ease;
      
      &:hover {
        color: var(--color-primary);
      }
    }
    
    .product-description {
      font-size: 14px;
      color: #666;
      line-height: 1.4;
      margin-bottom: 12px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .product-rating {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;
      
      .review-count {
        font-size: 12px;
        color: #999;
      }
    }
    
    .product-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .product-price {
        .current-price {
          font-size: 20px;
          font-weight: 700;
          color: var(--color-primary);
        }
        
        .original-price {
          font-size: 14px;
          color: #999;
          text-decoration: line-through;
          margin-left: 8px;
        }
      }
      
      .product-actions {
        .el-button {
          border-radius: 20px;
        }
      }
    }
  }
}

@include respond-to('mobile') {
  .product-card {
    .product-footer {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
      
      .product-actions {
        .el-button {
          width: 100%;
        }
      }
    }
  }
}
</style>
