package redis

import (
	"context"
	"log"
	"time"

	"github.com/go-redis/redis/v8"
)

var ctx = context.Background()

func Init(redisURL string) *redis.Client {
	opt, err := redis.ParseURL(redisURL)
	if err != nil {
		log.Fatal("Failed to parse Redis URL:", err)
	}

	rdb := redis.NewClient(opt)

	// 测试连接
	_, err = rdb.Ping(ctx).Result()
	if err != nil {
		log.Fatal("Failed to connect to Redis:", err)
	}

	log.Println("Redis connected successfully")
	return rdb
}

// 缓存产品信息
func CacheProduct(rdb *redis.Client, productID string, data interface{}) error {
	return rdb.Set(ctx, "product:"+productID, data, 30*time.Minute).Err()
}

// 获取缓存的产品信息
func GetCachedProduct(rdb *redis.Client, productID string) (string, error) {
	return rdb.Get(ctx, "product:"+productID).Result()
}

// 缓存用户会话
func CacheUserSession(rdb *redis.Client, token string, userID int) error {
	return rdb.Set(ctx, "session:"+token, userID, 24*time.Hour).Err()
}

// 获取用户会话
func GetUserSession(rdb *redis.Client, token string) (string, error) {
	return rdb.Get(ctx, "session:"+token).Result()
}

// 删除用户会话
func DeleteUserSession(rdb *redis.Client, token string) error {
	return rdb.Del(ctx, "session:"+token).Err()
}

// 缓存热门产品
func CacheHotProducts(rdb *redis.Client, products interface{}) error {
	return rdb.Set(ctx, "hot_products", products, 1*time.Hour).Err()
}

// 获取热门产品
func GetHotProducts(rdb *redis.Client) (string, error) {
	return rdb.Get(ctx, "hot_products").Result()
}
