<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>美食外卖 · 首页</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/styles.css" />
  </head>
  <body>
    <header class="header">
      <div class="container nav">
        <a class="brand" href="index.html">
          <span class="brand-mark">🍽️</span>
          <span>美食外卖</span>
        </a>
        <nav class="nav-menu">
          <ul>
            <li><a href="#restaurants">商家</a></li>
            <li><a href="#categories">分类</a></li>
            <li><a href="#about">关于我们</a></li>
            <li><a href="#contact">联系</a></li>
          </ul>
        </nav>
        <div class="nav-actions">
          <button class="btn-cart" id="cartBtn">
            <span>🛒</span>
            <span class="cart-count" id="cartCount">0</span>
          </button>
          <button class="btn btn-primary" id="loginBtn">登录</button>
        </div>
      </div>
    </header>

    <main>
      <section class="hero">
        <div class="container hero-inner">
          <div class="reveal">
            <h1>XiangYueYuan<br />来自重庆 · 软件技术专业</h1>
            <p>爱好美学与 UI 设计，Go 语言学习者。项目 BlueBell - Go Web 论坛系统（Gin + MySQL + Redis + Vue.js，前后端分离架构）。</p>
            <div style="display:flex; gap:12px; margin-top:18px;">
              <a class="btn btn-primary" href="products.html">查看核心特性</a>
              <a class="btn" href="story.html">了解项目</a>
            </div>
          </div>
          <div class="hero-card reveal" style="transform:rotate(-3deg);">
            <div class="box-stack">
              <div class="choco-box choco-box--1">ORANGE</div>
              <div class="choco-box choco-box--2">HAPPI</div>
              <div class="choco-box choco-box--3">OAT MILK</div>
            </div>
          </div>
        </div>
      </section>

      <section class="section">
        <div class="container stickers reveal">
          <span class="sticker">Gin</span>
          <span class="sticker">MySQL</span>
          <span class="sticker">Redis</span>
          <span class="sticker">Vue.js</span>
        </div>
      </section>

      <section class="section">
        <div class="container reveal">
          <h2 class="section-title">BlueBell · 核心特性</h2>
          <p class="section-sub">一个基于 Gin + MySQL + Redis + Vue.js 的现代化论坛系统。</p>
          <div class="grid grid-3">
            <article class="product-card reveal">
              <div class="product-art"></div>
              <div class="product-title">后端架构</div>
              <div class="badge">Gin + 分层设计</div>
              <div class="price">帖子 · 投票 · 社区</div>
              <a class="btn btn-primary" href="products.html">了解更多</a>
            </article>
            <article class="product-card reveal">
              <div class="product-art" style="background:linear-gradient(135deg,#60a5fa,#2563eb);"></div>
              <div class="product-title">前端界面</div>
              <div class="badge">Vue.js + 响应式</div>
              <div class="price">流畅用户体验</div>
              <a class="btn btn-primary" href="products.html">了解更多</a>
            </article>
            <article class="product-card reveal">
              <div class="product-art" style="background:linear-gradient(135deg,#f97316,#ef4444);"></div>
              <div class="product-title">数据与认证</div>
              <div class="badge">MySQL + Redis</div>
              <div class="price">JWT · 权限控制</div>
              <a class="btn btn-primary" href="products.html">了解更多</a>
            </article>
          </div>
        </div>
      </section>

      <section class="ooo-block">
        <div class="container reveal">
          <div class="ooo-text">BlueBell has</div>
          <div class="ooo-row" style="margin:10px 0 20px;">
            <div class="ooo">O</div>
            <div class="ooo">O</div>
            <div class="ooo">O</div>
            <div class="ooo">O</div>
            <div class="ooo">O</div>
          </div>
          <div class="ooo-text">a modern stack</div>
        </div>
      </section>

      <section class="section" style="background: #ffefc3;">
        <div class="container grid grid-2 reveal">
          <div class="feature">
            <h3 class="section-title" style="font-size:36px;">投票机制</h3>
            <p>支持帖子点赞/点踩，实时评分排序。</p>
          </div>
          <div class="feature">
            <h3 class="section-title" style="font-size:36px;">API 文档</h3>
            <p>集成 Swagger，开发调试便捷（前端擅长使用 Cursor 生成）。</p>
          </div>
        </div>
      </section>

      <section class="section">
        <div class="container newsletter reveal">
          <h3 class="section-title" style="font-size:34px;">联系我</h3>
          <p class="section-sub">欢迎交流 Go / 前端 / UI 设计。</p>
          <form action="#" onsubmit="return false;">
            <input type="email" placeholder="你的邮箱" />
            <button class="btn btn-primary" type="submit">发送</button>
          </form>
        </div>
      </section>
    </main>

    <footer>
      <div class="container" style="display:flex; justify-content:space-between; align-items:center; gap:16px;">
        <div>© 2025 XiangYueYuan</div>
        <div class="links">
          <a href="products.html">商店</a>
          <a href="story.html">关于我们</a>
          <a href="benefits.html">优势</a>
          <a href="subscribe.html">订阅</a>
        </div>
      </div>
    </footer>
    <script src="assets/js/app.js"></script>
  </body>
</html>


