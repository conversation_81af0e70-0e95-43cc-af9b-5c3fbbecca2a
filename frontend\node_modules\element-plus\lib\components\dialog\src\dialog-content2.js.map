{"version": 3, "file": "dialog-content2.js", "sources": ["../../../../../../packages/components/dialog/src/dialog-content.vue"], "sourcesContent": ["<template>\n  <div :ref=\"composedDialogRef\" :class=\"dialogKls\" :style=\"style\" tabindex=\"-1\">\n    <header\n      ref=\"headerRef\"\n      :class=\"[ns.e('header'), headerClass, { 'show-close': showClose }]\"\n    >\n      <slot name=\"header\">\n        <span role=\"heading\" :aria-level=\"ariaLevel\" :class=\"ns.e('title')\">\n          {{ title }}\n        </span>\n      </slot>\n      <button\n        v-if=\"showClose\"\n        :aria-label=\"t('el.dialog.close')\"\n        :class=\"ns.e('headerbtn')\"\n        type=\"button\"\n        @click=\"$emit('close')\"\n      >\n        <el-icon :class=\"ns.e('close')\">\n          <component :is=\"closeIcon || Close\" />\n        </el-icon>\n      </button>\n    </header>\n    <div :id=\"bodyId\" :class=\"[ns.e('body'), bodyClass]\">\n      <slot />\n    </div>\n    <footer v-if=\"$slots.footer\" :class=\"[ns.e('footer'), footerClass]\">\n      <slot name=\"footer\" />\n    </footer>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject } from 'vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { FOCUS_TRAP_INJECTION_KEY } from '@element-plus/components/focus-trap'\nimport { useDraggable, useLocale } from '@element-plus/hooks'\nimport { CloseComponents, composeRefs } from '@element-plus/utils'\nimport { dialogInjectionKey } from './constants'\nimport { dialogContentEmits, dialogContentProps } from './dialog-content'\n\nconst { t } = useLocale()\nconst { Close } = CloseComponents\n\ndefineOptions({ name: 'ElDialogContent' })\nconst props = defineProps(dialogContentProps)\ndefineEmits(dialogContentEmits)\n\nconst { dialogRef, headerRef, bodyId, ns, style } = inject(dialogInjectionKey)!\nconst { focusTrapRef } = inject(FOCUS_TRAP_INJECTION_KEY)!\n\nconst composedDialogRef = composeRefs(focusTrapRef, dialogRef)\n\nconst draggable = computed(() => !!props.draggable)\nconst overflow = computed(() => !!props.overflow)\nconst { resetPosition, updatePosition, isDragging } = useDraggable(\n  dialogRef,\n  headerRef,\n  draggable,\n  overflow\n)\n\nconst dialogKls = computed(() => [\n  ns.b(),\n  ns.is('fullscreen', props.fullscreen),\n  ns.is('draggable', draggable.value),\n  ns.is('dragging', isDragging.value),\n  ns.is('align-center', !!props.alignCenter),\n  { [ns.m('center')]: props.center },\n])\n\ndefineExpose({\n  resetPosition,\n  updatePosition,\n})\n</script>\n"], "names": ["useLocale", "CloseComponents", "inject", "dialogInjectionKey", "FOCUS_TRAP_INJECTION_KEY", "composeRefs", "computed", "useDraggable", "_openBlock", "_createElementBlock", "_unref", "_normalizeClass", "_normalizeStyle"], "mappings": ";;;;;;;;;;;;;;;uCA4Cc,CAAA,EAAE,IAAM,EAAA,iBAAA,EAAkB,CAAA,CAAA;;;;;;;AAHxC,IAAM,MAAA,EAAE,CAAE,EAAA,GAAIA,eAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAE,OAAU,GAAAC,oBAAA,CAAA;AAMlB,IAAM,MAAA,EAAE,WAAW,SAAW,EAAA,MAAA,EAAQ,IAAI,KAAM,EAAA,GAAIC,WAAOC,4BAAkB,CAAA,CAAA;AAC7E,IAAA,MAAM,EAAE,YAAA,EAAiB,GAAAD,UAAA,CAAOE,+BAAwB,CAAA,CAAA;AAExD,IAAM,MAAA,iBAAA,GAAoBC,gBAAY,CAAA,YAAA,EAAc,SAAS,CAAA,CAAA;AAE7D,IAAA,MAAM,YAAYC,YAAS,CAAA,MAAM,CAAC,CAAC,MAAM,SAAS,CAAA,CAAA;AAClD,IAAA,MAAM,WAAWA,YAAS,CAAA,MAAM,CAAC,CAAC,MAAM,QAAQ,CAAA,CAAA;AAChD,IAAA,MAAM,EAAE,aAAA,EAAe,cAAgB,EAAA,UAAA,EAAe,GAAAC,oBAAA,CAAA,SAAA,EAAA,SAAA,EAAA,SAAA,EAAA,QAAA,CAAA,CAAA;AAAA,IACpD,MAAA,SAAA,GAAAD,YAAA,CAAA,MAAA;AAAA,MACA,EAAA,CAAA,CAAA,EAAA;AAAA,MACA,EAAA,CAAA,EAAA,CAAA,YAAA,EAAA,KAAA,CAAA,UAAA,CAAA;AAAA,MACA,EAAA,CAAA,EAAA,CAAA,WAAA,EAAA,SAAA,CAAA,KAAA,CAAA;AAAA,MACF,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,UAAA,CAAA,KAAA,CAAA;AAEA,MAAM,EAAA,CAAA,EAAA,CAAA,uBAA2B,CAAA,WAAA,CAAA;AAAA,MAC/B,GAAG,EAAE,CAAA,CAAA,CAAA,QAAA,CAAA,GAAA,KAAA,CAAA,MAAA,EAAA;AAAA,KAAA,CACL,CAAG;AAAiC,IAAA,MACjC,CAAA;AAA+B,MAClC,aAAkB;AAAgB,MAClC,cAAM;AAAmC,KACzC,CAAA,CAAA;AAAiC,IACnC,OAAC,CAAA,IAAA,EAAA,MAAA,KAAA;AAED,MAAa,OAAAE,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,QACX,GAAA,EAAAC,SAAA,CAAA,iBAAA,CAAA;AAAA,QACA,KAAA,EAAAC,kBAAA,CAAAD,SAAA,CAAA,SAAA,CAAA,CAAA;AAAA,QACD,KAAA,EAAAE,kBAAA,CAAAF,SAAA,CAAA,KAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}