<template>
  <div class="product-detail-page">
    <div class="container">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      
      <div v-else-if="product" class="product-detail">
        <div class="product-images">
          <div class="main-image">
            <img v-if="product.image_url" :src="product.image_url" :alt="product.name" />
            <div v-else class="placeholder-image">
              <el-icon size="80"><Picture /></el-icon>
            </div>
          </div>
        </div>
        
        <div class="product-info">
          <div class="product-category">{{ product.category }}</div>
          <h1 class="product-name">{{ product.name }}</h1>
          <p class="product-description">{{ product.description }}</p>
          
          <div class="product-price">
            <span class="current-price">¥{{ product.price.toFixed(2) }}</span>
          </div>
          
          <div class="product-stock">
            <span v-if="product.stock_quantity > 0" class="in-stock">
              库存：{{ product.stock_quantity }} 件
            </span>
            <span v-else class="out-of-stock">缺货</span>
          </div>
          
          <div class="product-actions">
            <div class="quantity-selector">
              <span>数量：</span>
              <el-input-number
                v-model="quantity"
                :min="1"
                :max="product.stock_quantity"
                :disabled="product.stock_quantity <= 0"
              />
            </div>
            
            <div class="action-buttons">
              <el-button
                type="primary"
                size="large"
                :disabled="product.stock_quantity <= 0"
                :loading="adding"
                @click="addToCart"
              >
                <el-icon><ShoppingCart /></el-icon>
                加入购物车
              </el-button>
              
              <el-button
                size="large"
                @click="toggleFavorite"
              >
                <el-icon><Star /></el-icon>
                {{ isFavorite ? '取消收藏' : '收藏' }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
      
      <div v-else class="not-found">
        <el-icon size="80" color="#ccc">
          <Warning />
        </el-icon>
        <h2>产品不存在</h2>
        <p>抱歉，您查找的产品不存在或已下架。</p>
        <router-link to="/products" class="btn btn-primary">返回产品列表</router-link>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { Picture, ShoppingCart, Star, Warning } from '@element-plus/icons-vue'

export default {
  name: 'ProductDetail',
  components: {
    Picture,
    ShoppingCart,
    Star,
    Warning
  },
  setup() {
    const store = useStore()
    const route = useRoute()
    const router = useRouter()
    
    const quantity = ref(1)
    const adding = ref(false)
    const isFavorite = ref(false)
    
    const product = computed(() => store.getters['products/currentProduct'])
    const loading = computed(() => store.getters['products/loading'])
    const isAuthenticated = computed(() => store.getters['auth/isAuthenticated'])
    
    const addToCart = async () => {
      if (!isAuthenticated.value) {
        store.dispatch('notifications/showNotification', {
          type: 'warning',
          message: '请先登录'
        })
        router.push('/login')
        return
      }
      
      adding.value = true
      try {
        await store.dispatch('cart/addToCart', {
          productId: product.value.id,
          quantity: quantity.value
        })
      } catch (error) {
        console.error('Add to cart error:', error)
      } finally {
        adding.value = false
      }
    }
    
    const toggleFavorite = () => {
      if (!isAuthenticated.value) {
        store.dispatch('notifications/showNotification', {
          type: 'warning',
          message: '请先登录'
        })
        router.push('/login')
        return
      }
      
      isFavorite.value = !isFavorite.value
      
      store.dispatch('notifications/showNotification', {
        type: 'success',
        message: isFavorite.value ? '已添加到收藏' : '已取消收藏'
      })
    }
    
    onMounted(async () => {
      const productId = route.params.id
      try {
        await store.dispatch('products/loadProduct', productId)
      } catch (error) {
        store.dispatch('notifications/showNotification', {
          type: 'error',
          message: error.message
        })
      }
    })
    
    return {
      product,
      loading,
      quantity,
      adding,
      isFavorite,
      addToCart,
      toggleFavorite
    }
  }
}
</script>

<style lang="scss" scoped>
.product-detail-page {
  padding: 40px 0;
  
  .loading-container {
    padding: 40px 0;
  }
  
  .product-detail {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    max-width: 1200px;
    margin: 0 auto;
    
    .product-images {
      .main-image {
        width: 100%;
        height: 500px;
        border-radius: var(--radius-lg);
        overflow: hidden;
        background: #f8f9fa;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .placeholder-image {
          width: 100%;
          height: 100%;
          @include chocolate-gradient;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }
      }
    }
    
    .product-info {
      .product-category {
        font-size: 14px;
        color: var(--color-primary);
        font-weight: 600;
        text-transform: uppercase;
        margin-bottom: 8px;
      }
      
      .product-name {
        font-size: 32px;
        font-weight: 800;
        color: var(--color-ink);
        margin-bottom: 16px;
      }
      
      .product-description {
        font-size: 16px;
        line-height: 1.6;
        color: #666;
        margin-bottom: 24px;
      }
      
      .product-price {
        margin-bottom: 16px;
        
        .current-price {
          font-size: 28px;
          font-weight: 700;
          color: var(--color-primary);
        }
      }
      
      .product-stock {
        margin-bottom: 32px;
        
        .in-stock {
          color: var(--color-success);
          font-weight: 600;
        }
        
        .out-of-stock {
          color: var(--color-error);
          font-weight: 600;
        }
      }
      
      .product-actions {
        .quantity-selector {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 24px;
          
          span {
            font-weight: 600;
            color: var(--color-ink);
          }
        }
        
        .action-buttons {
          display: flex;
          gap: 16px;
          
          .el-button {
            flex: 1;
          }
        }
      }
    }
  }
  
  .not-found {
    text-align: center;
    padding: 80px 20px;
    
    h2 {
      font-size: 24px;
      color: var(--color-ink);
      margin: 24px 0 16px 0;
    }
    
    p {
      font-size: 16px;
      color: #666;
      margin-bottom: 32px;
    }
  }
}

@include respond-to('mobile') {
  .product-detail-page {
    .product-detail {
      grid-template-columns: 1fr;
      gap: 30px;
      
      .product-images {
        .main-image {
          height: 300px;
        }
      }
      
      .product-info {
        .product-name {
          font-size: 24px;
        }
        
        .product-actions {
          .action-buttons {
            flex-direction: column;
          }
        }
      }
    }
  }
}
</style>
