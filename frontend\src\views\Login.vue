<template>
  <div class="login-page">
    <div class="container">
      <div class="login-card">
        <div class="login-header">
          <h1>登录 X Oberon</h1>
          <p>欢迎回到巧克力的甜蜜世界</p>
        </div>
        
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          @submit.prevent="handleLogin"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="用户名"
              size="large"
              prefix-icon="User"
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="密码"
              size="large"
              prefix-icon="Lock"
              show-password
            />
          </el-form-item>
          
          <el-form-item>
            <el-button
              type="primary"
              size="large"
              :loading="loading"
              @click="handleLogin"
              class="login-button"
            >
              登录
            </el-button>
          </el-form-item>
        </el-form>
        
        <div class="login-footer">
          <p>还没有账号？ <router-link to="/register">立即注册</router-link></p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'

export default {
  name: 'Login',
  setup() {
    const store = useStore()
    const router = useRouter()
    const route = useRoute()
    
    const loginFormRef = ref()
    const loading = ref(false)
    
    const loginForm = reactive({
      username: '',
      password: ''
    })
    
    const loginRules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '密码长度至少6位', trigger: 'blur' }
      ]
    }
    
    const handleLogin = async () => {
      if (!loginFormRef.value) return
      
      try {
        await loginFormRef.value.validate()
        
        loading.value = true
        
        await store.dispatch('auth/login', {
          username: loginForm.username,
          password: loginForm.password
        })
        
        store.dispatch('notifications/showNotification', {
          type: 'success',
          message: '登录成功'
        })
        
        // 跳转到原来要访问的页面或首页
        const redirect = route.query.redirect || '/'
        router.push(redirect)
        
      } catch (error) {
        store.dispatch('notifications/showNotification', {
          type: 'error',
          message: error.message
        })
      } finally {
        loading.value = false
      }
    }
    
    return {
      loginFormRef,
      loginForm,
      loginRules,
      loading,
      handleLogin
    }
  }
}
</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  
  .login-card {
    @include card-style;
    width: 100%;
    max-width: 400px;
    
    .login-header {
      text-align: center;
      margin-bottom: 32px;
      
      h1 {
        font-size: 28px;
        font-weight: 700;
        color: var(--color-ink);
        margin-bottom: 8px;
      }
      
      p {
        color: #666;
        font-size: 16px;
      }
    }
    
    .login-button {
      width: 100%;
    }
    
    .login-footer {
      text-align: center;
      margin-top: 24px;
      
      p {
        color: #666;
        font-size: 14px;
        
        a {
          color: var(--color-primary);
          text-decoration: none;
          font-weight: 600;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-input__wrapper) {
  border-radius: var(--radius-sm);
}
</style>
