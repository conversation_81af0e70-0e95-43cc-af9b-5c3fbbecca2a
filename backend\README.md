# X Oberon 巧克力商店 - 后端API

基于 Go + Gin + MySQL + Redis 构建的现代化巧克力在线商店后端服务。

## 技术栈

- **Go 1.21+** - 编程语言
- **Gin** - Web框架
- **MySQL** - 主数据库
- **Redis** - 缓存和会话存储
- **SQLX** - SQL扩展库
- **JWT** - 用户认证
- **bcrypt** - 密码加密

## 功能特性

### 用户管理
- 用户注册/登录
- JWT认证
- 用户资料管理
- 会话管理（Redis）

### 产品管理
- 产品列表（分页）
- 产品详情
- 按分类筛选
- 产品搜索
- Redis缓存优化

### 购物车
- 添加/移除商品
- 更新商品数量
- 购物车持久化

### 订单管理
- 创建订单
- 订单历史
- 订单状态跟踪
- 库存管理

## 快速开始

### 环境要求

- Go 1.21+
- MySQL 8.0+
- Redis 6.0+

### 安装依赖

```bash
cd backend
go mod tidy
```

### 环境变量

创建 `.env` 文件或设置以下环境变量：

```bash
PORT=8080
ENVIRONMENT=development
DATABASE_URL=postgres://user:password@localhost/chocolate_shop?sslmode=disable
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-secret-key
```

### 数据库设置

1. 创建MySQL数据库：
```sql
CREATE DATABASE chocolate_shop;
```

2. 应用程序会自动创建所需的表结构

### 启动服务

```bash
go run main.go
```

服务将在 `http://localhost:8080` 启动

## API文档

### 认证相关

#### 用户注册
```
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "full_name": "Test User",
  "phone": "1234567890",
  "address": "Test Address"
}
```

#### 用户登录
```
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "password123"
}
```

### 产品相关

#### 获取产品列表
```
GET /api/v1/products?page=1&page_size=12
```

#### 获取产品详情
```
GET /api/v1/products/{id}
```

#### 按分类获取产品
```
GET /api/v1/products/category/{category}?page=1&page_size=12
```

#### 搜索产品
```
GET /api/v1/products/search?q=巧克力&page=1&page_size=12
```

### 购物车相关（需要认证）

#### 获取购物车
```
GET /api/v1/cart
Authorization: Bearer {token}
```

#### 添加到购物车
```
POST /api/v1/cart/add
Authorization: Bearer {token}
Content-Type: application/json

{
  "product_id": 1,
  "quantity": 2
}
```

### 订单相关（需要认证）

#### 获取订单列表
```
GET /api/v1/orders?page=1&page_size=10
Authorization: Bearer {token}
```

#### 创建订单
```
POST /api/v1/orders
Authorization: Bearer {token}
Content-Type: application/json

{
  "shipping_address": "配送地址",
  "payment_method": "credit_card"
}
```

## 项目结构

```
backend/
├── main.go                 # 应用入口
├── go.mod                  # Go模块文件
├── config/                 # 配置管理
│   └── config.go
├── internal/
│   ├── api/               # API路由
│   │   └── routes.go
│   ├── database/          # 数据库连接和初始化
│   │   └── database.go
│   ├── handlers/          # HTTP处理器
│   │   ├── user.go
│   │   ├── product.go
│   │   ├── cart.go
│   │   └── order.go
│   ├── middleware/        # 中间件
│   │   └── auth.go
│   ├── models/           # 数据模型
│   │   └── models.go
│   └── redis/            # Redis操作
│       └── redis.go
└── README.md
```

## 开发说明

### 数据库表结构

- `users` - 用户表
- `products` - 产品表
- `orders` - 订单表
- `order_items` - 订单项表
- `cart_items` - 购物车表

### 缓存策略

- 产品信息缓存（30分钟）
- 热门产品缓存（1小时）
- 用户会话缓存（24小时）

### 安全特性

- JWT认证
- 密码bcrypt加密
- CORS支持
- 输入验证

## 部署

### Docker部署（推荐）

```bash
# 构建镜像
docker build -t chocolate-shop-api .

# 运行容器
docker run -p 8080:8080 \
  -e DATABASE_URL="your-database-url" \
  -e REDIS_URL="your-redis-url" \
  -e JWT_SECRET="your-jwt-secret" \
  chocolate-shop-api
```

### 生产环境配置

1. 设置环境变量 `ENVIRONMENT=production`
2. 配置生产数据库和Redis
3. 使用强密码作为JWT_SECRET
4. 配置反向代理（Nginx）
5. 启用HTTPS

## 许可证

MIT License
