package database

import (
	"chocolate-shop/internal/models"
	"log"

	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq"
)

func Init(databaseURL string) (*sqlx.DB, error) {
	db, err := sqlx.Connect("postgres", databaseURL)
	if err != nil {
		return nil, err
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, err
	}

	// 创建表
	if err := createTables(db); err != nil {
		return nil, err
	}

	log.Println("Database connected successfully")
	return db, nil
}

func createTables(db *sqlx.DB) error {
	// 用户表
	userTable := `
	CREATE TABLE IF NOT EXISTS users (
		id SERIAL PRIMARY KEY,
		username VARCHAR(50) UNIQUE NOT NULL,
		email VARCHAR(100) UNIQUE NOT NULL,
		password_hash VARCHAR(255) NOT NULL,
		full_name VARCHAR(100),
		phone VARCHAR(20),
		address TEXT,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`

	// 巧克力产品表
	productTable := `
	CREATE TABLE IF NOT EXISTS products (
		id SERIAL PRIMARY KEY,
		name VARCHAR(100) NOT NULL,
		description TEXT,
		price DECIMAL(10,2) NOT NULL,
		category VARCHAR(50) NOT NULL,
		image_url VARCHAR(255),
		stock_quantity INTEGER DEFAULT 0,
		is_available BOOLEAN DEFAULT true,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`

	// 订单表
	orderTable := `
	CREATE TABLE IF NOT EXISTS orders (
		id SERIAL PRIMARY KEY,
		user_id INTEGER REFERENCES users(id),
		total_amount DECIMAL(10,2) NOT NULL,
		status VARCHAR(20) DEFAULT 'pending',
		shipping_address TEXT NOT NULL,
		payment_method VARCHAR(20) NOT NULL,
		payment_status VARCHAR(20) DEFAULT 'pending',
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`

	// 订单项表
	orderItemTable := `
	CREATE TABLE IF NOT EXISTS order_items (
		id SERIAL PRIMARY KEY,
		order_id INTEGER REFERENCES orders(id),
		product_id INTEGER REFERENCES products(id),
		quantity INTEGER NOT NULL,
		price DECIMAL(10,2) NOT NULL,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`

	// 购物车表
	cartTable := `
	CREATE TABLE IF NOT EXISTS cart_items (
		id SERIAL PRIMARY KEY,
		user_id INTEGER REFERENCES users(id),
		product_id INTEGER REFERENCES products(id),
		quantity INTEGER NOT NULL,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		UNIQUE(user_id, product_id)
	)`

	// 产品评价表
	reviewTable := `
	CREATE TABLE IF NOT EXISTS product_reviews (
		id SERIAL PRIMARY KEY,
		user_id INTEGER REFERENCES users(id),
		product_id INTEGER REFERENCES products(id),
		rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
		comment TEXT,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		UNIQUE(user_id, product_id)
	)`

	// 优惠券表
	couponTable := `
	CREATE TABLE IF NOT EXISTS coupons (
		id SERIAL PRIMARY KEY,
		code VARCHAR(50) UNIQUE NOT NULL,
		name VARCHAR(100) NOT NULL,
		description TEXT,
		discount_type VARCHAR(20) NOT NULL, -- 'percentage' or 'fixed'
		discount_value DECIMAL(10,2) NOT NULL,
		min_order_amount DECIMAL(10,2) DEFAULT 0,
		max_discount_amount DECIMAL(10,2),
		usage_limit INTEGER DEFAULT 0, -- 0 means unlimited
		used_count INTEGER DEFAULT 0,
		start_date TIMESTAMP NOT NULL,
		end_date TIMESTAMP NOT NULL,
		is_active BOOLEAN DEFAULT true,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`

	// 用户优惠券表
	userCouponTable := `
	CREATE TABLE IF NOT EXISTS user_coupons (
		id SERIAL PRIMARY KEY,
		user_id INTEGER REFERENCES users(id),
		coupon_id INTEGER REFERENCES coupons(id),
		used_at TIMESTAMP,
		order_id INTEGER REFERENCES orders(id),
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		UNIQUE(user_id, coupon_id)
	)`

	// 收藏表
	favoriteTable := `
	CREATE TABLE IF NOT EXISTS user_favorites (
		id SERIAL PRIMARY KEY,
		user_id INTEGER REFERENCES users(id),
		product_id INTEGER REFERENCES products(id),
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		UNIQUE(user_id, product_id)
	)`

	tables := []string{userTable, productTable, orderTable, orderItemTable, cartTable, reviewTable, couponTable, userCouponTable, favoriteTable}

	for _, table := range tables {
		if _, err := db.Exec(table); err != nil {
			return err
		}
	}

	// 插入示例产品数据
	if err := insertSampleProducts(db); err != nil {
		log.Printf("Warning: Failed to insert sample products: %v", err)
	}

	return nil
}

func insertSampleProducts(db *sqlx.DB) error {
	// 检查是否已有产品数据
	var count int
	err := db.Get(&count, "SELECT COUNT(*) FROM products")
	if err != nil {
		return err
	}

	if count > 0 {
		return nil // 已有数据，跳过插入
	}

	products := []models.Product{
		{
			Name:          "经典黑巧克力",
			Description:   "70%可可含量的经典黑巧克力，口感浓郁醇厚",
			Price:         28.00,
			Category:      "黑巧克力",
			ImageURL:      "/images/dark-chocolate.jpg",
			StockQuantity: 100,
			IsAvailable:   true,
		},
		{
			Name:          "丝滑牛奶巧克力",
			Description:   "香甜丝滑的牛奶巧克力，老少皆宜",
			Price:         25.00,
			Category:      "牛奶巧克力",
			ImageURL:      "/images/milk-chocolate.jpg",
			StockQuantity: 150,
			IsAvailable:   true,
		},
		{
			Name:          "纯白巧克力",
			Description:   "纯正白巧克力，甜而不腻",
			Price:         30.00,
			Category:      "白巧克力",
			ImageURL:      "/images/white-chocolate.jpg",
			StockQuantity: 80,
			IsAvailable:   true,
		},
		{
			Name:          "榛子巧克力",
			Description:   "加入优质榛子的特色巧克力",
			Price:         35.00,
			Category:      "坚果巧克力",
			ImageURL:      "/images/hazelnut-chocolate.jpg",
			StockQuantity: 60,
			IsAvailable:   true,
		},
	}

	for _, product := range products {
		_, err := db.NamedExec(`
			INSERT INTO products (name, description, price, category, image_url, stock_quantity, is_available)
			VALUES (:name, :description, :price, :category, :image_url, :stock_quantity, :is_available)
		`, product)
		if err != nil {
			return err
		}
	}

	return nil
}
