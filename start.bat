@echo off
echo Starting X Oberon Chocolate Shop...
echo.

echo [1/3] Starting backend services...
cd backend
start "Backend Services" cmd /k "docker-compose up"

echo [2/3] Waiting for services to start...
timeout /t 10 /nobreak > nul

echo [3/3] Opening frontend...
cd ..
start "" "index.html"

echo.
echo X Oberon Chocolate Shop is starting!
echo Frontend: http://localhost/index.html
echo Backend API: http://localhost:8080
echo.
echo Press any key to exit...
pause > nul
