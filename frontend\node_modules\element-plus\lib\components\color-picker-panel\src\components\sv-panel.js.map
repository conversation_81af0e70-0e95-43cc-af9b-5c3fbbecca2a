{"version": 3, "file": "sv-panel.js", "sources": ["../../../../../../../packages/components/color-picker-panel/src/components/sv-panel.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"ns.b()\"\n    :style=\"{\n      backgroundColor: background,\n    }\"\n  >\n    <div :class=\"ns.e('white')\" />\n    <div :class=\"ns.e('black')\" />\n    <div\n      :class=\"ns.e('cursor')\"\n      :style=\"{\n        top: cursorTop + 'px',\n        left: cursorLeft + 'px',\n      }\"\n    >\n      <div />\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  onMounted,\n  ref,\n  watch,\n} from 'vue'\nimport { getClientXY } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { draggable } from '../utils/draggable'\n\nimport type { PropType } from 'vue'\nimport type Color from '../utils/color'\n\nexport default defineComponent({\n  name: 'ElSlPanel',\n\n  props: {\n    color: {\n      type: Object as PropType<Color>,\n      required: true,\n    },\n    disabled: Boolean,\n  },\n\n  setup(props) {\n    const ns = useNamespace('color-svpanel')\n\n    // instance\n    const instance = getCurrentInstance()!\n\n    // data\n    const cursorTop = ref(0)\n    const cursorLeft = ref(0)\n    const background = ref('hsl(0, 100%, 50%)')\n    const colorValue = computed(() => {\n      const hue = props.color.get('hue')\n      const value = props.color.get('value')\n      return { hue, value }\n    })\n\n    // methods\n    function update() {\n      const saturation = props.color.get('saturation')\n      const value = props.color.get('value')\n\n      const el = instance.vnode.el!\n      const { clientWidth: width, clientHeight: height } = el\n\n      cursorLeft.value = (saturation * width) / 100\n      cursorTop.value = ((100 - value) * height) / 100\n\n      background.value = `hsl(${props.color.get('hue')}, 100%, 50%)`\n    }\n\n    function handleDrag(event: MouseEvent | TouchEvent) {\n      if (props.disabled) return\n\n      const el = instance.vnode.el!\n      const rect = el.getBoundingClientRect()\n      const { clientX, clientY } = getClientXY(event)\n\n      let left = clientX - rect.left\n      let top = clientY - rect.top\n      left = Math.max(0, left)\n      left = Math.min(left, rect.width)\n\n      top = Math.max(0, top)\n      top = Math.min(top, rect.height)\n\n      cursorLeft.value = left\n      cursorTop.value = top\n      props.color.set({\n        saturation: (left / rect.width) * 100,\n        value: 100 - (top / rect.height) * 100,\n      })\n    }\n\n    // watch\n    watch(\n      () => colorValue.value,\n      () => {\n        update()\n      }\n    )\n\n    // mounted\n    onMounted(() => {\n      draggable(instance.vnode.el as HTMLElement, {\n        drag: (event) => {\n          handleDrag(event)\n        },\n        end: (event) => {\n          handleDrag(event)\n        },\n      })\n\n      update()\n    })\n    return {\n      cursorTop,\n      cursorLeft,\n      background,\n      colorValue,\n      handleDrag,\n      update,\n      ns,\n    }\n  },\n})\n</script>\n"], "names": ["defineComponent", "useNamespace", "getCurrentInstance", "ref", "computed", "getClientXY", "watch", "onMounted", "draggable", "_normalizeStyle", "_createElementVNode", "_normalizeClass", "_export_sfc"], "mappings": ";;;;;;;;;;AAqCA,MAAK,YAAaA,mBAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,WAAA;AAAA,EAEN,KAAO,EAAA;AAAA,IACL,KAAO,EAAA;AAAA,MACL,IAAM,EAAA,MAAA;AAAA,MACN,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,IACA,QAAU,EAAA,OAAA;AAAA,GACZ;AAAA,EAEA,MAAM,KAAO,EAAA;AACX,IAAM,MAAA,EAAA,GAAKC,mBAAa,eAAe,CAAA,CAAA;AAGvC,IAAA,MAAM,WAAWC,sBAAmB,EAAA,CAAA;AAGpC,IAAM,MAAA,SAAA,GAAYC,QAAI,CAAC,CAAA,CAAA;AACvB,IAAM,MAAA,UAAA,GAAaA,QAAI,CAAC,CAAA,CAAA;AACxB,IAAM,MAAA,UAAA,GAAaA,QAAI,mBAAmB,CAAA,CAAA;AAC1C,IAAM,MAAA,UAAA,GAAaC,aAAS,MAAM;AAChC,MAAA,MAAM,GAAM,GAAA,KAAA,CAAM,KAAM,CAAA,GAAA,CAAI,KAAK,CAAA,CAAA;AACjC,MAAA,MAAM,KAAQ,GAAA,KAAA,CAAM,KAAM,CAAA,GAAA,CAAI,OAAO,CAAA,CAAA;AACrC,MAAO,OAAA,EAAE,KAAK,KAAM,EAAA,CAAA;AAAA,KACrB,CAAA,CAAA;AAGD,IAAA,SAAS,MAAS,GAAA;AAChB,MAAA,MAAM,UAAa,GAAA,KAAA,CAAM,KAAM,CAAA,GAAA,CAAI,YAAY,CAAA,CAAA;AAC/C,MAAA,MAAM,KAAQ,GAAA,KAAA,CAAM,KAAM,CAAA,GAAA,CAAI,OAAO,CAAA,CAAA;AAErC,MAAM,MAAA,EAAA,GAAK,SAAS,KAAM,CAAA,EAAA,CAAA;AAC1B,MAAA,MAAM,EAAE,WAAA,EAAa,KAAO,EAAA,YAAA,EAAc,QAAW,GAAA,EAAA,CAAA;AAErD,MAAW,UAAA,CAAA,KAAA,GAAS,aAAa,KAAS,GAAA,GAAA,CAAA;AAC1C,MAAU,SAAA,CAAA,KAAA,GAAA,CAAU,GAAM,GAAA,KAAA,IAAS,MAAU,GAAA,GAAA,CAAA;AAE7C,MAAA,UAAA,CAAW,QAAQ,CAAO,IAAA,EAAA,KAAA,CAAM,KAAM,CAAA,GAAA,CAAI,KAAK,CAAC,CAAA,YAAA,CAAA,CAAA;AAAA,KAClD;AAEA,IAAA,SAAS,WAAW,KAAgC,EAAA;AAClD,MAAA,IAAI,MAAM,QAAU;AAEpB,QAAM,OAAA;AACN,MAAM,MAAA,EAAA,GAAA,QAAgC,CAAA,KAAA,CAAA,EAAA,CAAA;AACtC,MAAA,MAAM,IAAE,GAAA,EAAA,CAAA;AAER,MAAI,MAAA,EAAA,gBAAsB,EAAA,GAAAC,oBAAA,CAAA,KAAA,CAAA,CAAA;AAC1B,MAAI,IAAA,IAAA,aAAqB,IAAA,CAAA,IAAA,CAAA;AACzB,MAAO,IAAA,GAAA,GAAA,OAAS,GAAO,IAAA,CAAA,GAAA,CAAA;AACvB,MAAA,IAAA,GAAO,IAAK,CAAA,GAAA,CAAI,CAAM,EAAA,IAAA,CAAA,CAAA;AAEtB,MAAM,IAAA,GAAA,IAAK,CAAI,GAAA,CAAA,IAAM,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACrB,MAAA,GAAA,GAAM,IAAK,CAAA,GAAA,CAAI,CAAK,EAAA,GAAA,CAAA,CAAA;AAEpB,MAAA,GAAA,GAAA,IAAA,CAAW,GAAQ,CAAA,GAAA,EAAA,IAAA,CAAA,MAAA,CAAA,CAAA;AACnB,MAAA,UAAU,CAAQ,KAAA,GAAA,IAAA,CAAA;AAClB,MAAA,eAAgB,GAAA,GAAA,CAAA;AAAA,MACd,KAAA,CAAA,KAAA,CAAA,GAAoB,CAAA;AAAc,QAClC,UAAO,EAAA,IAAa,GAAA,IAAA,CAAK,KAAU,GAAA,GAAA;AAAA,QACpC,KAAA,EAAA,GAAA,GAAA,GAAA,GAAA,IAAA,CAAA,MAAA,GAAA,GAAA;AAAA,OACH,CAAA,CAAA;AAGA,KAAA;AAAA,IAAAC,gBACmB,UAAA,CAAA,KAAA,EAAA,MAAA;AAAA,MACjB,MAAM,EAAA,CAAA;AACJ,KAAO,CAAA,CAAA;AAAA,IACTC,aAAA,CAAA,MAAA;AAAA,MACFC,mBAAA,CAAA,QAAA,CAAA,KAAA,CAAA,EAAA,EAAA;AAGA,QAAA,IAAA,EAAU,CAAM,KAAA,KAAA;AACd,UAAU,UAAA,CAAA;AAAkC,SAC1C;AACE,QAAA,GAAA,EAAA,CAAA,KAAA,KAAgB;AAAA,UAClB,UAAA,CAAA,KAAA,CAAA,CAAA;AAAA,SACA;AACE,OAAA,CAAA,CAAA;AAAgB,MAClB,MAAA,EAAA,CAAA;AAAA,KAAA,CACF,CAAC;AAED,IAAO,OAAA;AAAA,MACR,SAAA;AACD,MAAO,UAAA;AAAA,MACL,UAAA;AAAA,MACA,UAAA;AAAA,MACA,UAAA;AAAA,MACA,MAAA;AAAA,MACA,EAAA;AAAA,KACA,CAAA;AAAA,GACA;AAAA,CACF,CAAA,CAAA;AAEJ,SAAC,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;;;AAnIC,IAAA,KAAA,EAAAC,kBAAA,CAAA;AAAA,MAiBM,eAAA,EAAA,IAAA,CAAA,UAAA;AAAA,KAAA,CAAA;AAAA,GAhBH,EAAA;AAAW,IAAAC,sBACN,CAAA,KAAA,EAAA;AAAA,MAA2B,KAAA,EAAAC,kBAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;AAAA,KAAA,EAAA,IAAA,EAAA,CAAA,CAAA;;;AAIjC,KAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AAAA,IAA8BD,sBAAA,CAAA,KAAA,EAAA;AAAA,MAAA,KAAA,EAAAC,kBAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA;AAAA,MAAxB,KAAA,EAAAF,kBAAO,CAAA;AAAI,QAAA,GAAA,EAAA,IAAA,CAAA,SAAA,GAAA,IAAA;;;;;AACjB,KAAA,EAAA,CAAA,CAAA;AAAA,GAA8B,EAAA,CAAA,CAAA,CAAA;AAAA,CAAA;AAAb,cAAA,gBAAAG,iCAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,cAAA,CAAA,CAAA,CAAA;;;;"}