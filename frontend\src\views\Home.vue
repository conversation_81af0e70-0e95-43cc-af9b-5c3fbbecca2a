<template>
  <div class="home">
    <!-- Hero Section -->
    <section class="hero">
      <div class="container hero-inner">
        <div class="hero-content reveal">
          <h1>X Oberon 巧克力<br />精品手工 · 甜蜜送达</h1>
          <p>精选优质可可豆，手工制作每一块巧克力。基于现代化技术构建的巧克力在线商店，为您提供极致的甜蜜体验。</p>
          <div class="hero-actions">
            <router-link to="/products" class="btn btn-primary">浏览巧克力</router-link>
            <router-link to="/about" class="btn btn-outline">了解品牌</router-link>
          </div>
        </div>
        <div class="hero-card reveal">
          <div class="box-stack">
            <div class="choco-box choco-box--1">黑巧克力</div>
            <div class="choco-box choco-box--2">牛奶巧克力</div>
            <div class="choco-box choco-box--3">白巧克力</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="section">
      <div class="container stickers reveal">
        <span class="sticker">手工制作</span>
        <span class="sticker">优质可可</span>
        <span class="sticker">新鲜配送</span>
        <span class="sticker">精美包装</span>
      </div>
    </section>

    <!-- Products Preview -->
    <section class="section">
      <div class="container reveal">
        <h2 class="section-title">X Oberon 巧克力 · 核心特性</h2>
        <p class="section-sub">基于现代化技术构建的巧克力在线商店。</p>
        <div class="grid grid-3">
          <article class="product-card reveal">
            <div class="product-art"></div>
            <div class="product-title">精品巧克力</div>
            <div class="badge">手工制作</div>
            <div class="price">100% 纯可可</div>
            <router-link to="/products" class="btn btn-primary">浏览产品</router-link>
          </article>
          <article class="product-card reveal">
            <div class="product-art" style="background:linear-gradient(135deg,#60a5fa,#2563eb);"></div>
            <div class="product-title">新鲜配送</div>
            <div class="badge">当日制作</div>
            <div class="price">保鲜包装</div>
            <router-link to="/products" class="btn btn-primary">立即购买</router-link>
          </article>
          <article class="product-card reveal">
            <div class="product-art" style="background:linear-gradient(135deg,#f97316,#ef4444);"></div>
            <div class="product-title">定制服务</div>
            <div class="badge">个性化包装</div>
            <div class="price">礼品 · 定制</div>
            <router-link to="/contact" class="btn btn-primary">了解更多</router-link>
          </article>
        </div>
      </div>
    </section>

    <!-- Featured Products -->
    <section class="section">
      <div class="container reveal">
        <h2 class="section-title">热门产品</h2>
        <p class="section-sub">精选最受欢迎的巧克力产品</p>
        
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="3" animated />
        </div>
        
        <div v-else class="grid grid-4">
          <ProductCard
            v-for="product in featuredProducts"
            :key="product.id"
            :product="product"
            class="reveal"
          />
        </div>
        
        <div class="text-center" style="margin-top: 40px;">
          <router-link to="/products" class="btn btn-primary">查看全部产品</router-link>
        </div>
      </div>
    </section>

    <!-- Chocolate Showcase -->
    <section class="ooo-block">
      <div class="container reveal">
        <div class="ooo-text">X Oberon has</div>
        <div class="ooo-row">
          <div class="ooo">🍫</div>
          <div class="ooo">🍫</div>
          <div class="ooo">🍫</div>
          <div class="ooo">🍫</div>
          <div class="ooo">🍫</div>
        </div>
        <div class="ooo-text">premium chocolate</div>
      </div>
    </section>

    <!-- Benefits Section -->
    <section class="section benefits-section">
      <div class="container grid grid-2 reveal">
        <div class="feature">
          <h3 class="section-title">品质保证</h3>
          <p>精选比利时可可豆，传统工艺手工制作，每一块巧克力都经过严格的品质检验。</p>
        </div>
        <div class="feature">
          <h3 class="section-title">礼品包装</h3>
          <p>精美包装设计，是送礼的完美选择。支持个性化定制，让每份礼物都独一无二。</p>
        </div>
      </div>
    </section>

    <!-- Newsletter Section -->
    <section class="section">
      <div class="container newsletter reveal">
        <h3 class="section-title">订阅优惠</h3>
        <p class="section-sub">订阅我们的优惠信息，第一时间获取巧克力新品和折扣。</p>
        <el-form @submit.prevent="handleSubscribe" class="newsletter-form">
          <el-input
            v-model="email"
            placeholder="请输入您的邮箱"
            size="large"
            class="newsletter-input"
          />
          <el-button
            type="primary"
            size="large"
            @click="handleSubscribe"
            :loading="subscribing"
            class="newsletter-button"
          >
            订阅
          </el-button>
        </el-form>
      </div>
    </section>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useStore } from 'vuex'
import ProductCard from '@/components/Product/ProductCard.vue'

export default {
  name: 'Home',
  components: {
    ProductCard
  },
  setup() {
    const store = useStore()
    
    const email = ref('')
    const subscribing = ref(false)
    const loading = ref(true)
    
    const featuredProducts = computed(() => store.getters['products/featuredProducts'])
    
    const handleSubscribe = async () => {
      if (!email.value) {
        store.dispatch('notifications/showNotification', {
          type: 'warning',
          message: '请输入邮箱地址'
        })
        return
      }
      
      subscribing.value = true
      try {
        // 这里可以调用订阅API
        await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
        
        store.dispatch('notifications/showNotification', {
          type: 'success',
          message: '订阅成功！'
        })
        
        email.value = ''
      } catch (error) {
        store.dispatch('notifications/showNotification', {
          type: 'error',
          message: '订阅失败，请稍后重试'
        })
      } finally {
        subscribing.value = false
      }
    }
    
    onMounted(async () => {
      try {
        await store.dispatch('products/loadFeaturedProducts')
      } catch (error) {
        console.error('Load featured products error:', error)
      } finally {
        loading.value = false
      }
      
      // 初始化滚动动画
      initRevealAnimation()
    })
    
    const initRevealAnimation = () => {
      const observer = new IntersectionObserver(entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('in-view')
            observer.unobserve(entry.target)
          }
        })
      }, { threshold: 0.1 })
      
      document.querySelectorAll('.reveal').forEach(el => {
        observer.observe(el)
      })
    }
    
    return {
      email,
      subscribing,
      loading,
      featuredProducts,
      handleSubscribe
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  .hero {
    padding: 80px 0;
    
    .hero-inner {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 60px;
      align-items: center;
    }
    
    .hero-content {
      h1 {
        font-size: 48px;
        font-weight: 800;
        line-height: 1.2;
        margin-bottom: 24px;
        color: var(--color-ink);
      }
      
      p {
        font-size: 18px;
        line-height: 1.6;
        margin-bottom: 32px;
        color: #666;
      }
      
      .hero-actions {
        display: flex;
        gap: 16px;
      }
    }
    
    .hero-card {
      transform: rotate(-3deg);
      
      .box-stack {
        display: flex;
        flex-direction: column;
        gap: 16px;
        
        .choco-box {
          padding: 24px;
          border-radius: var(--radius-lg);
          color: white;
          font-weight: 700;
          text-align: center;
          font-size: 18px;
          
          &--1 {
            @include chocolate-gradient;
          }
          
          &--2 {
            background: linear-gradient(135deg, #D2691E, #CD853F);
          }
          
          &--3 {
            background: linear-gradient(135deg, #F5DEB3, #DEB887);
            color: var(--color-ink);
          }
        }
      }
    }
  }
  
  .stickers {
    display: flex;
    justify-content: center;
    gap: 24px;
    flex-wrap: wrap;
    
    .sticker {
      background: var(--color-primary);
      color: var(--color-ink);
      padding: 12px 24px;
      border-radius: 999px;
      font-weight: 600;
      font-size: 14px;
    }
  }
  
  .section {
    padding: 80px 0;
    
    &.benefits-section {
      background: var(--color-sand);
    }
  }
  
  .section-title {
    font-size: 36px;
    font-weight: 800;
    text-align: center;
    margin-bottom: 16px;
    color: var(--color-ink);
  }
  
  .section-sub {
    text-align: center;
    font-size: 18px;
    color: #666;
    margin-bottom: 48px;
  }
  
  .product-card {
    @include card-style;
    text-align: center;
    
    .product-art {
      height: 120px;
      border-radius: var(--radius-md);
      @include chocolate-gradient;
      margin-bottom: 20px;
    }
    
    .product-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 8px;
      color: var(--color-ink);
    }
    
    .badge {
      display: inline-block;
      background: var(--color-teal);
      color: white;
      padding: 4px 12px;
      border-radius: 999px;
      font-size: 12px;
      font-weight: 600;
      margin-bottom: 8px;
    }
    
    .price {
      font-size: 16px;
      color: #666;
      margin-bottom: 20px;
    }
  }
  
  .ooo-block {
    background: var(--color-primary);
    padding: 60px 0;
    text-align: center;
    
    .ooo-text {
      font-size: 24px;
      font-weight: 700;
      color: var(--color-ink);
    }
    
    .ooo-row {
      display: flex;
      justify-content: center;
      gap: 16px;
      margin: 20px 0;
      
      .ooo {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
      }
    }
  }
  
  .feature {
    h3 {
      font-size: 28px;
      font-weight: 700;
      margin-bottom: 16px;
      color: var(--color-ink);
    }
    
    p {
      font-size: 16px;
      line-height: 1.6;
      color: #666;
    }
  }
  
  .newsletter {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
    
    .newsletter-form {
      display: flex;
      gap: 16px;
      margin-top: 32px;
      
      .newsletter-input {
        flex: 1;
      }
    }
  }
  
  .loading-container {
    padding: 40px 0;
  }
  
  .text-center {
    text-align: center;
  }
}

@include respond-to('mobile') {
  .home {
    .hero {
      padding: 40px 0;
      
      .hero-inner {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
      }
      
      .hero-content {
        h1 {
          font-size: 32px;
        }
        
        .hero-actions {
          justify-content: center;
        }
      }
    }
    
    .section {
      padding: 40px 0;
    }
    
    .section-title {
      font-size: 28px;
    }
    
    .stickers {
      gap: 12px;
      
      .sticker {
        font-size: 12px;
        padding: 8px 16px;
      }
    }
    
    .newsletter-form {
      flex-direction: column;
    }
  }
}
</style>
