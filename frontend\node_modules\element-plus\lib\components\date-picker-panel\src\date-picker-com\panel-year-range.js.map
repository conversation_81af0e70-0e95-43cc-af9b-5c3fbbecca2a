{"version": 3, "file": "panel-year-range.js", "sources": ["../../../../../../../packages/components/date-picker-panel/src/date-picker-com/panel-year-range.vue"], "sourcesContent": ["<template>\n  <div :class=\"panelKls\">\n    <div :class=\"ppNs.e('body-wrapper')\">\n      <slot name=\"sidebar\" :class=\"ppNs.e('sidebar')\" />\n      <div v-if=\"hasShortcuts\" :class=\"ppNs.e('sidebar')\">\n        <button\n          v-for=\"(shortcut, key) in shortcuts\"\n          :key=\"key\"\n          type=\"button\"\n          :class=\"ppNs.e('shortcut')\"\n          :disabled=\"disabled\"\n          @click=\"handleShortcutClick(shortcut)\"\n        >\n          {{ shortcut.text }}\n        </button>\n      </div>\n      <div :class=\"ppNs.e('body')\">\n        <div :class=\"leftPanelKls.content\">\n          <div :class=\"drpNs.e('header')\">\n            <button\n              type=\"button\"\n              :class=\"leftPanelKls.arrowLeftBtn\"\n              :disabled=\"disabled\"\n              @click=\"leftPrevYear\"\n            >\n              <slot name=\"prev-year\">\n                <el-icon><d-arrow-left /></el-icon>\n              </slot>\n            </button>\n            <button\n              v-if=\"unlinkPanels\"\n              type=\"button\"\n              :disabled=\"!enableYearArrow || disabled\"\n              :class=\"leftPanelKls.arrowRightBtn\"\n              @click=\"leftNextYear\"\n            >\n              <slot name=\"next-year\">\n                <el-icon><d-arrow-right /></el-icon>\n              </slot>\n            </button>\n            <div>{{ leftLabel }}</div>\n          </div>\n          <year-table\n            selection-mode=\"range\"\n            :date=\"leftDate\"\n            :min-date=\"minDate\"\n            :max-date=\"maxDate\"\n            :range-state=\"rangeState\"\n            :disabled-date=\"disabledDate\"\n            :disabled=\"disabled\"\n            :cell-class-name=\"cellClassName\"\n            @changerange=\"handleChangeRange\"\n            @pick=\"handleRangePick\"\n            @select=\"onSelect\"\n          />\n        </div>\n        <div :class=\"rightPanelKls.content\">\n          <div :class=\"drpNs.e('header')\">\n            <button\n              v-if=\"unlinkPanels\"\n              type=\"button\"\n              :disabled=\"!enableYearArrow || disabled\"\n              :class=\"rightPanelKls.arrowLeftBtn\"\n              @click=\"rightPrevYear\"\n            >\n              <slot name=\"prev-year\">\n                <el-icon><d-arrow-left /></el-icon>\n              </slot>\n            </button>\n            <button\n              type=\"button\"\n              :class=\"rightPanelKls.arrowRightBtn\"\n              :disabled=\"disabled\"\n              @click=\"rightNextYear\"\n            >\n              <slot name=\"next-year\">\n                <el-icon><d-arrow-right /></el-icon>\n              </slot>\n            </button>\n            <div>{{ rightLabel }}</div>\n          </div>\n          <year-table\n            selection-mode=\"range\"\n            :date=\"rightDate\"\n            :min-date=\"minDate\"\n            :max-date=\"maxDate\"\n            :range-state=\"rangeState\"\n            :disabled-date=\"disabledDate\"\n            :disabled=\"disabled\"\n            :cell-class-name=\"cellClassName\"\n            @changerange=\"handleChangeRange\"\n            @pick=\"handleRangePick\"\n            @select=\"onSelect\"\n          />\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, ref, toRef, unref, useSlots, watch } from 'vue'\nimport dayjs from 'dayjs'\nimport { isArray } from '@element-plus/utils'\nimport { DArrowLeft, DArrowRight } from '@element-plus/icons-vue'\nimport ElIcon from '@element-plus/components/icon'\nimport { useLocale } from '@element-plus/hooks'\nimport { PICKER_BASE_INJECTION_KEY } from '@element-plus/components/time-picker'\nimport {\n  panelYearRangeEmits,\n  panelYearRangeProps,\n} from '../props/panel-year-range'\nimport { useYearRangeHeader } from '../composables/use-year-range-header'\nimport { useRangePicker } from '../composables/use-range-picker'\nimport {\n  correctlyParseUserInput,\n  getDefaultValue,\n  isValidRange,\n} from '../utils'\nimport { ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY } from '../constants'\nimport YearTable from './basic-year-table.vue'\n\nimport type { Dayjs } from 'dayjs'\n\ndefineOptions({\n  name: 'DatePickerYearRange',\n})\n\nconst props = defineProps(panelYearRangeProps)\nconst emit = defineEmits(panelYearRangeEmits)\nconst step = 10\nconst unit = 'year'\n\nconst { lang } = useLocale()\nconst leftDate = ref(dayjs().locale(lang.value))\nconst rightDate = ref(dayjs().locale(lang.value).add(step, unit))\nconst isDefaultFormat = inject(\n  ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY,\n  undefined\n) as any\nconst pickerBase = inject(PICKER_BASE_INJECTION_KEY) as any\nconst { shortcuts, disabledDate, cellClassName } = pickerBase.props\nconst format = toRef(pickerBase.props, 'format')\nconst defaultValue = toRef(pickerBase.props, 'defaultValue')\n\nconst {\n  minDate,\n  maxDate,\n  rangeState,\n  ppNs,\n  drpNs,\n\n  handleChangeRange,\n  handleRangeConfirm,\n  handleShortcutClick,\n  onSelect,\n  onReset,\n} = useRangePicker(props, {\n  defaultValue,\n  leftDate,\n  rightDate,\n  step,\n  unit,\n  onParsedValueChanged,\n})\n\nconst {\n  leftPrevYear,\n  rightNextYear,\n  leftNextYear,\n  rightPrevYear,\n  leftLabel,\n  rightLabel,\n  leftYear,\n  rightYear,\n} = useYearRangeHeader({\n  unlinkPanels: toRef(props, 'unlinkPanels'),\n  leftDate,\n  rightDate,\n})\n\nconst hasShortcuts = computed(() => !!shortcuts.length)\n\nconst panelKls = computed(() => [\n  ppNs.b(),\n  drpNs.b(),\n  ppNs.is('border', props.border),\n  ppNs.is('disabled', props.disabled),\n  {\n    'has-sidebar': Boolean(useSlots().sidebar) || hasShortcuts.value,\n  },\n])\n\nconst leftPanelKls = computed(() => {\n  return {\n    content: [ppNs.e('content'), drpNs.e('content'), 'is-left'],\n    arrowLeftBtn: [ppNs.e('icon-btn'), 'd-arrow-left'],\n    arrowRightBtn: [\n      ppNs.e('icon-btn'),\n      { [ppNs.is('disabled')]: !enableYearArrow.value },\n      'd-arrow-right',\n    ],\n  }\n})\n\nconst rightPanelKls = computed(() => {\n  return {\n    content: [ppNs.e('content'), drpNs.e('content'), 'is-right'],\n    arrowLeftBtn: [\n      ppNs.e('icon-btn'),\n      { 'is-disabled': !enableYearArrow.value },\n      'd-arrow-left',\n    ],\n    arrowRightBtn: [ppNs.e('icon-btn'), 'd-arrow-right'],\n  }\n})\n\nconst enableYearArrow = computed(() => {\n  return props.unlinkPanels && rightYear.value > leftYear.value + 1\n})\n\ntype RangePickValue = {\n  minDate: Dayjs\n  maxDate: Dayjs\n}\nconst handleRangePick = (val: RangePickValue, close = true) => {\n  const minDate_ = val.minDate\n  const maxDate_ = val.maxDate\n  if (maxDate.value === maxDate_ && minDate.value === minDate_) {\n    return\n  }\n  emit('calendar-change', [minDate_.toDate(), maxDate_ && maxDate_.toDate()])\n  maxDate.value = maxDate_\n  minDate.value = minDate_\n\n  if (!close) return\n  handleRangeConfirm()\n}\n\nconst parseUserInput = (value: Dayjs | Dayjs[]) => {\n  return correctlyParseUserInput(\n    value,\n    format.value,\n    lang.value,\n    isDefaultFormat\n  )\n}\n\nconst formatToString = (value: Dayjs[] | Dayjs) => {\n  return isArray(value)\n    ? value.map((day) => day.format(format.value))\n    : value.format(format.value)\n}\n\nconst isValidValue = (date: [Dayjs, Dayjs]) => {\n  return (\n    isValidRange(date) &&\n    (disabledDate\n      ? !disabledDate(date[0].toDate()) && !disabledDate(date[1].toDate())\n      : true)\n  )\n}\n\nconst handleClear = () => {\n  const defaultArr = getDefaultValue(unref(defaultValue), {\n    lang: unref(lang),\n    step,\n    unit,\n    unlinkPanels: props.unlinkPanels,\n  })\n  leftDate.value = defaultArr[0]\n  rightDate.value = defaultArr[1]\n  emit('pick', null)\n}\n\nfunction onParsedValueChanged(\n  minDate: Dayjs | undefined,\n  maxDate: Dayjs | undefined\n) {\n  if (props.unlinkPanels && maxDate) {\n    const minDateYear = minDate?.year() || 0\n    const maxDateYear = maxDate.year()\n\n    rightDate.value =\n      minDateYear + step > maxDateYear ? maxDate.add(step, unit) : maxDate\n  } else {\n    rightDate.value = leftDate.value.add(step, unit)\n  }\n}\n\nwatch(\n  () => props.visible,\n  (visible) => {\n    if (!visible && rangeState.value.selecting) {\n      onReset(props.parsedValue)\n      onSelect(false)\n    }\n  }\n)\n\nemit('set-picker-option', ['isValidValue', isValidValue])\nemit('set-picker-option', ['parseUserInput', parseUserInput])\nemit('set-picker-option', ['formatToString', formatToString])\nemit('set-picker-option', ['handleClear', handleClear])\n</script>\n"], "names": ["useLocale", "ref", "dayjs", "inject", "ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY", "PICKER_BASE_INJECTION_KEY", "toRef", "useRangePicker", "useYearRangeHeader", "computed", "useSlots", "correctlyParseUserInput", "isArray", "isValidRange", "getDefaultValue", "unref", "watch", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref", "_createElementVNode", "_renderSlot"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;uCA4Hc,CAAA;AAAA,EACZ,IAAM,EAAA,qBAAA;AACR;;;;;;;AAOA,IAAM,MAAA,EAAE,IAAK,EAAA,GAAIA,eAAU,EAAA,CAAA;AAC3B,IAAA,MAAM,WAAWC,OAAI,CAAAC,yBAAA,GAAQ,MAAO,CAAA,IAAA,CAAK,KAAK,CAAC,CAAA,CAAA;AAC/C,IAAM,MAAA,SAAA,GAAYD,OAAI,CAAAC,yBAAA,EAAQ,CAAA,MAAA,CAAO,IAAK,CAAA,KAAK,CAAE,CAAA,GAAA,CAAI,IAAM,EAAA,IAAI,CAAC,CAAA,CAAA;AAChE,IAAA,MAAM,eAAkB,GAAAC,UAAA,CAAAC,qDAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IACtB,MAAA,UAAA,GAAAD,UAAA,CAAAE,qCAAA,CAAA,CAAA;AAAA,IACA,MAAA,EAAA,SAAA,EAAA,YAAA,EAAA,aAAA,EAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AAAA,IACF,MAAA,MAAA,GAAAC,SAAA,CAAA,UAAA,CAAA,KAAA,EAAA,QAAA,CAAA,CAAA;AACA,IAAM,MAAA,YAAA,YAAoB,CAAyB,UAAA,CAAA,KAAA,EAAA,cAAA,CAAA,CAAA;AACnD,IAAA,MAAM;AACN,MAAA,OAAe;AACf,MAAA,OAAqB;AAErB,MAAM,UAAA;AAAA,MACJ,IAAA;AAAA,MACA,KAAA;AAAA,MACA,iBAAA;AAAA,MACA,kBAAA;AAAA,MACA,mBAAA;AAAA,MAEA,QAAA;AAAA,MACA,OAAA;AAAA,KACA,GAAAC,6BAAA,CAAA,KAAA,EAAA;AAAA,MACA,YAAA;AAAA,MACA,QAAA;AAAA,MACF;AAA0B,MACxB,IAAA;AAAA,MACA,IAAA;AAAA,MACA,oBAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACA,MAAA;AAAA,MACA,YAAA;AAAA,MACD,aAAA;AAED,MAAM,YAAA;AAAA,MACJ,aAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA;AAAA,MACA,QAAA;AAAA,MACA,SAAA;AAAA,KACA,GAAAC,qCAAA,CAAA;AAAA,MACA,YAAA,EAAAF,SAAA,CAAA,KAAA,EAAA,cAAA,CAAA;AAAA,MACA,QAAA;AAAA,eACqB;AAAA,KACrB,CAAA,CAAA;AAAyC,IACzC,MAAA,YAAA,GAAAG,YAAA,CAAA,MAAA,CAAA,CAAA,SAAA,CAAA,MAAA,CAAA,CAAA;AAAA,IACA,MAAA,QAAA,GAAAA,YAAA,CAAA,MAAA;AAAA,MACD,IAAA,CAAA,CAAA,EAAA;AAED,MAAA;AAEA,MAAM,IAAA,CAAA,EAAA,CAAA,gBAAoB,MAAM,CAAA;AAAA,MAC9B,KAAK,EAAE,CAAA,UAAA,EAAA,KAAA,CAAA,QAAA,CAAA;AAAA,MACP;AAAQ,QACH,aAAa,EAAA,OAAM,CAAMC,YAAA,EAAA,CAAA,OAAA,CAAA,IAAA,YAAA,CAAA,KAAA;AAAA,OACzB;AAA6B,KAClC,CAAA,CAAA;AAAA,IAAA,qBACyBD,YAAA,CAAA,MAAS;AAA2B,MAC7D,OAAA;AAAA,QACD,OAAA,EAAA,CAAA,IAAA,CAAA,CAAA,CAAA,SAAA,CAAA,EAAA,KAAA,CAAA,CAAA,CAAA,SAAA,CAAA,EAAA,SAAA,CAAA;AAED,QAAM,YAAA,EAAA,CAAA,iBAA8B,CAAA,EAAA,cAAA,CAAA;AAClC,QAAO,aAAA,EAAA;AAAA,UACL,IAAA,CAAA,CAAA,CAAS,UAAQ,CAAA;AAAyC,+BACpC,CAAA,GAAA,CAAA,eAA2B,CAAA,KAAA,EAAA;AAAA,UAClC,eAAA;AAAA,SACb;AAAiB,OACjB,CAAA;AAAgD,KAChD,CAAA,CAAA;AAAA,IACF,MAAA,aAAA,GAAAA,YAAA,CAAA,MAAA;AAAA,MACF,OAAA;AAAA,QACD,OAAA,EAAA,CAAA,IAAA,CAAA,CAAA,CAAA,SAAA,CAAA,EAAA,KAAA,CAAA,CAAA,CAAA,SAAA,CAAA,EAAA,UAAA,CAAA;AAED,QAAM,YAAA,EAAA;AACJ,UAAO,IAAA,CAAA,CAAA,CAAA,UAAA,CAAA;AAAA,UACL,EAAA,aAAiB,EAAA,CAAA,eAAkB,CAAA,KAAW,EAAA;AAAa,UAC7C,cAAA;AAAA,SACZ;AAAiB,QAAA,aACf,EAAA,CAAA,IAAgB,CAAA,CAAA,CAAA,UAAA,CAAA,EAAgB,eAAM,CAAA;AAAA,OACxC,CAAA;AAAA,KACF,CAAA,CAAA;AAAA,IAAA,qBACgB,GAAAA,YAAO,CAAA;AAA4B,MACrD,OAAA,KAAA,CAAA,YAAA,IAAA,SAAA,CAAA,KAAA,GAAA,QAAA,CAAA,KAAA,GAAA,CAAA,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,eAAA,GAAkB,WAAe,GAAA,IAAA,KAAA;AACrC,MAAA,MAAA,QAAa,GAAA,GAAA,CAAA,OAAA,CAAgB;AAAmC,MACjE,MAAA,QAAA,GAAA,GAAA,CAAA,OAAA,CAAA;AAMD,MAAA,IAAM,OAAkB,CAAA,KAAA,KAAA,QAAsB,IAAA,OAAiB,CAAA,KAAA,KAAA,QAAA,EAAA;AAC7D,QAAA;AACA,OAAA;AACA,MAAA,IAAI,CAAQ,iBAAU,EAAY,CAAA,QAAA,CAAA,MAAA,EAAQ,UAAU,IAAU,QAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA;AAC5D,MAAA,OAAA,CAAA,KAAA,GAAA,QAAA,CAAA;AAAA,MACF,OAAA,CAAA,KAAA,GAAA,QAAA,CAAA;AACA,MAAK,IAAA,CAAA,KAAA;AACL,QAAA,OAAgB;AAChB,MAAA,kBAAgB,EAAA,CAAA;AAEhB,KAAA,CAAA;AACA,IAAmB,MAAA,cAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MACrB,OAAAE,6BAAA,CAAA,KAAA,EAAA,MAAA,CAAA,KAAA,EAAA,IAAA,CAAA,KAAA,EAAA,eAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAO,MAAA,cAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MACL,OAAAC,cAAA,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAA,GAAA,CAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,CAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AAAA,KAAA,CAAA;AACO,IAAA,MACF,YAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MACL,OAAAC,kBAAA,CAAA,IAAA,CAAA,KAAA,YAAA,GAAA,CAAA,YAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,KACF,CAAA;AAAA,IACF,MAAA,WAAA,GAAA,MAAA;AAEA,MAAM,MAAA,UAAA,GAAAC,qBAA6C,CAAAC,SAAA,CAAA,YAAA,CAAA,EAAA;AACjD,QAAA,IAAA,gBAAoB,CAAA;AAES,QAC/B,IAAA;AAEA,QAAM,IAAA;AACJ,QAAA,mBACmB,CAAA;AAGb,OAER,CAAA,CAAA;AAEA,MAAA,2BAA0B,CAAA,CAAA,CAAA,CAAA;AACxB,MAAA,SAAmB,CAAA,KAAA,GAAA,UAAA,CAAA,CAAA,CAAA,CAAA;AAAqC,MACtD,IAAA,CAAA,YAAgB,CAAA,CAAA;AAAA,KAChB,CAAA;AAAA,IACA,SAAA,oBAAA,CAAA,QAAA,EAAA,QAAA,EAAA;AAAA,MAAA,sBACoB,IAAA,QAAA,EAAA;AAAA,QACrB,MAAA,WAAA,GAAA,CAAA,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,IAAA,EAAA,KAAA,CAAA,CAAA;AACD,QAAS,MAAA,sBAAoB,CAAA,IAAA,EAAA,CAAA;AAC7B,QAAU,SAAA,CAAA,KAAA,cAAoB,GAAA,IAAA,GAAA,WAAA,GAAA,QAAA,CAAA,GAAA,CAAA,IAAA,EAAA,IAAA,CAAA,GAAA,QAAA,CAAA;AAC9B,OAAA;AAAiB,QACnB,SAAA,CAAA,KAAA,GAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA;AAEA,OAAS;AAIP,KAAI;AACF,IAAMC,SAAA,CAAA,MAAA,KAAA,CAAA,OAAuB,EAAA,CAAA,OAAA,KAAU;AACvC,MAAM,IAAA,CAAA,OAAA,IAAA,gBAA2B,CAAA,SAAA,EAAA;AAEjC,QAAU,OAAA,CAAA,KAAA,CAAA;AACqD,QAC1D,QAAA,CAAA,KAAA,CAAA,CAAA;AACL,OAAA;AAA+C,KACjD,CAAA,CAAA;AAAA,IACF,IAAA,CAAA,mBAAA,EAAA,CAAA,cAAA,EAAA,YAAA,CAAA,CAAA,CAAA;AAEA,IAAA,IAAA,CAAA,mBAAA,EAAA,CAAA,gBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AAAA,IAAA,wBACc,EAAA,CAAA,gBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AAAA,IAAA,IACC,CAAA,mBAAA,EAAA,CAAA,aAAA,EAAA,WAAA,CAAA,CAAA,CAAA;AACX,IAAA,OAAA,CAAI,IAAC,EAAA,MAAW,KAAW;AACzB,MAAA,OAAAC,eAAyB,EAAAC,sBAAA,CAAA,KAAA,EAAA;AACzB,QAAA,KAAA,EAAAC,kBAAc,CAAAC,SAAA,CAAA,QAAA,CAAA,CAAA;AAAA,OAChB,EAAA;AAAA,QACFC,sBAAA,CAAA,KAAA,EAAA;AAAA,UACF,KAAA,EAAAF,kBAAA,CAAAC,SAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAA,CAAA;AAEA,SAAK,EAAqB;AAC1B,UAA0BE,cAAA,CAAA,IAAA,CAAA,MAAmB,EAAA,SAAA,EAAA;AAC7C,YAA0B,KAAA,EAAAH,kBAAmB,CAAAC,SAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA;AAC7C,WAA0B,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}