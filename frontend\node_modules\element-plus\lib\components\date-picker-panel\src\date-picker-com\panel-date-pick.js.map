{"version": 3, "file": "panel-date-pick.js", "sources": ["../../../../../../../packages/components/date-picker-panel/src/date-picker-com/panel-date-pick.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[\n      ppNs.b(),\n      dpNs.b(),\n      ppNs.is('border', border),\n      ppNs.is('disabled', disabled),\n      {\n        'has-sidebar': $slots.sidebar || hasShortcuts,\n        'has-time': showTime,\n      },\n    ]\"\n  >\n    <div :class=\"ppNs.e('body-wrapper')\">\n      <slot name=\"sidebar\" :class=\"ppNs.e('sidebar')\" />\n      <div v-if=\"hasShortcuts\" :class=\"ppNs.e('sidebar')\">\n        <button\n          v-for=\"(shortcut, key) in shortcuts\"\n          :key=\"key\"\n          type=\"button\"\n          :disabled=\"disabled\"\n          :class=\"ppNs.e('shortcut')\"\n          @click=\"handleShortcutClick(shortcut)\"\n        >\n          {{ shortcut.text }}\n        </button>\n      </div>\n      <div :class=\"ppNs.e('body')\">\n        <div v-if=\"showTime\" :class=\"dpNs.e('time-header')\">\n          <span :class=\"dpNs.e('editor-wrap')\">\n            <el-input\n              :placeholder=\"t('el.datepicker.selectDate')\"\n              :model-value=\"visibleDate\"\n              size=\"small\"\n              :validate-event=\"false\"\n              :disabled=\"disabled\"\n              @input=\"(val) => (userInputDate = val)\"\n              @change=\"handleVisibleDateChange\"\n            />\n          </span>\n          <span\n            v-click-outside=\"handleTimePickClose\"\n            :class=\"dpNs.e('editor-wrap')\"\n          >\n            <el-input\n              :placeholder=\"t('el.datepicker.selectTime')\"\n              :model-value=\"visibleTime\"\n              size=\"small\"\n              :validate-event=\"false\"\n              :disabled=\"disabled\"\n              @focus=\"onTimePickerInputFocus\"\n              @input=\"(val) => (userInputTime = val)\"\n              @change=\"handleVisibleTimeChange\"\n            />\n            <time-pick-panel\n              :visible=\"timePickerVisible\"\n              :format=\"timeFormat\"\n              :parsed-value=\"innerDate\"\n              @pick=\"handleTimePick\"\n            />\n          </span>\n        </div>\n        <div\n          v-show=\"currentView !== 'time'\"\n          :class=\"[\n            dpNs.e('header'),\n            (currentView === 'year' || currentView === 'month') &&\n              dpNs.e('header--bordered'),\n          ]\"\n        >\n          <span :class=\"dpNs.e('prev-btn')\">\n            <button\n              type=\"button\"\n              :aria-label=\"t(`el.datepicker.prevYear`)\"\n              class=\"d-arrow-left\"\n              :class=\"ppNs.e('icon-btn')\"\n              :disabled=\"disabled\"\n              @click=\"moveByYear(false)\"\n            >\n              <slot name=\"prev-year\">\n                <el-icon><d-arrow-left /></el-icon>\n              </slot>\n            </button>\n            <button\n              v-show=\"currentView === 'date'\"\n              type=\"button\"\n              :aria-label=\"t(`el.datepicker.prevMonth`)\"\n              :class=\"ppNs.e('icon-btn')\"\n              class=\"arrow-left\"\n              :disabled=\"disabled\"\n              @click=\"moveByMonth(false)\"\n            >\n              <slot name=\"prev-month\">\n                <el-icon><arrow-left /></el-icon>\n              </slot>\n            </button>\n          </span>\n          <span\n            role=\"button\"\n            :class=\"dpNs.e('header-label')\"\n            aria-live=\"polite\"\n            tabindex=\"0\"\n            @keydown.enter=\"showPicker('year')\"\n            @click=\"showPicker('year')\"\n            >{{ yearLabel }}</span\n          >\n          <span\n            v-show=\"currentView === 'date'\"\n            role=\"button\"\n            aria-live=\"polite\"\n            tabindex=\"0\"\n            :class=\"[\n              dpNs.e('header-label'),\n              { active: currentView === 'month' },\n            ]\"\n            @keydown.enter=\"showPicker('month')\"\n            @click=\"showPicker('month')\"\n            >{{ t(`el.datepicker.month${month + 1}`) }}</span\n          >\n          <span :class=\"dpNs.e('next-btn')\">\n            <button\n              v-show=\"currentView === 'date'\"\n              type=\"button\"\n              :aria-label=\"t(`el.datepicker.nextMonth`)\"\n              :class=\"ppNs.e('icon-btn')\"\n              class=\"arrow-right\"\n              :disabled=\"disabled\"\n              @click=\"moveByMonth(true)\"\n            >\n              <slot name=\"next-month\">\n                <el-icon><arrow-right /></el-icon>\n              </slot>\n            </button>\n            <button\n              type=\"button\"\n              :aria-label=\"t(`el.datepicker.nextYear`)\"\n              :class=\"ppNs.e('icon-btn')\"\n              class=\"d-arrow-right\"\n              :disabled=\"disabled\"\n              @click=\"moveByYear(true)\"\n            >\n              <slot name=\"next-year\">\n                <el-icon><d-arrow-right /></el-icon>\n              </slot>\n            </button>\n          </span>\n        </div>\n        <div :class=\"ppNs.e('content')\" @keydown=\"handleKeydownTable\">\n          <date-table\n            v-if=\"currentView === 'date'\"\n            ref=\"currentViewRef\"\n            :selection-mode=\"selectionMode\"\n            :date=\"innerDate\"\n            :parsed-value=\"parsedValue\"\n            :disabled-date=\"disabledDate\"\n            :disabled=\"disabled\"\n            :cell-class-name=\"cellClassName\"\n            :show-week-number=\"showWeekNumber\"\n            @pick=\"handleDatePick\"\n          />\n          <year-table\n            v-if=\"currentView === 'year'\"\n            ref=\"currentViewRef\"\n            :selection-mode=\"selectionMode\"\n            :date=\"innerDate\"\n            :disabled-date=\"disabledDate\"\n            :disabled=\"disabled\"\n            :parsed-value=\"parsedValue\"\n            :cell-class-name=\"cellClassName\"\n            @pick=\"handleYearPick\"\n          />\n          <month-table\n            v-if=\"currentView === 'month'\"\n            ref=\"currentViewRef\"\n            :selection-mode=\"selectionMode\"\n            :date=\"innerDate\"\n            :parsed-value=\"parsedValue\"\n            :disabled-date=\"disabledDate\"\n            :disabled=\"disabled\"\n            :cell-class-name=\"cellClassName\"\n            @pick=\"handleMonthPick\"\n          />\n        </div>\n      </div>\n    </div>\n    <div\n      v-if=\"showFooter && footerVisible && footerFilled\"\n      :class=\"ppNs.e('footer')\"\n    >\n      <el-button\n        v-show=\"!isMultipleType && showNow\"\n        text\n        size=\"small\"\n        :class=\"ppNs.e('link-btn')\"\n        :disabled=\"disabledNow\"\n        @click=\"changeToNow\"\n      >\n        {{ t('el.datepicker.now') }}\n      </el-button>\n      <el-button\n        v-if=\"showConfirm\"\n        plain\n        size=\"small\"\n        :class=\"ppNs.e('link-btn')\"\n        :disabled=\"disabledConfirm\"\n        @click=\"onConfirm\"\n      >\n        {{ t('el.datepicker.confirm') }}\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  inject,\n  nextTick,\n  ref,\n  toRef,\n  useAttrs,\n  useSlots,\n  watch,\n} from 'vue'\nimport dayjs from 'dayjs'\nimport ElButton from '@element-plus/components/button'\nimport { ClickOutside as vClickOutside } from '@element-plus/directives'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport ElInput from '@element-plus/components/input'\nimport {\n  DEFAULT_FORMATS_DATE,\n  DEFAULT_FORMATS_TIME,\n  PICKER_BASE_INJECTION_KEY,\n  TimePickPanel,\n  extractDateFormat,\n  extractTimeFormat,\n} from '@element-plus/components/time-picker'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { isArray, isFunction } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport {\n  ArrowLeft,\n  ArrowRight,\n  DArrowLeft,\n  DArrowRight,\n} from '@element-plus/icons-vue'\nimport { panelDatePickProps } from '../props/panel-date-pick'\nimport {\n  correctlyParseUserInput,\n  getValidDateOfMonth,\n  getValidDateOfYear,\n} from '../utils'\nimport { ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY } from '../constants'\nimport DateTable from './basic-date-table.vue'\nimport MonthTable from './basic-month-table.vue'\nimport YearTable from './basic-year-table.vue'\n\nimport type { SetupContext } from 'vue'\nimport type { ConfigType, Dayjs } from 'dayjs'\nimport type { PanelDatePickProps } from '../props/panel-date-pick'\nimport type {\n  DateTableEmits,\n  DatesPickerEmits,\n  MonthsPickerEmits,\n  WeekPickerEmits,\n  YearsPickerEmits,\n} from '../props/basic-date-table'\n\ntype DatePickType = PanelDatePickProps['type']\n// todo\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst timeWithinRange = (_: ConfigType, __: any, ___: string) => true\nconst props = defineProps(panelDatePickProps)\nconst contextEmit = defineEmits(['pick', 'set-picker-option', 'panel-change'])\nconst ppNs = useNamespace('picker-panel')\nconst dpNs = useNamespace('date-picker')\nconst attrs = useAttrs()\nconst slots = useSlots()\n\nconst { t, lang } = useLocale()\nconst pickerBase = inject(PICKER_BASE_INJECTION_KEY) as any\nconst isDefaultFormat = inject(\n  ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY,\n  undefined\n) as any\nconst { shortcuts, disabledDate, cellClassName, defaultTime } = pickerBase.props\nconst defaultValue = toRef(pickerBase.props, 'defaultValue')\n\nconst currentViewRef = ref<{ focus: () => void }>()\n\nconst innerDate = ref(dayjs().locale(lang.value))\n\nconst isChangeToNow = ref(false)\n\nlet isShortcut = false\n\nconst defaultTimeD = computed(() => {\n  return dayjs(defaultTime).locale(lang.value)\n})\n\nconst month = computed(() => {\n  return innerDate.value.month()\n})\n\nconst year = computed(() => {\n  return innerDate.value.year()\n})\n\nconst selectableRange = ref([])\nconst userInputDate = ref<string | null>(null)\nconst userInputTime = ref<string | null>(null)\n// todo update to disableHour\nconst checkDateWithinRange = (date: ConfigType) => {\n  return selectableRange.value.length > 0\n    ? timeWithinRange(date, selectableRange.value, props.format || 'HH:mm:ss')\n    : true\n}\nconst formatEmit = (emitDayjs: Dayjs) => {\n  if (\n    defaultTime &&\n    !visibleTime.value &&\n    !isChangeToNow.value &&\n    !isShortcut\n  ) {\n    return defaultTimeD.value\n      .year(emitDayjs.year())\n      .month(emitDayjs.month())\n      .date(emitDayjs.date())\n  }\n  if (showTime.value) return emitDayjs.millisecond(0)\n  return emitDayjs.startOf('day')\n}\nconst emit = (value: Dayjs | Dayjs[], ...args: any[]) => {\n  if (!value) {\n    contextEmit('pick', value, ...args)\n  } else if (isArray(value)) {\n    const dates = value.map(formatEmit)\n    contextEmit('pick', dates, ...args)\n  } else {\n    contextEmit('pick', formatEmit(value), ...args)\n  }\n  userInputDate.value = null\n  userInputTime.value = null\n  isChangeToNow.value = false\n  isShortcut = false\n}\nconst handleDatePick = async (value: DateTableEmits, keepOpen?: boolean) => {\n  if (selectionMode.value === 'date') {\n    value = value as Dayjs\n    let newDate = props.parsedValue\n      ? (props.parsedValue as Dayjs)\n          .year(value.year())\n          .month(value.month())\n          .date(value.date())\n      : value\n    // change default time while out of selectableRange\n    if (!checkDateWithinRange(newDate)) {\n      newDate = (selectableRange.value[0][0] as Dayjs)\n        .year(value.year())\n        .month(value.month())\n        .date(value.date())\n    }\n    innerDate.value = newDate\n    emit(newDate, showTime.value || keepOpen)\n  } else if (selectionMode.value === 'week') {\n    emit((value as WeekPickerEmits).date)\n  } else if (selectionMode.value === 'dates') {\n    emit(value as DatesPickerEmits, true) // set true to keep panel open\n  }\n}\n\nconst moveByMonth = (forward: boolean) => {\n  const action = forward ? 'add' : 'subtract'\n  innerDate.value = innerDate.value[action](1, 'month')\n  handlePanelChange('month')\n}\n\nconst moveByYear = (forward: boolean) => {\n  const currentDate = innerDate.value\n  const action = forward ? 'add' : 'subtract'\n\n  innerDate.value =\n    currentView.value === 'year'\n      ? currentDate[action](10, 'year')\n      : currentDate[action](1, 'year')\n\n  handlePanelChange('year')\n}\n\nconst currentView = ref('date')\n\nconst yearLabel = computed(() => {\n  const yearTranslation = t('el.datepicker.year')\n  if (currentView.value === 'year') {\n    const startYear = Math.floor(year.value / 10) * 10\n    if (yearTranslation) {\n      return `${startYear} ${yearTranslation} - ${\n        startYear + 9\n      } ${yearTranslation}`\n    }\n    return `${startYear} - ${startYear + 9}`\n  }\n  return `${year.value} ${yearTranslation}`\n})\n\ntype Shortcut = {\n  value: (() => Dayjs) | Dayjs\n  onClick?: (ctx: Omit<SetupContext, 'expose'>) => void\n}\n\nconst handleShortcutClick = (shortcut: Shortcut) => {\n  const shortcutValue = isFunction(shortcut.value)\n    ? shortcut.value()\n    : shortcut.value\n  if (shortcutValue) {\n    isShortcut = true\n    emit(dayjs(shortcutValue).locale(lang.value))\n    return\n  }\n  if (shortcut.onClick) {\n    shortcut.onClick({\n      attrs,\n      slots,\n      emit: contextEmit as SetupContext['emit'],\n    })\n  }\n}\n\nconst selectionMode = computed<DatePickType>(() => {\n  const { type } = props\n  if (['week', 'month', 'months', 'year', 'years', 'dates'].includes(type))\n    return type\n  return 'date' as DatePickType\n})\n\nconst isMultipleType = computed(() => {\n  return (\n    selectionMode.value === 'dates' ||\n    selectionMode.value === 'months' ||\n    selectionMode.value === 'years'\n  )\n})\n\nconst keyboardMode = computed<string>(() => {\n  return selectionMode.value === 'date'\n    ? currentView.value\n    : selectionMode.value\n})\n\nconst hasShortcuts = computed(() => !!shortcuts.length)\n\nconst handleMonthPick = async (\n  month: number | MonthsPickerEmits,\n  keepOpen?: boolean\n) => {\n  if (selectionMode.value === 'month') {\n    innerDate.value = getValidDateOfMonth(\n      innerDate.value,\n      innerDate.value.year(),\n      month as number,\n      lang.value,\n      disabledDate\n    )\n    emit(innerDate.value, false)\n  } else if (selectionMode.value === 'months') {\n    emit(month as MonthsPickerEmits, keepOpen ?? true)\n  } else {\n    innerDate.value = getValidDateOfMonth(\n      innerDate.value,\n      innerDate.value.year(),\n      month as number,\n      lang.value,\n      disabledDate\n    )\n    currentView.value = 'date'\n    if (['month', 'year', 'date', 'week'].includes(selectionMode.value)) {\n      emit(innerDate.value, true)\n      await nextTick()\n      handleFocusPicker()\n    }\n  }\n  handlePanelChange('month')\n}\n\nconst handleYearPick = async (\n  year: number | YearsPickerEmits,\n  keepOpen?: boolean\n) => {\n  if (selectionMode.value === 'year') {\n    const data = innerDate.value.startOf('year').year(year as number)\n    innerDate.value = getValidDateOfYear(data, lang.value, disabledDate)\n    emit(innerDate.value, false)\n  } else if (selectionMode.value === 'years') {\n    emit(year as YearsPickerEmits, keepOpen ?? true)\n  } else {\n    const data = innerDate.value.year(year as number)\n    innerDate.value = getValidDateOfYear(data, lang.value, disabledDate)\n    currentView.value = 'month'\n    if (['month', 'year', 'date', 'week'].includes(selectionMode.value)) {\n      emit(innerDate.value, true)\n      await nextTick()\n      handleFocusPicker()\n    }\n  }\n  handlePanelChange('year')\n}\n\nconst showPicker = async (view: 'month' | 'year') => {\n  if (props.disabled) return\n  currentView.value = view\n  await nextTick()\n  handleFocusPicker()\n}\n\nconst showTime = computed(\n  () => props.type === 'datetime' || props.type === 'datetimerange'\n)\n\nconst footerVisible = computed(() => {\n  const showDateFooter = showTime.value || selectionMode.value === 'dates'\n  const showYearFooter = selectionMode.value === 'years'\n  const showMonthFooter = selectionMode.value === 'months'\n  const isDateView = currentView.value === 'date'\n  const isYearView = currentView.value === 'year'\n  const isMonthView = currentView.value === 'month'\n  return (\n    (showDateFooter && isDateView) ||\n    (showYearFooter && isYearView) ||\n    (showMonthFooter && isMonthView)\n  )\n})\n\nconst footerFilled = computed(\n  () => (!isMultipleType.value && props.showNow) || props.showConfirm\n)\n\nconst disabledConfirm = computed(() => {\n  if (!disabledDate) return false\n  if (!props.parsedValue) return true\n  if (isArray(props.parsedValue)) {\n    return disabledDate(props.parsedValue[0].toDate())\n  }\n  return disabledDate(props.parsedValue.toDate())\n})\nconst onConfirm = () => {\n  if (isMultipleType.value) {\n    emit(props.parsedValue as Dayjs[])\n  } else {\n    // deal with the scenario where: user opens the date time picker, then confirm without doing anything\n    let result = props.parsedValue as Dayjs\n    if (!result) {\n      const defaultTimeD = dayjs(defaultTime).locale(lang.value)\n      const defaultValueD = getDefaultValue()\n      result = defaultTimeD\n        .year(defaultValueD.year())\n        .month(defaultValueD.month())\n        .date(defaultValueD.date())\n    }\n    innerDate.value = result\n    emit(result)\n  }\n}\n\nconst disabledNow = computed(() => {\n  if (!disabledDate) return false\n  return disabledDate(dayjs().locale(lang.value).toDate())\n})\nconst changeToNow = () => {\n  // NOTE: not a permanent solution\n  //       consider disable \"now\" button in the future\n  const now = dayjs().locale(lang.value)\n  const nowDate = now.toDate()\n  isChangeToNow.value = true\n  if (\n    (!disabledDate || !disabledDate(nowDate)) &&\n    checkDateWithinRange(nowDate)\n  ) {\n    innerDate.value = dayjs().locale(lang.value)\n    emit(innerDate.value)\n  }\n}\n\nconst timeFormat = computed(() => {\n  return (\n    props.timeFormat || extractTimeFormat(props.format) || DEFAULT_FORMATS_TIME\n  )\n})\n\nconst dateFormat = computed(() => {\n  return (\n    props.dateFormat || extractDateFormat(props.format) || DEFAULT_FORMATS_DATE\n  )\n})\n\nconst visibleTime = computed(() => {\n  if (userInputTime.value) return userInputTime.value\n  if (!props.parsedValue && !defaultValue.value) return\n  return ((props.parsedValue || innerDate.value) as Dayjs).format(\n    timeFormat.value\n  )\n})\n\nconst visibleDate = computed(() => {\n  if (userInputDate.value) return userInputDate.value\n  if (!props.parsedValue && !defaultValue.value) return\n  return ((props.parsedValue || innerDate.value) as Dayjs).format(\n    dateFormat.value\n  )\n})\n\nconst timePickerVisible = ref(false)\nconst onTimePickerInputFocus = () => {\n  timePickerVisible.value = true\n}\nconst handleTimePickClose = () => {\n  timePickerVisible.value = false\n}\n\nconst getUnits = (date: Dayjs) => {\n  return {\n    hour: date.hour(),\n    minute: date.minute(),\n    second: date.second(),\n    year: date.year(),\n    month: date.month(),\n    date: date.date(),\n  }\n}\n\nconst handleTimePick = (value: Dayjs, visible: boolean, first: boolean) => {\n  const { hour, minute, second } = getUnits(value)\n  const newDate = props.parsedValue\n    ? (props.parsedValue as Dayjs).hour(hour).minute(minute).second(second)\n    : value\n  innerDate.value = newDate\n  emit(innerDate.value, true)\n  if (!first) {\n    timePickerVisible.value = visible\n  }\n}\n\nconst handleVisibleTimeChange = (value: string) => {\n  const newDate = dayjs(value, timeFormat.value).locale(lang.value)\n  if (newDate.isValid() && checkDateWithinRange(newDate)) {\n    const { year, month, date } = getUnits(innerDate.value)\n    innerDate.value = newDate.year(year).month(month).date(date)\n    userInputTime.value = null\n    timePickerVisible.value = false\n    emit(innerDate.value, true)\n  }\n}\n\nconst handleVisibleDateChange = (value: string) => {\n  const newDate = correctlyParseUserInput(\n    value,\n    dateFormat.value,\n    lang.value,\n    isDefaultFormat\n  ) as Dayjs\n  if (newDate.isValid()) {\n    if (disabledDate && disabledDate(newDate.toDate())) {\n      return\n    }\n    const { hour, minute, second } = getUnits(innerDate.value)\n    innerDate.value = newDate.hour(hour).minute(minute).second(second)\n    userInputDate.value = null\n    emit(innerDate.value, true)\n  }\n}\n\nconst isValidValue = (date: unknown) => {\n  return (\n    dayjs.isDayjs(date) &&\n    date.isValid() &&\n    (disabledDate ? !disabledDate(date.toDate()) : true)\n  )\n}\n\nconst formatToString = (value: Dayjs | Dayjs[]) => {\n  return isArray(value)\n    ? (value as Dayjs[]).map((_) => _.format(props.format))\n    : (value as Dayjs).format(props.format)\n}\n\nconst parseUserInput = (value: Dayjs) => {\n  return correctlyParseUserInput(\n    value,\n    props.format,\n    lang.value,\n    isDefaultFormat\n  )\n}\n\nconst getDefaultValue = () => {\n  const parseDate = dayjs(defaultValue.value).locale(lang.value)\n  if (!defaultValue.value) {\n    const defaultTimeDValue = defaultTimeD.value\n    return dayjs()\n      .hour(defaultTimeDValue.hour())\n      .minute(defaultTimeDValue.minute())\n      .second(defaultTimeDValue.second())\n      .locale(lang.value)\n  }\n  return parseDate\n}\n\nconst handleFocusPicker = () => {\n  if (['week', 'month', 'year', 'date'].includes(selectionMode.value)) {\n    currentViewRef.value?.focus()\n  }\n}\n\nconst _handleFocusPicker = () => {\n  handleFocusPicker()\n  // TODO: After focus the date input, the first time you use the ArrowDown keys, you cannot focus on the date cell\n  if (selectionMode.value === 'week') {\n    handleKeyControl(EVENT_CODE.down)\n  }\n}\n\nconst handleKeydownTable = (event: KeyboardEvent) => {\n  const { code } = event\n  const validCode = [\n    EVENT_CODE.up,\n    EVENT_CODE.down,\n    EVENT_CODE.left,\n    EVENT_CODE.right,\n    EVENT_CODE.home,\n    EVENT_CODE.end,\n    EVENT_CODE.pageUp,\n    EVENT_CODE.pageDown,\n  ]\n  if (validCode.includes(code)) {\n    handleKeyControl(code)\n    event.stopPropagation()\n    event.preventDefault()\n  }\n  if (\n    [EVENT_CODE.enter, EVENT_CODE.space, EVENT_CODE.numpadEnter].includes(\n      code\n    ) &&\n    userInputDate.value === null &&\n    userInputTime.value === null\n  ) {\n    event.preventDefault()\n    emit(innerDate.value, false)\n  }\n}\n\nconst handleKeyControl = (code: string) => {\n  type KeyControlMappingCallableOffset = (date: Date, step?: number) => number\n  type KeyControl = {\n    [key: string]:\n      | number\n      | KeyControlMappingCallableOffset\n      | ((date: Date, step: number) => any)\n    offset: (date: Date, step: number) => any\n  }\n  interface KeyControlMapping {\n    [key: string]: KeyControl\n  }\n\n  const { up, down, left, right, home, end, pageUp, pageDown } = EVENT_CODE\n  const mapping: KeyControlMapping = {\n    year: {\n      [up]: -4,\n      [down]: 4,\n      [left]: -1,\n      [right]: 1,\n      offset: (date: Date, step: number) =>\n        date.setFullYear(date.getFullYear() + step),\n    },\n    month: {\n      [up]: -4,\n      [down]: 4,\n      [left]: -1,\n      [right]: 1,\n      offset: (date: Date, step: number) =>\n        date.setMonth(date.getMonth() + step),\n    },\n    week: {\n      [up]: -1,\n      [down]: 1,\n      [left]: -1,\n      [right]: 1,\n      offset: (date: Date, step: number) =>\n        date.setDate(date.getDate() + step * 7),\n    },\n    date: {\n      [up]: -7,\n      [down]: 7,\n      [left]: -1,\n      [right]: 1,\n      [home]: (date: Date) => -date.getDay(),\n      [end]: (date: Date) => -date.getDay() + 6,\n      [pageUp]: (date: Date) =>\n        -new Date(date.getFullYear(), date.getMonth(), 0).getDate(),\n      [pageDown]: (date: Date) =>\n        new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate(),\n      offset: (date: Date, step: number) => date.setDate(date.getDate() + step),\n    },\n  }\n\n  const newDate = innerDate.value.toDate()\n  while (Math.abs(innerDate.value.diff(newDate, 'year', true)) < 1) {\n    const map = mapping[keyboardMode.value]\n    if (!map) return\n    map.offset(\n      newDate,\n      isFunction(map[code])\n        ? (map[code] as unknown as KeyControlMappingCallableOffset)(newDate)\n        : (map[code] as number) ?? 0\n    )\n    if (disabledDate && disabledDate(newDate)) {\n      break\n    }\n    const result = dayjs(newDate).locale(lang.value)\n    innerDate.value = result\n    contextEmit('pick', result, true)\n    break\n  }\n}\n\nconst handlePanelChange = (mode: 'month' | 'year') => {\n  contextEmit('panel-change', innerDate.value.toDate(), mode, currentView.value)\n}\n\nwatch(\n  () => selectionMode.value,\n  (val) => {\n    if (['month', 'year'].includes(val)) {\n      currentView.value = val\n      return\n    } else if (val === 'years') {\n      currentView.value = 'year'\n      return\n    } else if (val === 'months') {\n      currentView.value = 'month'\n      return\n    }\n    currentView.value = 'date'\n  },\n  { immediate: true }\n)\n\nwatch(\n  () => defaultValue.value,\n  (val) => {\n    if (val) {\n      innerDate.value = getDefaultValue()\n    }\n  },\n  { immediate: true }\n)\n\nwatch(\n  () => props.parsedValue,\n  (val) => {\n    if (val) {\n      if (isMultipleType.value) return\n      if (isArray(val)) return\n      innerDate.value = val\n    } else {\n      innerDate.value = getDefaultValue()\n    }\n  },\n  { immediate: true }\n)\n\ncontextEmit('set-picker-option', ['isValidValue', isValidValue])\ncontextEmit('set-picker-option', ['formatToString', formatToString])\ncontextEmit('set-picker-option', ['parseUserInput', parseUserInput])\ncontextEmit('set-picker-option', ['handleFocusPicker', _handleFocusPicker])\n</script>\n"], "names": ["useNamespace", "useAttrs", "useSlots", "useLocale", "inject", "PICKER_BASE_INJECTION_KEY", "ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY", "toRef", "ref", "dayjs", "computed", "isArray", "isFunction", "getValidDateOfMonth", "month", "nextTick", "getValidDateOfYear", "year", "extractTimeFormat", "DEFAULT_FORMATS_TIME", "extractDateFormat", "DEFAULT_FORMATS_DATE", "correctlyParseUserInput", "EVENT_CODE", "watch", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref", "_createElementVNode", "_renderSlot", "_Fragment", "_renderList", "_createCommentVNode"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+QA,IAAA,MAAM,eAAkB,GAAA,CAAC,CAAe,EAAA,EAAA,EAAS,GAAgB,KAAA,IAAA,CAAA;AAGjE,IAAM,MAAA,IAAA,GAAOA,mBAAa,cAAc,CAAA,CAAA;AACxC,IAAM,MAAA,IAAA,GAAOA,mBAAa,aAAa,CAAA,CAAA;AACvC,IAAA,MAAM,QAAQC,YAAS,EAAA,CAAA;AACvB,IAAA,MAAM,QAAQC,YAAS,EAAA,CAAA;AAEvB,IAAA,MAAM,EAAE,CAAA,EAAG,IAAK,EAAA,GAAIC,iBAAU,EAAA,CAAA;AAC9B,IAAM,MAAA,UAAA,GAAaC,WAAOC,mCAAyB,CAAA,CAAA;AACnD,IAAA,MAAM,eAAkB,GAAAD,UAAA,CAAAE,uDAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IACtB,MAAA,EAAA,SAAA,EAAA,YAAA,EAAA,aAAA,EAAA,WAAA,EAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AAAA,IACA,MAAA,YAAA,GAAAC,SAAA,CAAA,UAAA,CAAA,KAAA,EAAA,cAAA,CAAA,CAAA;AAAA,IACF,MAAA,cAAA,GAAAC,OAAA,EAAA,CAAA;AACA,IAAA,MAAM,SAAa,GAAAA,OAAA,CAAAC,yBAAA,EAAA,CAAA,MAA6B,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAChD,IAAA,MAAM,aAAe,GAAAD,OAAA,CAAA,KAAiB,CAAA,CAAA;AAEtC,IAAA,IAAA,mBAAuB;AAEvB,IAAA,MAAM,YAAY,GAAIE,aAAQ,MAAO;AAErC,MAAM,OAAAD,yBAAA,CAAA,WAAoB,CAAK,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAE/B,KAAA,CAAA,CAAA;AAEA,IAAM,MAAA,KAAA,GAAAC;AACJ,MAAA,OAAO,SAAM,CAAA,KAAA,CAAA,KAAa,EAAA,CAAA;AAAiB,KAC5C,CAAA,CAAA;AAED,IAAM,MAAA,IAAA,GAAAA,mBAAuB;AAC3B,MAAO,OAAA,SAAA,CAAU,MAAM,IAAM,EAAA,CAAA;AAAA,KAC9B,CAAA,CAAA;AAED,IAAM,MAAA,kBAAsBF,OAAA,CAAA,EAAA,CAAA,CAAA;AAC1B,IAAO,MAAA,aAAU,UAAM,CAAK,IAAA,CAAA,CAAA;AAAA,IAC9B,MAAC,aAAA,GAAAA,OAAA,CAAA,IAAA,CAAA,CAAA;AAED,IAAM,MAAA,oBAAsB,GAAC,CAAC,IAAA,KAAA;AAC9B,MAAM,OAAA,qBAAuC,CAAA,MAAA,GAAA,CAAA,GAAA,eAAA,CAAA,IAAA,EAAA,eAAA,CAAA,KAAA,EAAA,KAAA,CAAA,MAAA,IAAA,UAAA,CAAA,GAAA,IAAA,CAAA;AAC7C,KAAM,CAAA;AAEN,IAAM,MAAA,UAAA,GAAA,CAAA,SAAuB,KAAsB;AACjD,MAAO,IAAA,WAAA,IAAA,CAAA,WAAsB,CAAA,KAAA,IACzB,CAAA,aAAA,CAAA,KAAsB,IAAA,CAAA,UAAA,EAAA;AACtB,QACN,OAAA,YAAA,CAAA,KAAA,CAAA,IAAA,CAAA,SAAA,CAAA,IAAA,EAAA,CAAA,CAAA,KAAA,CAAA,SAAA,CAAA,KAAA,EAAA,CAAA,CAAA,IAAA,CAAA,SAAA,CAAA,IAAA,EAAA,CAAA,CAAA;AACA,OAAM;AACJ,MACE,IAAA,QAAA,CAAA,KAAA;AAKA,QAAA,OAAO,SAAa,CAAA,WACZ,CAAA,CAAA,CAAA,CAAA;AAEgB,MAC1B,OAAA,SAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AACA,KAAA,CAAA;AACA,IAAO,MAAA,IAAA,GAAA,CAAA,KAAU,SAAa,KAAA;AAAA,MAChC,IAAA,CAAA,KAAA,EAAA;AACA,QAAM,WAAQ,CAAA,MAAA,EAA2B,KAAgB,EAAA,GAAA,IAAA,CAAA,CAAA;AACvD,OAAA,MAAY,IAAAG,cAAA,CAAA,KAAA,CAAA,EAAA;AACV,QAAY,MAAA,KAAA,GAAA,KAAA,CAAQ,GAAO,CAAA,UAAO,CAAA,CAAA;AAAA,QACpC,WAAmB,CAAA,MAAA,EAAA,KAAQ,EAAA,GAAA,IAAA,CAAA,CAAA;AACzB,OAAM,MAAA;AACN,QAAY,WAAA,CAAA,MAAA,EAAQ,UAAU,CAAI,KAAA,CAAA,EAAA,GAAA,IAAA,CAAA,CAAA;AAAA,OAC7B;AACL,MAAA,aAAA,CAAY,KAAQ,GAAA,IAAA,CAAA;AAA0B,MAChD,aAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACA,MAAA,aAAA,CAAc,KAAQ,GAAA,KAAA,CAAA;AACtB,MAAA,UAAA,GAAA,KAAsB,CAAA;AACtB,KAAA,CAAA;AACA,IAAa,MAAA,cAAA,GAAA,OAAA,KAAA,EAAA,QAAA,KAAA;AAAA,MACf,IAAA,aAAA,CAAA,KAAA,KAAA,MAAA,EAAA;AACA,QAAM,KAAA,GAAA,KAAA,CAAA;AACJ,QAAI,IAAA,OAAA,GAAA,iBAAgC,GAAA,KAAA,CAAA,WAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA,EAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA,GAAA,KAAA,CAAA;AAClC,QAAQ,IAAA,CAAA,oBAAA,CAAA,OAAA,CAAA,EAAA;AACR,UAAA,yBAAoB,CAAA,KAAA,CACf,MAAM,IACJ,CAAA,KAAA,CAAA,IAAA,EAAK,OAAW,CAAA,MAChB,KAAM,EAAA,CAAA,CAAA,UAAa,CAAA,MACd,CAAM,CAAA;AAGlB,SAAI;AACF,QAAA,SAAA,CAAA;AAGoB,QACtB,IAAA,CAAA,OAAA,EAAA,QAAA,CAAA,KAAA,IAAA,QAAA,CAAA,CAAA;AACA,OAAA,MAAA,IAAA,aAAkB,CAAA,KAAA,KAAA,MAAA,EAAA;AAClB,QAAK,IAAA,CAAA,KAAA,CAAA,IAAkB,CAAA,CAAA;AAAiB,OAC1C,MAAA,IAAW,aAAc,CAAA,KAAA,KAAU,OAAQ,EAAA;AACzC,QAAA,IAAA,CAAM,OAA8B,IAAA,CAAA,CAAA;AAAA,OACtC;AACE,KAAA,CAAA;AAAoC,IACtC,MAAA,WAAA,GAAA,CAAA,OAAA,KAAA;AAAA,MACF,MAAA,MAAA,GAAA,OAAA,GAAA,KAAA,GAAA,UAAA,CAAA;AAEA,MAAM,SAAA,CAAA,KAAA,GAAc,SAAsB,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,OAAA,CAAA,CAAA;AACxC,MAAM,yBAAmB,CAAQ,CAAA;AACjC,KAAA,CAAA;AACA,IAAA,MAAA,UAAA,GAAA,CAAkB,OAAO,KAAA;AAAA,MAC3B,MAAA,WAAA,GAAA,SAAA,CAAA,KAAA,CAAA;AAEA,MAAM,MAAA,MAAA,GAAA,OAAmC,GAAA,KAAA,GAAA,UAAA,CAAA;AACvC,MAAA,6BAA8B,CAAA,KAAA,KAAA,MAAA,GAAA,WAAA,CAAA,MAAA,CAAA,CAAA,EAAA,EAAA,MAAA,CAAA,GAAA,WAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,MAAA,CAAA,CAAA;AAC9B,MAAM,yBAAmB,CAAQ;AAEjC,KAAA,CAAA;AAKA,IAAA,MAAA,WAAA,GAAkBH,OAAM,CAAA,MAAA,CAAA,CAAA;AAAA,IAC1B,MAAA,SAAA,GAAAE,YAAA,CAAA,MAAA;AAEA,MAAM,MAAA,kBAAwB,CAAA,CAAA,oBAAA,CAAA,CAAA;AAE9B,MAAM,IAAA,WAAA,CAAY,UAAe,MAAA,EAAA;AAC/B,QAAM,MAAA,SAAA,GAAA,IAAkB,MAAsB,CAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,GAAA,EAAA,CAAA;AAC9C,QAAI,IAAA;AACF,UAAA,mBAAuB,CAAA,CAAA,EAAA,eAAW,CAAA,GAAU,EAAI,SAAA,GAAA,CAAA,CAAA,CAAA,EAAA,eAAA,CAAA,CAAA,CAAA;AAChD,SAAA;AACE,QAAO,OAAA,CAAA,EAAA,aAAgB,EAAA,SAAA,GAAA,CAAA,CAAA,CAAe;AAEnB,OACrB;AACA,MAAA,OAAA,CAAA,EAAU,IAAA,CAAA,KAAA,CAAS,CAAM,EAAA,eAAY,CAAC,CAAA,CAAA;AAAA,KACxC,CAAA,CAAA;AACA,IAAA,MAAA,mBAAoB,GAAmB,CAAA,QAAA,KAAA;AAAA,MACxC,MAAA,aAAA,GAAAE,iBAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,QAAA,CAAA,KAAA,EAAA,GAAA,QAAA,CAAA,KAAA,CAAA;AAOD,MAAM,IAAA,aAAA,EAAA;AACJ,QAAM,UAAA,GAAA,IAAA,CAAA;AAGN,QAAA,IAAmB,CAAAH,yBAAA,CAAA,aAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACjB,QAAa,OAAA;AACb,OAAA;AACA,MAAA,IAAA,QAAA,CAAA,OAAA,EAAA;AAAA,QACF,QAAA,CAAA,OAAA,CAAA;AACA,UAAI;AACF,UAAA,KAAA;AAAiB,UACf,IAAA,EAAA,WAAA;AAAA,SACA,CAAA,CAAA;AAAA,OAAA;AACM,KAAA,CAAA;AACP,IACH,MAAA,aAAA,GAAAC,YAAA,CAAA,MAAA;AAAA,MACF,MAAA,EAAA,IAAA,EAAA,GAAA,KAAA,CAAA;AAEA,MAAM,IAAA,CAAA,MAAA,EAAA,OAAgB,UAA6B,EAAA,MAAA,EAAA,OAAA,EAAA,OAAA,CAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACjD,QAAM,WAAO,CAAI;AACjB,MAAI,aAAS,CAAS;AACpB,KAAO,CAAA,CAAA;AACT,IAAO,MAAA,cAAA,GAAAA,YAAA,CAAA,MAAA;AAAA,MACR,OAAA,aAAA,CAAA,KAAA,KAAA,OAAA,IAAA,aAAA,CAAA,KAAA,KAAA,QAAA,IAAA,aAAA,CAAA,KAAA,KAAA,OAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,2BAC0B,CAAA,MAAA;AAEA,MAE3B,OAAA,aAAA,CAAA,KAAA,KAAA,MAAA,GAAA,WAAA,CAAA,KAAA,GAAA,aAAA,CAAA,KAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,YAAqB,GAAAA,YAAA,CAAA,MACjB,CAAA,CAAA,SAAA,CAAA,MAAY;AACE,IACpB,MAAC,eAAA,GAAA,OAAA,MAAA,EAAA,QAAA,KAAA;AAED,MAAA,IAAM,mBAAwB,KAAA,OAAO;AAErC,QAAM,SAAA,CAAA,KAAA,GAAAG,2BAGD,CAAA,SAAA,CAAA,KAAA,EAAA,SAAA,CAAA,KAAA,CAAA,IAAA,EAAA,EAAA,MAAA,EAAA,IAAA,CAAA,KAAA,EAAA,YAAA,CAAA,CAAA;AACH,QAAI,IAAA,CAAA,SAAA,CAAA,YAAiC,CAAA,CAAA;AACnC,OAAA,MAAA,IAAA,aAAkB,CAAA,KAAA,KAAA,QAAA,EAAA;AAAA,QAAA,IACN,CAAA,MAAA,EAAA,QAAA,IAAA,IAAA,GAAA,QAAA,GAAA,IAAA,CAAA,CAAA;AAAA,OACV,MAAA;AAAqB,QACrBC,SAAAA,CAAAA,KAAAA,GAAAA,2BAAAA,CAAAA,SAAAA,CAAAA,KAAAA,EAAAA,SAAAA,CAAAA,KAAAA,CAAAA,IAAAA,EAAAA,EAAAA,MAAAA,EAAAA,IAAAA,CAAAA,KAAAA,EAAAA,YAAAA,CAAAA,CAAAA;AAAA,QAAA,WACK,CAAA,KAAA,GAAA,MAAA,CAAA;AAAA,QACL,IAAA,CAAA,OAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,CAAA,CAAA,QAAA,CAAA,aAAA,CAAA,KAAA,CAAA,EAAA;AAAA,UACF,IAAA,CAAA,SAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AACA,UAAK,MAAAC;AAAsB,UAC7B,iBAAyB,EAAA,CAAA;AACvB,SAAKD;AAA4C,OAC5C;AACL,MAAA,iBAAkB,CAAA,OAAA,CAAA,CAAA;AAAA,KAAA,CAAA;AACN,IACV,MAAA,iBAAqB,OAAA,KAAA,EAAA,QAAA,KAAA;AAAA,MACrBA,IAAAA,aAAAA,CAAAA,KAAAA,KAAAA,MAAAA,EAAAA;AAAA,QAAA,MACK,IAAA,GAAA,SAAA,CAAA,KAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QACL,SAAA,CAAA,KAAA,GAAAE,0BAAA,CAAA,IAAA,EAAA,IAAA,CAAA,KAAA,EAAA,YAAA,CAAA,CAAA;AAAA,QACF,IAAA,CAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,CAAA;AACA,OAAA,MAAA,IAAA,aAAoB,CAAA,KAAA,KAAA,OAAA,EAAA;AACpB,QAAI,IAAA,CAAC,eAAiB,IAAA,IAAA,WAAgB,GAAA,IAAA,CAAS,CAAc;AAC3D,OAAK,MAAA;AACL,QAAA,MAAA,IAAe,GAAA,SAAA,CAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACf,QAAkB,SAAA,CAAA,KAAA,GAAAA,0BAAA,CAAA,IAAA,EAAA,IAAA,CAAA,KAAA,EAAA,YAAA,CAAA,CAAA;AAAA,QACpB,WAAA,CAAA,KAAA,GAAA,OAAA,CAAA;AAAA,QACF,IAAA,CAAA,OAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,CAAA,CAAA,QAAA,CAAA,aAAA,CAAA,KAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,SAAkB,CAAO,KAAA,EAAA,IAAA,CAAA,CAAA;AAAA,UAC3B,MAAAD,YAAA,EAAA,CAAA;AAEA,UAAM,iBAAiB,EACrBE,CAAAA;AAGA,SAAI;AACF,OAAA;AACA,MAAA,iBAAkB,CAAA,MAAA,CAAA,CAAA;AAClB,KAAK,CAAA;AAAsB,IAC7B,MAAA,UAAyB,GAAA,OAAA,IAAA,KAAA;AACvB,MAAKA,IAAAA,KAAAA,CAAAA;AAA0C,QAC1C,OAAA;AACL,MAAA,WAAa,CAAA,KAAA,GAAA,IAAA,CAAU;AACvB,MAAA,MAAAF,YAAkB,EAAA,CAAA;AAClB,MAAA,iBAAoB,EAAA,CAAA;AACpB,KAAI,CAAA;AACF,IAAK,MAAA,QAAA,GAAAL,mBAAqB,KAAA,CAAA,IAAA,KAAA,UAAA,IAAA,KAAA,CAAA,IAAA,KAAA,eAAA,CAAA,CAAA;AAC1B,IAAA,MAAA,aAAe,GAAAA,YAAA,CAAA,MAAA;AACf,MAAkB,MAAA,cAAA,GAAA,QAAA,CAAA,KAAA,IAAA,aAAA,CAAA,KAAA,KAAA,OAAA,CAAA;AAAA,MACpB,MAAA,cAAA,GAAA,aAAA,CAAA,KAAA,KAAA,OAAA,CAAA;AAAA,MACF,MAAA,eAAA,GAAA,aAAA,CAAA,KAAA,KAAA,QAAA,CAAA;AACA,MAAA,MAAA,UAAA,GAAwB,WAAA,CAAA,KAAA,KAAA,MAAA,CAAA;AAAA,MAC1B,MAAA,UAAA,GAAA,WAAA,CAAA,KAAA,KAAA,MAAA,CAAA;AAEA,MAAM,MAAA,WAAa,cAAkC,CAAA,KAAA,KAAA,OAAA,CAAA;AACnD,MAAA,qBAAoB,IAAA,UAAA,IAAA,cAAA,IAAA,UAAA,IAAA,eAAA,IAAA,WAAA,CAAA;AACpB,KAAA,CAAA,CAAA;AACA,IAAA,MAAA,YAAe,GAAAA,YAAA,CAAA,MAAA,CAAA,cAAA,CAAA,KAAA,IAAA,KAAA,CAAA,OAAA,IAAA,KAAA,CAAA,WAAA,CAAA,CAAA;AACf,IAAkB,MAAA,eAAA,GAAAA,YAAA,CAAA,MAAA;AAAA,MACpB,IAAA,CAAA,YAAA;AAEA,QAAA,OAAiB,KAAA,CAAA;AAAA,MACf,IAAM,CAAA,KAAA,CAAA,WAAe;AAA6B,QACpD,OAAA,IAAA,CAAA;AAEA,MAAM,IAAAC,cAAA,CAAA,KAAA,CAAA,WAAyB,CAAM,EAAA;AACnC,QAAA,OAAuB,YAAA,CAAA,KAAA,CAAA,WAAkB,CAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA;AACzC,OAAM;AACN,MAAM,OAAA,YAAA,CAAA,kBAAgC,MAAU,EAAA,CAAA,CAAA;AAChD,KAAM,CAAA,CAAA;AACN,IAAM,MAAA,SAAA,GAAA;AACN,MAAM,IAAA,cAAA,CAAA;AACN,QAAA,IAAA,CACG,KAAkB,CAAA,WAAA,CAAA,CAAA;AAEC,OAEvB,MAAA;AAED,QAAA,IAAqB,MAAA,GAAA,KAAA,CAAA,WAAA,CAAA;AAAA,YACZ,CAAC,MAAA,EAAA;AAAgD,UAC1D,MAAA,aAAA,GAAAF,yBAAA,CAAA,WAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAEA,UAAM,MAAA,+BAAiC,EAAA,CAAA;AACrC,UAAI,sBAAsB,CAAA,IAAA,CAAA,aAAA,CAAA,IAAA,EAAA,CAAA,CAAA,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA,CAAA,CAAA,IAAA,CAAA,aAAA,CAAA,IAAA,EAAA,CAAA,CAAA;AAC1B,SAAI;AACJ,QAAI,SAAA,CAAQ,KAAM,GAAA,MAAA,CAAA;AAChB,QAAA,IAAA,CAAA;AAAiD,OACnD;AACA,KAAA,CAAA;AAA8C,IAChD,MAAC,WAAA,GAAAC,YAAA,CAAA,MAAA;AACD,MAAA,IAAM,aAAkB;AACtB,QAAA;AACE,MAAA,OAAK,YAA4B,CAAAD,yBAAA,EAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA;AAAA,KAAA,CACnC,CAAO;AAEL,IAAA,MAAA,WAAa,GAAM,MAAA;AACnB,MAAA,MAAI,GAAS,GAAAA,yBAAA,EAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACX,MAAA,MAAA,oBAAqB,EAAM,CAAA;AAC3B,MAAA,0BAAsB,CAAgB;AACtC,MAAA,IAAA,CAAA,CAAA,YACG,IAAA,CAAA,YAAmB,CAAA,OAAA,CAAA,KAAA,oBACC,CAAA,OAAA,CAAA,EAAM;AACD,QAC9B,SAAA,CAAA,KAAA,GAAAA,yBAAA,EAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAkB,CAAA,KAAA,CAAA,CAAA;AAClB,OAAA;AAAW,KACb,CAAA;AAAA,IACF,MAAA,UAAA,GAAAC,YAAA,CAAA,MAAA;AAEA,MAAM,OAAA,KAAA,CAAA,cAAuBQ,uBAAM,CAAA,KAAA,CAAA,MAAA,CAAA,IAAAC,8BAAA,CAAA;AACjC,KAAI,CAAA,CAAA;AACJ,IAAO,MAAA,UAAA,GAAAT,aAAmB,MAAE;AAA2B,MACxD,OAAA,KAAA,CAAA,UAAA,IAAAU,uBAAA,CAAA,KAAA,CAAA,MAAA,CAAA,IAAAC,8BAAA,CAAA;AACD,KAAA,CAAA,CAAA;AAGE,IAAA,MAAA,WAAY,GAAMX,YAAE,CAAO,MAAU;AACrC,MAAM,IAAA,mBAAqB;AAC3B,QAAA,OAAA,aAAsB,CAAA,KAAA,CAAA;AACtB,MACG,IAAA,CAAA,iBAAiB,IAAC,CAAA,kBACnB;AAEA,QAAA,OAAA;AACA,MAAA,OAAK,kBAAe,IAAA,SAAA,CAAA,KAAA,EAAA,MAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AAAA,KACtB,CAAA,CAAA;AAAA,IACF,MAAA,WAAA,GAAAA,YAAA,CAAA,MAAA;AAEA,MAAM,IAAA,aAAa;AACjB,QAAA,OACQ,aAAA,CAAA,KAAgC,CAAA;AAAiB,MAE1D,IAAA,CAAA,KAAA,CAAA,WAAA,IAAA,CAAA,YAAA,CAAA,KAAA;AAED,QAAM,OAAA;AACJ,MAAA,OACE,CAAM,KAAA,CAAA,WAAA,IAAgC,SAAA,CAAA,KAAA,EAAA,iBAAiB,CAAA,KAAA,CAAA,CAAA;AAAA,KAE1D,CAAA,CAAA;AAED,IAAM,MAAA,2BAAuB,CAAM,KAAA,CAAA,CAAA;AACjC,IAAI,MAAA,sBAAqB,GAAA,MAAqB;AAC9C,MAAA,iBAA0B,CAAA,KAAA,GAAA;AAC1B,KAAS,CAAA;AAAgD,IAAA,MAC5C,mBAAA,GAAA,MAAA;AAAA,MACb,iBAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAAA,KACD,CAAA;AAED,IAAM,MAAA,QAAA,GAAA,CAAA;AACJ,MAAI,OAAA;AACJ,QAAA,IAAK,EAAA,IAAqB,CAAA,IAAA,EAAA;AAC1B,QAAS,MAAA,EAAA,IAAM,CAAe,MAAA,EAAA;AAA2B,QACvD,MAAW,EAAA,IAAA,CAAA,MAAA,EAAA;AAAA,QACb,IAAA,EAAA,IAAA,CAAA,IAAA,EAAA;AAAA,QACD,KAAA,EAAA,IAAA,CAAA,KAAA,EAAA;AAED,QAAM,IAAA,EAAA,IAAA,CAAA,IAAA,EAAA;AACN,OAAA,CAAA;AACE,KAAA,CAAA;AAA0B,IAC5B,MAAA,cAAA,GAAA,CAAA,KAAA,EAAA,OAAA,EAAA,KAAA,KAAA;AACA,MAAA,4BAAkC,EAAA,GAAA,QAAA,CAAA,KAAA,CAAA,CAAA;AAChC,MAAA,MAAA,OAAA,GAAA,KAA0B,CAAA,WAAA,GAAA,KAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,KAAA,CAAA;AAAA,MAC5B,SAAA,CAAA,KAAA,GAAA,OAAA,CAAA;AAEA,MAAM,IAAA,CAAA,SAAA,CAAW,KAAiB,EAAA,IAAA,CAAA,CAAA;AAChC,MAAO,IAAA,CAAA,KAAA,EAAA;AAAA,QACL,iBAAgB,CAAA,KAAA,GAAA,OAAA,CAAA;AAAA,OAChB;AAAoB,KACpB,CAAA;AAAoB,IACpB,MAAA,uBAAgB,GAAA,CAAA,KAAA,KAAA;AAAA,MAChB,MAAA,UAAkBD,yBAAA,CAAA,KAAA,EAAA,UAAA,CAAA,KAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,MAClB,IAAA,eAAgB,EAAA,IAAA,oBAAA,CAAA,OAAA,CAAA,EAAA;AAAA,QAClB,MAAA,EAAA,IAAA,EAAA,KAAA,EAAA,KAAA,EAAA,MAAA,EAAA,IAAA,EAAA,GAAA,QAAA,CAAA,SAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QACF,SAAA,CAAA,KAAA,GAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;AAEA,QAAA,aAAuB,CAAA,KAAA,GAAe,IAAA,CAAA;AACpC,QAAA,iBAAc,CAAA,KAAe,GAAA,KAAI;AACjC,QAAA,IAAM,CAAU,SAAA,CAAA,KAAM,EACjB,IAAA,CAAA,CAAA;AAEL,OAAA;AACA,KAAK,CAAA;AACL,IAAA,MAAI,uBAAQ,GAAA,CAAA,KAAA,KAAA;AACV,MAAA,MAAA,OAAA,GAAAa,+BAA0B,CAAA,KAAA,EAAA,UAAA,CAAA,KAAA,EAAA,IAAA,CAAA,KAAA,EAAA,eAAA,CAAA,CAAA;AAAA,MAC5B,IAAA,OAAA,CAAA,OAAA,EAAA,EAAA;AAAA,QACF,IAAA,YAAA,IAAA,YAAA,CAAA,OAAA,CAAA,MAAA,EAAA,CAAA,EAAA;AAEA,UAAM,OAAA;AACJ,SAAM;AACN,QAAA,MAAY,EAAA,IAAA,EAAA,MAAa,EAAA,MAAA,EAAA,GAAA,QAAA,CAAA,SAA4B,CAAG,KAAA,CAAA,CAAA;AACtD,QAAM,eAAQ,GAAA,OAAA,CAAA,UAAO,CAAK,MAAA,CAAA,MAAa,CAAA,CAAA,MAAA,CAAA,MAAe,CAAA,CAAA;AACtD,QAAU,aAAA,CAAA,YAAgB,CAAKL;AAC/B,QAAA,IAAA,CAAA,SAAc,CAAQ,KAAA,EAAA,IAAA,CAAA,CAAA;AACtB,OAAA;AACA,KAAK,CAAA;AAAqB,IAC5B,MAAA,YAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MACF,OAAAR,yBAAA,CAAA,OAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,OAAA,EAAA,KAAA,YAAA,GAAA,CAAA,YAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAA,cAAgB,GAAA,CAAA,KAAA,KAAA;AAAA,MACd,OAAAE,cAAA,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,GAAA,KAAA,CAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AAAA,KAAA,CAAA;AACW,IAAA,MACN,cAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MACL,OAAAW,+BAAA,CAAA,KAAA,EAAA,KAAA,CAAA,MAAA,EAAA,IAAA,CAAA,KAAA,EAAA,eAAA,CAAA,CAAA;AAAA,KACF,CAAA;AACA,IAAI,MAAA,kBAAmB,MAAA;AACrB,MAAA,MAAI,SAAgB,GAAAb,yBAAA,CAAA,YAAqB,CAAA,KAAA,CAAA,CAAA,MAAO,CAAC,IAAG,CAAA,KAAA,CAAA,CAAA;AAClD,MAAA,IAAA,CAAA,YAAA,CAAA,KAAA,EAAA;AAAA,QACF,MAAA,iBAAA,GAAA,YAAA,CAAA,KAAA,CAAA;AACA,QAAA,gCAAc,EAAA,CAAA,IAAA,CAAA,iBAAmB,CAAA,IAAS,UAAU,CAAK,iBAAA,CAAA,MAAA,EAAA,CAAA,CAAA,MAAA,CAAA,iBAAA,CAAA,MAAA,EAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACzD,OAAU;AACV,MAAA,OAAA,SAAc,CAAQ;AACtB,KAAK,CAAA;AAAqB,IAC5B,MAAA,iBAAA,GAAA,MAAA;AAAA,MACF,IAAA,EAAA,CAAA;AAEA,MAAM,IAAA,CAAA,MAAA,EAAA,OAAgB,EAAkB,MAAA,EAAA,MAAA,CAAA,CAAA,QAAA,CAAA,aAAA,CAAA,KAAA,CAAA,EAAA;AACtC,QAAA,CAAA,EAAA,GACQ,cAAY,CAAA,KAAA,KACb,IAAA,GAAA,KACJ,CAAA,GAAA,EAAA,CAAA,KAAA,EAAA,CAAA;AAA8C,OAEnD;AAEA,KAAM,CAAA;AACJ,IAAA,MAAA,kBAAoB,GACG,MAAA;AACiB,MAC1C,iBAAA,EAAA,CAAA;AAEA,MAAM,IAAA,aAAA,CAAA,KAAmC,KAAA,MAAA,EAAA;AACvC,QAAO,gBAAA,CAAAc,eAAA,CAAA,IAAA,CAAA,CAAA;AAAA,OACL;AAAA,KAAA,CAAA;AACM,IAAA,MACD,kBAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MACL,MAAA,EAAA,IAAA,EAAA,GAAA,KAAA,CAAA;AAAA,MACF,MAAA,SAAA,GAAA;AAAA,QACFA,eAAA,CAAA,EAAA;AAEA,QAAAA;AACE,QAAAA;AACA,QAAIA,qBAAc;AAChB,QAAAA;AACA,QAAAA,mBACG;AAGiB,QACtBA,eAAA,CAAA,MAAA;AACA,QAAOA,eAAA,CAAA,QAAA;AAAA,OACT,CAAA;AAEA,MAAA,IAAM,uBAA0B,CAAA,EAAA;AAC9B,QAAI,gBAAkB,CAAA,IAAA,CAAA,CAAA;AACpB,QAAA,KAAA,CAAA,iBAA4B,CAAA;AAAA,QAC9B,KAAA,CAAA,cAAA,EAAA,CAAA;AAAA,OACF;AAEA,MAAA,IAAM,uCAA2B,CAAA,KAAA,EAAAA,eAAA,CAAA,WAAA,CAAA,CAAA,QAAA,CAAA,IAAA,CAAA,IAAA,aAAA,CAAA,KAAA,KAAA,IAAA,IAAA,aAAA,CAAA,KAAA,KAAA,IAAA,EAAA;AAC/B,QAAkB,KAAA,CAAA,cAAA,EAAA,CAAA;AAElB,QAAI,IAAA,CAAA,SAAA,CAAA,YAAgC,CAAA,CAAA;AAClC,OAAA;AAAgC,KAClC,CAAA;AAAA,IACF,MAAA,gBAAA,GAAA,CAAA,IAAA,KAAA;AAEA,MAAM,IAAA,EAAA,CAAA;AACJ,MAAM,MAAA,EAAE,QAAS,EAAA,IAAA,EAAA,KAAA,EAAA,IAAA,EAAA,GAAA,EAAA,MAAA,EAAA,QAAA,EAAA,GAAAA,eAAA,CAAA;AACjB,MAAA,MAAM,OAAY,GAAA;AAAA,QAChB,IAAW,EAAA;AAAA,UACA,CAAA,EAAA,GAAA,CAAA,CAAA;AAAA,UACA,CAAA,IAAA,GAAA,CAAA;AAAA,UACA,CAAA,IAAA,GAAA,CAAA,CAAA;AAAA,UACA,CAAA,KAAA,GAAA,CAAA;AAAA,UACA,MAAA,EAAA,CAAA,IAAA,EAAA,IAAA,KAAA,IAAA,CAAA,WAAA,CAAA,IAAA,CAAA,WAAA,EAAA,GAAA,IAAA,CAAA;AAAA,SACA;AAAA,QACX,KAAW,EAAA;AAAA,UACb,CAAA,EAAA,GAAA,CAAA,CAAA;AACA,UAAI,CAAA,IAAA,GAAA,CAAA;AACF,UAAA,CAAA,IAAA,GAAA,CAAA,CAAA;AACA,UAAA,CAAA,KAAsB,GAAA,CAAA;AACtB,UAAA,MAAqB,EAAA,CAAA,IAAA,EAAA,IAAA,KAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,QAAA,EAAA,GAAA,IAAA,CAAA;AAAA,SACvB;AACA,QAAA,IACc,EAAA;AAAiD,UAC3D,CAAA,EAAA,GAAA,CAAA,CAAA;AAAA,WAEF,IAAc,GAAA,CAAA;AAGd,UAAA,CAAA,IAAqB,GAAA,CAAA,CAAA;AACrB,UAAK,CAAA,KAAA,GAAA,CAAA;AAAsB,UAC7B,MAAA,EAAA,CAAA,IAAA,EAAA,IAAA,KAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,OAAA,EAAA,GAAA,IAAA,GAAA,CAAA,CAAA;AAAA,SACF;AAEA,QAAM,IAAA,EAAA;AAaJ,UAAM,CAAA,EAAA,KAAM;AACZ,UAAA,CAAA,IAAmC,GAAA,CAAA;AAAA,UAC3B,CAAA,IAAA,GAAA,CAAA,CAAA;AAAA,UACJ,CAAC,KAAK,GAAA,CAAA;AAAA,UACN,CAAC,IAAI,GAAG,CAAA,IAAA,KAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AAAA,UACR,CAAC,MAAO,CAAA,IAAA,KAAA,CAAA,IAAA,CAAA,MAAA,EAAA,GAAA,CAAA;AAAA,UACR,CAAC,MAAQ,GAAA,CAAA,IAAA,KAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,WAAA,EAAA,EAAA,IAAA,CAAA,QAAA,EAAA,EAAA,CAAA,CAAA,CAAA,OAAA,EAAA;AAAA,UACT,CAAA,QAAS,GAAY,CAAA,IAAA,KAAA,QACF,CAAA,IAAA,CAAA,WAAiB,EAAA,EAAA,IAAA,CAAA,QAAQ,EAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAA,OAAA,EAAA;AAAA,UAC9C,MAAA,EAAA,CAAA,IAAA,EAAA,IAAA,KAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,OAAA,EAAA,GAAA,IAAA,CAAA;AAAA,SACO;AAAA,OACL,CAAA;AAAM,MACN,aAAQ,GAAA,SAAA,CAAA,KAAA,CAAA,MAAA,EAAA,CAAA;AAAA,MACR,WAAQ,CAAA,GAAA,CAAA,SAAA,CAAA,KAAA,CAAA,IAAA,CAAA,OAAA,EAAA,MAAA,EAAA,IAAA,CAAA,CAAA,GAAA,CAAA,EAAA;AAAA,QACR,SAAS,GAAA,OAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QACT,IAAA,CAAA,GAAA;AACsC,UACxC,OAAA;AAAA,QACA,GAAM,CAAA,MAAA,CAAA,OAAA,EAAAX,iBAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,OAAA,CAAA,GAAA,CAAA,EAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,IAAA,GAAA,EAAA,GAAA,CAAA,CAAA,CAAA;AAAA,QACJ,gBAAM,IAAA,YAAA,CAAA,OAAA,CAAA,EAAA;AAAA,UACN,MAAQ;AAAA,SACR;AAAQ,QACR,YAAS,GAAAH,yBAAA,CAAA,OAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QACT,SAAA,CAAQ,KAAC,GACP,MAAA,CAAA;AAAsC,QAC1C,WAAA,CAAA,MAAA,EAAA,MAAA,EAAA,IAAA,CAAA,CAAA;AAAA,QACA,MAAM;AAAA,OACJ;AAAM,KACN,CAAA;AAAQ,IACR,MAAA,iBAAQ,GAAA,CAAA,IAAA,KAAA;AAAA,MACR,WAAS,CAAA,cAAA,EAAA,SAAA,CAAA,KAAA,CAAA,MAAA,EAAA,EAAA,IAAA,EAAA,WAAA,CAAA,KAAA,CAAA,CAAA;AAAA,KAAA,CAAA;AAC4B,IACrCe,SAAA,CAAA,MAAO,aAAiB,CAAA,WAAgB,KAAA;AAAA,MAAA,IACxC,CAAC,OAAM,EAAG,MAAC,CAAA,CAAA,QACJ,CAAA,GAAK,CAAK,EAAA;AAA2C,QAAA,WACnD,CAAA,KAAI,GAAA,GAAA,CACX;AAA6D,QAC/D,OAAA;AAAwE,OAC1E,MAAA,IAAA,GAAA,KAAA,OAAA,EAAA;AAAA,QACF,WAAA,CAAA,KAAA,GAAA,MAAA,CAAA;AAEA,QAAM,OAAA;AACN,OAAO,MAAA,IAAA,GAAS,KAAA,QAAgB,EAAA;AAC9B,QAAM,WAAA,CAAM,KAAQ,GAAA,OAAA,CAAA;AACpB,QAAA,OAAU;AACV,OAAI;AAAA,MACF,WAAA,CAAA,KAAA,GAAA,MAAA,CAAA;AAAA,KAAA,EAAA,EAAA,SACW,EAAA,IAAA,EAAQ,CAAA,CAAA;AAEU,IAC/BA,SAAA,CAAA,MAAA,YAAA,CAAA,KAAA,EAAA,CAAA,GAAA,KAAA;AACA,MAAI,IAAA,GAAA,EAAA;AACF,QAAA,SAAA,CAAA,KAAA,GAAA,eAAA,EAAA,CAAA;AAAA,OACF;AACA,KAAA,EAAA,EAAA,eAAqB,EAAA,CAAA,CAAA;AACrB,IAAAA,SAAA,CAAA,MAAA,KAAkB,CAAA,WAAA,EAAA,CAAA,GAAA,KAAA;AAClB,MAAY,IAAA,GAAA,EAAA;AACZ,QAAA,IAAA,cAAA,CAAA,KAAA;AAAA,UACF,OAAA;AAAA,QACF,IAAAb,cAAA,CAAA,GAAA,CAAA;AAEA,UAAM,OAAA;AACJ,QAAA,SAAA,CAAY;AAAiE,OAC/E,MAAA;AAEA,QAAA,SAAA,CAAA,KAAA,GAAA,eAAA,EAAA,CAAA;AAAA;AACsB,KAAA,EACnB,EAAQ,SAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AACP,IAAA,WAAc,CAAA,mBAAQ,EAAA,CAAA,cAAe,EAAA,YAAA,CAAA,CAAA,CAAA;AACnC,IAAA,WAAA,CAAA,mBAAoB,EAAA,CAAA,gBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AACpB,IAAA,WAAA,CAAA,mBAAA,EAAA,CAAA,gBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AAAA,IACF,WAAA,CAAA,mBAA4B,EAAA,CAAA,mBAAA,EAAA,kBAAA,CAAA,CAAA,CAAA;AAC1B,IAAA,OAAA,CAAA,IAAA,EAAA,MAAoB,KAAA;AACpB,MAAA,OAAAc,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,QACF,KAAA,EAAAC,kBAA6B,CAAA;AAC3B,UAAAC,SAAA,CAAA,IAAA,CAAY,CAAQ,CAAA,EAAA;AACpB,UAAAA,SAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA;AAAA,UACFA,SAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,QAAA,EAAA,IAAA,CAAA,MAAA,CAAA;AACA,UAAAA,SAAA,CAAA,IAAoB,CAAA,CAAA,EAAA,CAAA,UAAA,EAAA,IAAA,CAAA,QAAA,CAAA;AAAA,UACtB;AAAA,yBACkB,EAAA,IAAA,CAAA,MAAA,CAAA,OAAA,IAAAA,SAAA,CAAA,YAAA,CAAA;AAAA,YACpB,UAAA,EAAAA,SAAA,CAAA,QAAA,CAAA;AAEA,WAAA;AAAA;AACqB,OAClB,EAAQ;AACP,QAAAC,sBAAS,CAAA,KAAA,EAAA;AACP,UAAA,KAAA,EAAAF,kBAAkC,CAAAC,SAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAA,CAAA;AAAA,SACpC,EAAA;AAAA,UACFE,cAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,EAAA;AAAA,mBACaH,kBAAK,CAAAC,SAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA;AAAA,WACpB,CAAA;AAEA,UAAAA,SAAA,CAAA,YAAA,CAAA,IAAAH,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,YACQ,GAAM,EAAA,CAAA;AAAA,YACH,KAAA,EAAAC,kBAAA,CAAAC,SAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA;AACP,WAAA,EAAS;AACP,aAAAH,mBAAmB,EAAOC,sBAAA,CAAAK,YAAA,EAAA,IAAA,EAAAC,cAAA,CAAAJ,SAAA,CAAA,SAAA,CAAA,EAAA,CAAA,QAAA,EAAA,GAAA,KAAA;AAC1B,cAAI,OAAAH,aAAc,EAAA,EAAAC,sBAAA,CAAA,QAAA,EAAA;AAClB,gBAAA,GAAA;AAAkB,gBACb,IAAA,EAAA,QAAA;AACL,gBAAA,cAAkC,CAAA,QAAA;AAAA,gBACpC,KAAA,EAAAC,kBAAA,CAAAC,SAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA;AAAA,gBACF,OAAA,EAAA,CAAA,MAAA,KAAA,mBAAA,CAAA,QAAA,CAAA;AAAA,oCACkB,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA,EAAA,EAAA,CAAA,UAAA,EAAA,SAAA,CAAA,CAAA,CAAA;AAAA,aACpB,CAAA,EAAA,GAAA,CAAA;AAEA,WAAA,EAAA,CAAA,CAAA,IAAiCK,sBAAC,CAAgB,MAAA,EAAA,IAAA,CAAA;AAClD,UAAAJ,sBAAiC,CAAA,KAAA,EAAA;AACjC,YAAA,KAAiC,EAAAF,kBAAA,CAAAC,SAAmB,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA;AACpD,WAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}