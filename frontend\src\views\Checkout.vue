<template>
  <div class="checkout-page">
    <div class="container">
      <div class="page-header">
        <h1>结账</h1>
        <p>请确认您的订单信息</p>
      </div>
      
      <div class="checkout-content">
        <div class="checkout-form">
          <el-form
            ref="checkoutFormRef"
            :model="checkoutForm"
            :rules="checkoutRules"
            @submit.prevent="handleSubmit"
          >
            <div class="form-section">
              <h3>配送信息</h3>
              <el-form-item prop="shipping_address" label="配送地址">
                <el-input
                  v-model="checkoutForm.shipping_address"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入详细的配送地址"
                />
              </el-form-item>
            </div>
            
            <div class="form-section">
              <h3>支付方式</h3>
              <el-form-item prop="payment_method">
                <el-radio-group v-model="checkoutForm.payment_method">
                  <el-radio label="alipay">
                    <div class="payment-option">
                      <span>支付宝</span>
                      <small>安全快捷的在线支付</small>
                    </div>
                  </el-radio>
                  <el-radio label="wechat">
                    <div class="payment-option">
                      <span>微信支付</span>
                      <small>便捷的移动支付</small>
                    </div>
                  </el-radio>
                  <el-radio label="credit_card">
                    <div class="payment-option">
                      <span>信用卡</span>
                      <small>支持主流信用卡</small>
                    </div>
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
          </el-form>
        </div>
        
        <div class="order-summary">
          <div class="summary-card">
            <h3>订单摘要</h3>
            
            <div class="order-items">
              <div
                v-for="item in cartItems"
                :key="item.id"
                class="order-item"
              >
                <div class="item-image">
                  <img v-if="item.product.image_url" :src="item.product.image_url" :alt="item.product.name" />
                  <div v-else class="placeholder-image">🍫</div>
                </div>
                <div class="item-info">
                  <h4>{{ item.product.name }}</h4>
                  <p>数量: {{ item.quantity }}</p>
                </div>
                <div class="item-price">
                  ¥{{ (item.product.price * item.quantity).toFixed(2) }}
                </div>
              </div>
            </div>
            
            <div class="summary-totals">
              <div class="summary-row">
                <span>商品总计</span>
                <span>¥{{ cartTotal.toFixed(2) }}</span>
              </div>
              <div class="summary-row">
                <span>配送费</span>
                <span>免费</span>
              </div>
              <div class="summary-row total-row">
                <span>总计</span>
                <span>¥{{ cartTotal.toFixed(2) }}</span>
              </div>
            </div>
            
            <div class="checkout-actions">
              <el-button
                type="primary"
                size="large"
                :loading="loading"
                @click="handleSubmit"
                :disabled="isCartEmpty"
              >
                确认下单
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'

export default {
  name: 'Checkout',
  setup() {
    const store = useStore()
    const router = useRouter()
    
    const checkoutFormRef = ref()
    const loading = ref(false)
    
    const checkoutForm = reactive({
      shipping_address: '',
      payment_method: 'alipay'
    })
    
    const checkoutRules = {
      shipping_address: [
        { required: true, message: '请输入配送地址', trigger: 'blur' },
        { min: 10, message: '请输入详细的配送地址', trigger: 'blur' }
      ],
      payment_method: [
        { required: true, message: '请选择支付方式', trigger: 'change' }
      ]
    }
    
    const cartItems = computed(() => store.getters['cart/cartItems'])
    const cartTotal = computed(() => store.getters['cart/cartTotal'])
    const isCartEmpty = computed(() => store.getters['cart/isCartEmpty'])
    
    const handleSubmit = async () => {
      if (!checkoutFormRef.value) return
      
      if (isCartEmpty.value) {
        store.dispatch('notifications/showNotification', {
          type: 'warning',
          message: '购物车为空'
        })
        router.push('/cart')
        return
      }
      
      try {
        await checkoutFormRef.value.validate()
        
        loading.value = true
        
        const orderData = {
          shipping_address: checkoutForm.shipping_address,
          payment_method: checkoutForm.payment_method
        }
        
        const result = await store.dispatch('orders/createOrder', orderData)
        
        if (result.success) {
          store.dispatch('notifications/showNotification', {
            type: 'success',
            message: '订单创建成功！'
          })
          
          // 跳转到订单详情页
          router.push(`/orders/${result.data.id}`)
        }
        
      } catch (error) {
        console.error('Checkout error:', error)
      } finally {
        loading.value = false
      }
    }
    
    return {
      checkoutFormRef,
      checkoutForm,
      checkoutRules,
      loading,
      cartItems,
      cartTotal,
      isCartEmpty,
      handleSubmit
    }
  }
}
</script>

<style lang="scss" scoped>
.checkout-page {
  padding: 40px 0;
  
  .page-header {
    text-align: center;
    margin-bottom: 40px;
    
    h1 {
      font-size: 36px;
      font-weight: 800;
      color: var(--color-ink);
      margin-bottom: 8px;
    }
    
    p {
      font-size: 16px;
      color: #666;
    }
  }
  
  .checkout-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto;
    
    .checkout-form {
      .form-section {
        margin-bottom: 32px;
        
        h3 {
          font-size: 20px;
          font-weight: 700;
          color: var(--color-ink);
          margin-bottom: 16px;
        }
        
        .payment-option {
          display: flex;
          flex-direction: column;
          
          span {
            font-weight: 600;
            color: var(--color-ink);
          }
          
          small {
            color: #666;
            font-size: 12px;
          }
        }
      }
    }
    
    .order-summary {
      .summary-card {
        @include card-style;
        position: sticky;
        top: 100px;
        
        h3 {
          font-size: 20px;
          font-weight: 700;
          color: var(--color-ink);
          margin-bottom: 20px;
        }
        
        .order-items {
          margin-bottom: 20px;
          
          .order-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
            
            &:last-child {
              border-bottom: none;
            }
            
            .item-image {
              width: 50px;
              height: 50px;
              border-radius: var(--radius-sm);
              overflow: hidden;
              flex-shrink: 0;
              
              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
              
              .placeholder-image {
                width: 100%;
                height: 100%;
                @include chocolate-gradient;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 20px;
              }
            }
            
            .item-info {
              flex: 1;
              
              h4 {
                font-size: 14px;
                font-weight: 600;
                color: var(--color-ink);
                margin: 0 0 4px 0;
              }
              
              p {
                font-size: 12px;
                color: #666;
                margin: 0;
              }
            }
            
            .item-price {
              font-size: 14px;
              font-weight: 600;
              color: var(--color-primary);
            }
          }
        }
        
        .summary-totals {
          .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            
            &.total-row {
              font-size: 18px;
              font-weight: 700;
              color: var(--color-ink);
              margin-top: 12px;
              padding-top: 16px;
              border-top: 2px solid var(--color-primary);
            }
          }
        }
        
        .checkout-actions {
          margin-top: 24px;
          
          .el-button {
            width: 100%;
          }
        }
      }
    }
  }
}

:deep(.el-radio) {
  display: block;
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

@include respond-to('mobile') {
  .checkout-page {
    .checkout-content {
      grid-template-columns: 1fr;
      gap: 20px;
    }
  }
}
</style>
