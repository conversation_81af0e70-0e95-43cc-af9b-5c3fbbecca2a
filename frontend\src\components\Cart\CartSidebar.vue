<template>
  <el-drawer
    v-model="isVisible"
    title="购物车"
    direction="rtl"
    size="400px"
    class="cart-sidebar"
  >
    <div v-if="isCartEmpty" class="empty-cart">
      <el-icon size="64" color="#ccc">
        <ShoppingCart />
      </el-icon>
      <p>购物车为空</p>
      <el-button type="primary" @click="goToProducts">去购物</el-button>
    </div>
    
    <div v-else class="cart-content">
      <div class="cart-items">
        <div
          v-for="item in cartItems"
          :key="item.id"
          class="cart-item"
        >
          <div class="item-image">
            <img v-if="item.product.image_url" :src="item.product.image_url" :alt="item.product.name" />
            <div v-else class="placeholder-image">🍫</div>
          </div>
          
          <div class="item-info">
            <h4 class="item-name">{{ item.product.name }}</h4>
            <p class="item-price">¥{{ item.product.price.toFixed(2) }}</p>
            
            <div class="item-controls">
              <el-input-number
                v-model="item.quantity"
                :min="1"
                :max="item.product.stock_quantity"
                size="small"
                @change="updateQuantity(item.id, item.quantity)"
              />
              <el-button
                type="danger"
                size="small"
                text
                @click="removeItem(item.id)"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>
      
      <div class="cart-footer">
        <div class="cart-total">
          <span class="total-label">总计：</span>
          <span class="total-amount">¥{{ cartTotal.toFixed(2) }}</span>
        </div>
        
        <div class="cart-actions">
          <el-button @click="clearCart" size="large">清空购物车</el-button>
          <el-button type="primary" size="large" @click="goToCheckout">
            去结账
          </el-button>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { computed } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { ShoppingCart } from '@element-plus/icons-vue'

export default {
  name: 'CartSidebar',
  components: {
    ShoppingCart
  },
  setup() {
    const store = useStore()
    const router = useRouter()
    
    const isVisible = computed({
      get: () => store.getters['cart/isCartVisible'],
      set: (value) => {
        if (!value) {
          store.dispatch('cart/hideCart')
        }
      }
    })
    
    const cartItems = computed(() => store.getters['cart/cartItems'])
    const cartTotal = computed(() => store.getters['cart/cartTotal'])
    const isCartEmpty = computed(() => store.getters['cart/isCartEmpty'])
    
    const updateQuantity = async (id, quantity) => {
      try {
        await store.dispatch('cart/updateCartItem', { id, quantity })
      } catch (error) {
        console.error('Update quantity error:', error)
      }
    }
    
    const removeItem = async (id) => {
      try {
        await store.dispatch('cart/removeFromCart', id)
      } catch (error) {
        console.error('Remove item error:', error)
      }
    }
    
    const clearCart = async () => {
      try {
        await store.dispatch('cart/clearCart')
      } catch (error) {
        console.error('Clear cart error:', error)
      }
    }
    
    const goToProducts = () => {
      store.dispatch('cart/hideCart')
      router.push('/products')
    }
    
    const goToCheckout = () => {
      store.dispatch('cart/hideCart')
      router.push('/checkout')
    }
    
    return {
      isVisible,
      cartItems,
      cartTotal,
      isCartEmpty,
      updateQuantity,
      removeItem,
      clearCart,
      goToProducts,
      goToCheckout
    }
  }
}
</script>

<style lang="scss" scoped>
.cart-sidebar {
  :deep(.el-drawer__body) {
    padding: 0;
    display: flex;
    flex-direction: column;
  }
}

.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 40px 20px;
  
  p {
    margin: 20px 0;
    color: #666;
    font-size: 16px;
  }
}

.cart-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.cart-items {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.cart-item {
  display: flex;
  gap: 12px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  .item-image {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-sm);
    overflow: hidden;
    flex-shrink: 0;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .placeholder-image {
      width: 100%;
      height: 100%;
      @include chocolate-gradient;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
    }
  }
  
  .item-info {
    flex: 1;
    
    .item-name {
      font-size: 14px;
      font-weight: 600;
      margin: 0 0 4px 0;
      color: var(--color-ink);
    }
    
    .item-price {
      font-size: 16px;
      font-weight: 700;
      color: var(--color-primary);
      margin: 0 0 8px 0;
    }
    
    .item-controls {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .el-input-number {
        width: 80px;
      }
    }
  }
}

.cart-footer {
  padding: 20px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
  
  .cart-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    font-size: 18px;
    
    .total-label {
      font-weight: 600;
      color: var(--color-ink);
    }
    
    .total-amount {
      font-weight: 700;
      color: var(--color-primary);
      font-size: 20px;
    }
  }
  
  .cart-actions {
    display: flex;
    gap: 12px;
    
    .el-button {
      flex: 1;
    }
  }
}
</style>
