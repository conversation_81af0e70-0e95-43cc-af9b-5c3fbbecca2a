package handlers

import (
	"chocolate-shop/internal/models"
	"chocolate-shop/internal/redis"
	"encoding/json"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/jmoiron/sqlx"
)

type ProductHandler struct {
	db  *sqlx.DB
	rdb *redis.Client
}

func NewProductHandler(db *sqlx.DB, rdb *redis.Client) *ProductHandler {
	return &ProductHandler{
		db:  db,
		rdb: rdb,
	}
}

// GetProducts 获取产品列表
func (h *ProductHandler) GetProducts(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.<PERSON>fault<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.Default<PERSON>uery("page_size", "12"))
	
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 12
	}

	offset := (page - 1) * pageSize

	// 尝试从缓存获取热门产品
	if page == 1 {
		cachedProducts, err := redis.GetHotProducts(h.rdb)
		if err == nil {
			var products []models.Product
			if json.Unmarshal([]byte(cachedProducts), &products) == nil {
				// 获取总数
				var total int
				h.db.Get(&total, "SELECT COUNT(*) FROM products WHERE is_available = true")
				
				totalPages := (total + pageSize - 1) / pageSize
				
				c.JSON(http.StatusOK, models.APIResponse{
					Success: true,
					Message: "Products retrieved successfully (cached)",
					Data: models.PaginationResponse{
						Data:       products,
						Page:       page,
						PageSize:   pageSize,
						Total:      total,
						TotalPages: totalPages,
					},
				})
				return
			}
		}
	}

	// 从数据库获取产品
	var products []models.Product
	err := h.db.Select(&products, `
		SELECT * FROM products 
		WHERE is_available = true 
		ORDER BY created_at DESC 
		LIMIT $1 OFFSET $2
	`, pageSize, offset)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to retrieve products",
		})
		return
	}

	// 获取总数
	var total int
	err = h.db.Get(&total, "SELECT COUNT(*) FROM products WHERE is_available = true")
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to get total count",
		})
		return
	}

	totalPages := (total + pageSize - 1) / pageSize

	// 如果是第一页，缓存热门产品
	if page == 1 {
		productsJSON, _ := json.Marshal(products)
		redis.CacheHotProducts(h.rdb, string(productsJSON))
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Products retrieved successfully",
		Data: models.PaginationResponse{
			Data:       products,
			Page:       page,
			PageSize:   pageSize,
			Total:      total,
			TotalPages: totalPages,
		},
	})
}

// GetProduct 获取单个产品
func (h *ProductHandler) GetProduct(c *gin.Context) {
	productID := c.Param("id")

	// 尝试从缓存获取
	cachedProduct, err := redis.GetCachedProduct(h.rdb, productID)
	if err == nil {
		var product models.Product
		if json.Unmarshal([]byte(cachedProduct), &product) == nil {
			c.JSON(http.StatusOK, models.APIResponse{
				Success: true,
				Message: "Product retrieved successfully (cached)",
				Data:    product,
			})
			return
		}
	}

	// 从数据库获取
	var product models.Product
	err = h.db.Get(&product, "SELECT * FROM products WHERE id = $1 AND is_available = true", productID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Message: "Product not found",
		})
		return
	}

	// 缓存产品信息
	productJSON, _ := json.Marshal(product)
	redis.CacheProduct(h.rdb, productID, string(productJSON))

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Product retrieved successfully",
		Data:    product,
	})
}

// GetProductsByCategory 按分类获取产品
func (h *ProductHandler) GetProductsByCategory(c *gin.Context) {
	category := c.Param("category")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "12"))
	
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 12
	}

	offset := (page - 1) * pageSize

	var products []models.Product
	err := h.db.Select(&products, `
		SELECT * FROM products 
		WHERE category = $1 AND is_available = true 
		ORDER BY created_at DESC 
		LIMIT $2 OFFSET $3
	`, category, pageSize, offset)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to retrieve products",
		})
		return
	}

	// 获取总数
	var total int
	err = h.db.Get(&total, "SELECT COUNT(*) FROM products WHERE category = $1 AND is_available = true", category)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to get total count",
		})
		return
	}

	totalPages := (total + pageSize - 1) / pageSize

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Products retrieved successfully",
		Data: models.PaginationResponse{
			Data:       products,
			Page:       page,
			PageSize:   pageSize,
			Total:      total,
			TotalPages: totalPages,
		},
	})
}

// SearchProducts 搜索产品
func (h *ProductHandler) SearchProducts(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Search query is required",
		})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "12"))
	
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 12
	}

	offset := (page - 1) * pageSize

	var products []models.Product
	searchQuery := "%" + query + "%"
	err := h.db.Select(&products, `
		SELECT * FROM products 
		WHERE (name ILIKE $1 OR description ILIKE $1 OR category ILIKE $1) 
		AND is_available = true 
		ORDER BY created_at DESC 
		LIMIT $2 OFFSET $3
	`, searchQuery, pageSize, offset)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to search products",
		})
		return
	}

	// 获取总数
	var total int
	err = h.db.Get(&total, `
		SELECT COUNT(*) FROM products 
		WHERE (name ILIKE $1 OR description ILIKE $1 OR category ILIKE $1) 
		AND is_available = true
	`, searchQuery)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to get total count",
		})
		return
	}

	totalPages := (total + pageSize - 1) / pageSize

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Products searched successfully",
		Data: models.PaginationResponse{
			Data:       products,
			Page:       page,
			PageSize:   pageSize,
			Total:      total,
			TotalPages: totalPages,
		},
	})
}
