package models

import (
	"time"
)

// User 用户模型
type User struct {
	ID           int       `json:"id" db:"id"`
	Username     string    `json:"username" db:"username"`
	Email        string    `json:"email" db:"email"`
	PasswordHash string    `json:"-" db:"password_hash"`
	FullName     string    `json:"full_name" db:"full_name"`
	Phone        string    `json:"phone" db:"phone"`
	Address      string    `json:"address" db:"address"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
}

// Product 产品模型
type Product struct {
	ID            int       `json:"id" db:"id"`
	Name          string    `json:"name" db:"name"`
	Description   string    `json:"description" db:"description"`
	Price         float64   `json:"price" db:"price"`
	Category      string    `json:"category" db:"category"`
	ImageURL      string    `json:"image_url" db:"image_url"`
	StockQuantity int       `json:"stock_quantity" db:"stock_quantity"`
	IsAvailable   bool      `json:"is_available" db:"is_available"`
	CreatedAt     time.Time `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time `json:"updated_at" db:"updated_at"`
}

// Order 订单模型
type Order struct {
	ID              int         `json:"id" db:"id"`
	UserID          int         `json:"user_id" db:"user_id"`
	TotalAmount     float64     `json:"total_amount" db:"total_amount"`
	Status          string      `json:"status" db:"status"`
	ShippingAddress string      `json:"shipping_address" db:"shipping_address"`
	PaymentMethod   string      `json:"payment_method" db:"payment_method"`
	PaymentStatus   string      `json:"payment_status" db:"payment_status"`
	CreatedAt       time.Time   `json:"created_at" db:"created_at"`
	UpdatedAt       time.Time   `json:"updated_at" db:"updated_at"`
	Items           []OrderItem `json:"items,omitempty"`
}

// OrderItem 订单项模型
type OrderItem struct {
	ID        int       `json:"id" db:"id"`
	OrderID   int       `json:"order_id" db:"order_id"`
	ProductID int       `json:"product_id" db:"product_id"`
	Quantity  int       `json:"quantity" db:"quantity"`
	Price     float64   `json:"price" db:"price"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	Product   *Product  `json:"product,omitempty"`
}

// CartItem 购物车项模型
type CartItem struct {
	ID        int       `json:"id" db:"id"`
	UserID    int       `json:"user_id" db:"user_id"`
	ProductID int       `json:"product_id" db:"product_id"`
	Quantity  int       `json:"quantity" db:"quantity"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
	Product   *Product  `json:"product,omitempty"`
}

// 请求和响应模型

// RegisterRequest 注册请求
type RegisterRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
	FullName string `json:"full_name"`
	Phone    string `json:"phone"`
	Address  string `json:"address"`
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Token string `json:"token"`
	User  User   `json:"user"`
}

// AddToCartRequest 添加到购物车请求
type AddToCartRequest struct {
	ProductID int `json:"product_id" binding:"required"`
	Quantity  int `json:"quantity" binding:"required,min=1"`
}

// CreateOrderRequest 创建订单请求
type CreateOrderRequest struct {
	ShippingAddress string `json:"shipping_address" binding:"required"`
	PaymentMethod   string `json:"payment_method" binding:"required"`
}

// APIResponse 通用API响应
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// PaginationResponse 分页响应
type PaginationResponse struct {
	Data       interface{} `json:"data"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	Total      int         `json:"total"`
	TotalPages int         `json:"total_pages"`
}

// ProductReview 产品评价模型
type ProductReview struct {
	ID        int       `json:"id" db:"id"`
	UserID    int       `json:"user_id" db:"user_id"`
	ProductID int       `json:"product_id" db:"product_id"`
	Rating    int       `json:"rating" db:"rating"`
	Comment   string    `json:"comment" db:"comment"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
	User      *User     `json:"user,omitempty"`
}

// Coupon 优惠券模型
type Coupon struct {
	ID                int       `json:"id" db:"id"`
	Code              string    `json:"code" db:"code"`
	Name              string    `json:"name" db:"name"`
	Description       string    `json:"description" db:"description"`
	DiscountType      string    `json:"discount_type" db:"discount_type"` // percentage, fixed
	DiscountValue     float64   `json:"discount_value" db:"discount_value"`
	MinOrderAmount    float64   `json:"min_order_amount" db:"min_order_amount"`
	MaxDiscountAmount *float64  `json:"max_discount_amount" db:"max_discount_amount"`
	UsageLimit        int       `json:"usage_limit" db:"usage_limit"`
	UsedCount         int       `json:"used_count" db:"used_count"`
	StartDate         time.Time `json:"start_date" db:"start_date"`
	EndDate           time.Time `json:"end_date" db:"end_date"`
	IsActive          bool      `json:"is_active" db:"is_active"`
	CreatedAt         time.Time `json:"created_at" db:"created_at"`
}

// UserCoupon 用户优惠券模型
type UserCoupon struct {
	ID        int        `json:"id" db:"id"`
	UserID    int        `json:"user_id" db:"user_id"`
	CouponID  int        `json:"coupon_id" db:"coupon_id"`
	UsedAt    *time.Time `json:"used_at" db:"used_at"`
	OrderID   *int       `json:"order_id" db:"order_id"`
	CreatedAt time.Time  `json:"created_at" db:"created_at"`
	Coupon    *Coupon    `json:"coupon,omitempty"`
}

// UserFavorite 用户收藏模型
type UserFavorite struct {
	ID        int       `json:"id" db:"id"`
	UserID    int       `json:"user_id" db:"user_id"`
	ProductID int       `json:"product_id" db:"product_id"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	Product   *Product  `json:"product,omitempty"`
}

// 新增请求模型

// CreateReviewRequest 创建评价请求
type CreateReviewRequest struct {
	ProductID int    `json:"product_id" binding:"required"`
	Rating    int    `json:"rating" binding:"required,min=1,max=5"`
	Comment   string `json:"comment"`
}

// CreateCouponRequest 创建优惠券请求
type CreateCouponRequest struct {
	Code              string    `json:"code" binding:"required"`
	Name              string    `json:"name" binding:"required"`
	Description       string    `json:"description"`
	DiscountType      string    `json:"discount_type" binding:"required,oneof=percentage fixed"`
	DiscountValue     float64   `json:"discount_value" binding:"required,gt=0"`
	MinOrderAmount    float64   `json:"min_order_amount"`
	MaxDiscountAmount *float64  `json:"max_discount_amount"`
	UsageLimit        int       `json:"usage_limit"`
	StartDate         time.Time `json:"start_date" binding:"required"`
	EndDate           time.Time `json:"end_date" binding:"required"`
}

// ApplyCouponRequest 应用优惠券请求
type ApplyCouponRequest struct {
	CouponCode string `json:"coupon_code" binding:"required"`
}

// PaymentRequest 支付请求
type PaymentRequest struct {
	OrderID       int     `json:"order_id" binding:"required"`
	PaymentMethod string  `json:"payment_method" binding:"required,oneof=alipay wechat credit_card"`
	Amount        float64 `json:"amount" binding:"required,gt=0"`
}
