@echo off
echo ========================================
echo    X Oberon 巧克力商店 - 前后端分离版
echo ========================================
echo.

echo [1/4] 检查环境...
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 Node.js，请先安装 Node.js
    pause
    exit /b 1
)

where docker >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 Docker，请先安装 Docker
    pause
    exit /b 1
)

echo ✓ Node.js 已安装
echo ✓ Docker 已安装
echo.

echo [2/4] 启动后端服务...
cd backend
start "X Oberon Backend" cmd /k "echo 启动后端服务... && docker-compose up"
cd ..

echo [3/4] 等待后端服务启动...
timeout /t 15 /nobreak > nul

echo [4/4] 启动前端开发服务器...
cd frontend

REM 检查是否已安装依赖
if not exist "node_modules" (
    echo 正在安装前端依赖...
    npm install
)

start "X Oberon Frontend" cmd /k "echo 启动前端服务... && npm run serve"
cd ..

echo.
echo ========================================
echo    X Oberon 巧克力商店已启动！
echo ========================================
echo.
echo 🍫 前端地址: http://localhost:3000
echo 🔧 后端API: http://localhost:8080
echo 📊 健康检查: http://localhost:8080/health
echo.
echo 提示：
echo - 前端使用 Vue.js + Element Plus
echo - 后端使用 Go + Gin + PostgreSQL + Redis
echo - 支持热重载和实时更新
echo.
echo 按任意键关闭此窗口...
pause > nul
