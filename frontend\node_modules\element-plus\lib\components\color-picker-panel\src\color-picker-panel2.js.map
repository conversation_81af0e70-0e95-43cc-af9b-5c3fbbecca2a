{"version": 3, "file": "color-picker-panel2.js", "sources": ["../../../../../../packages/components/color-picker-panel/src/color-picker-panel.vue"], "sourcesContent": ["<template>\n  <div :class=\"[ns.b(), ns.is('disabled', disabled), ns.is('border', border)]\">\n    <div :class=\"ns.e('wrapper')\">\n      <hue-slider\n        ref=\"hue\"\n        class=\"hue-slider\"\n        :color=\"color\"\n        vertical\n        :disabled=\"disabled\"\n      />\n      <sv-panel ref=\"sv\" :color=\"color\" :disabled=\"disabled\" />\n    </div>\n    <alpha-slider\n      v-if=\"showAlpha\"\n      ref=\"alpha\"\n      :color=\"color\"\n      :disabled=\"disabled\"\n    />\n    <predefine\n      v-if=\"predefine\"\n      ref=\"predefine\"\n      :enable-alpha=\"showAlpha\"\n      :color=\"color\"\n      :colors=\"predefine\"\n      :disabled=\"disabled\"\n    />\n    <div :class=\"ns.e('footer')\">\n      <el-input\n        ref=\"inputRef\"\n        v-model=\"customInput\"\n        :validate-event=\"false\"\n        size=\"small\"\n        :disabled=\"disabled\"\n        @change=\"handleConfirm\"\n      />\n      <slot name=\"footer\" />\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, nextTick, onMounted, provide, ref, watch } from 'vue'\nimport { ElInput } from '@element-plus/components/input'\nimport { useFormDisabled } from '@element-plus/components/form'\nimport { useNamespace } from '@element-plus/hooks'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport AlphaSlider from './components/alpha-slider.vue'\nimport HueSlider from './components/hue-slider.vue'\nimport Predefine from './components/predefine.vue'\nimport SvPanel from './components/sv-panel.vue'\nimport {\n  ROOT_COMMON_COLOR_INJECTION_KEY,\n  colorPickerPanelContextKey,\n  colorPickerPanelEmits,\n  colorPickerPanelProps,\n} from './color-picker-panel'\nimport { useCommonColor } from './composables/use-common-color'\n\nimport type { InputInstance } from '@element-plus/components/input'\n\ndefineOptions({\n  name: 'ElColorPickerPanel',\n})\nconst props = defineProps(colorPickerPanelProps)\nconst emit = defineEmits(colorPickerPanelEmits)\n\nconst ns = useNamespace('color-picker-panel')\nconst disabled = useFormDisabled()\nconst hue = ref<InstanceType<typeof HueSlider>>()\nconst sv = ref<InstanceType<typeof SvPanel>>()\nconst alpha = ref<InstanceType<typeof AlphaSlider>>()\nconst inputRef = ref<InputInstance>()\nconst customInput = ref('')\n\nconst { color } = inject(\n  ROOT_COMMON_COLOR_INJECTION_KEY,\n  () => useCommonColor(props, emit),\n  true\n)\n\nfunction handleConfirm() {\n  color.fromString(customInput.value)\n  if (color.value !== customInput.value) {\n    customInput.value = color.value\n  }\n}\n\nonMounted(() => {\n  if (props.modelValue) {\n    customInput.value = color.value\n  }\n  nextTick(() => {\n    hue.value?.update()\n    sv.value?.update()\n    alpha.value?.update()\n  })\n})\n\nwatch(\n  () => props.modelValue,\n  (newVal) => {\n    if (newVal && newVal !== color.value) {\n      color.fromString(newVal)\n    }\n  }\n)\n\nwatch(\n  () => color.value,\n  (val) => {\n    emit(UPDATE_MODEL_EVENT, val)\n    customInput.value = val\n  }\n)\n\nprovide(colorPickerPanelContextKey, {\n  currentColor: computed(() => color.value),\n})\n\ndefineExpose({\n  /**\n   * @description current color object\n   */\n  color,\n  /**\n   * @description custom input ref\n   */\n  inputRef,\n})\n</script>\n"], "names": ["useNamespace", "useFormDisabled", "ref", "inject", "ROOT_COMMON_COLOR_INJECTION_KEY", "useCommonColor", "onMounted", "nextTick", "watch", "UPDATE_MODEL_EVENT", "provide", "colorPickerPanelContextKey", "computed", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref", "_createElementVNode", "_createVNode", "HueSlider"], "mappings": ";;;;;;;;;;;;;;;;;uCA4Dc,CAAA;AAAA,EACZ,IAAM,EAAA,oBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAIA,IAAM,MAAA,EAAA,GAAKA,mBAAa,oBAAoB,CAAA,CAAA;AAC5C,IAAA,MAAM,WAAWC,kCAAgB,EAAA,CAAA;AACjC,IAAA,MAAM,MAAMC,OAAoC,EAAA,CAAA;AAChD,IAAA,MAAM,KAAKA,OAAkC,EAAA,CAAA;AAC7C,IAAA,MAAM,QAAQA,OAAsC,EAAA,CAAA;AACpD,IAAA,MAAM,WAAWA,OAAmB,EAAA,CAAA;AACpC,IAAM,MAAA,WAAA,GAAcA,QAAI,EAAE,CAAA,CAAA;AAE1B,IAAM,MAAA,EAAE,OAAU,GAAAC,UAAA,CAAAC,gDAAA,EAAA,MAAAC,6BAAA,CAAA,KAAA,EAAA,IAAA,CAAA,EAAA,IAAA,CAAA,CAAA;AAAA,IAChB,SAAA,aAAA,GAAA;AAAA,MACA,KAAA,CAAM,UAAe,CAAA,WAAO,CAAI,KAAA,CAAA,CAAA;AAAA,MAChC,IAAA,KAAA,CAAA,KAAA,KAAA,WAAA,CAAA,KAAA,EAAA;AAAA,QACF,WAAA,CAAA,KAAA,GAAA,KAAA,CAAA,KAAA,CAAA;AAEA,OAAA;AACE,KAAM;AACN,IAAIC,aAAA,CAAA,MAAgB;AAClB,MAAA,IAAA,KAAA,CAAA,YAAoB;AAAM,QAC5B,WAAA,CAAA,KAAA,GAAA,KAAA,CAAA,KAAA,CAAA;AAAA,OACF;AAEA,MAAAC,YAAU,CAAM,MAAA;AACd,QAAA,QAAU,EAAY,EAAA,EAAA,CAAA;AACpB,QAAA,CAAA,EAAA,GAAA,GAAA,CAAA,UAAoB,IAAM,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,EAAA,CAAA;AAAA,QAC5B,CAAA,EAAA,GAAA,EAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,EAAA,CAAA;AACA,QAAA,CAAA,EAAA,GAAA,KAAe,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,EAAA,CAAA;AACb,OAAA,CAAA,CAAA;AACA,KAAA,CAAA,CAAA;AACA,IAAAC,SAAA,CAAA,WAAa,CAAO,UAAA,EAAA,CAAA,MAAA,KAAA;AAAA,MACtB,IAAC,MAAA,IAAA,MAAA,KAAA,KAAA,CAAA,KAAA,EAAA;AAAA,QACF,KAAA,CAAA,UAAA,CAAA,MAAA,CAAA,CAAA;AAED,OAAA;AAAA,KAAA,CACE;AAAY,IAAAA,SACA,CAAA,MAAA,KAAA,CAAA,KAAA,EAAA,CAAA,GAAA,KAAA;AACV,MAAI,IAAA,CAAAC,wBAAqB,EAAA,GAAA,CAAA,CAAA;AACvB,MAAA,uBAAuB,CAAA;AAAA,KACzB,CAAA,CAAA;AAAA,IACFC,WAAA,CAAAC,2CAAA,EAAA;AAAA,MACF,YAAA,EAAAC,YAAA,CAAA,MAAA,KAAA,CAAA,KAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AAAA,IAAA;AACc,MACZ,KAAS;AACP,MAAA;AACA,KAAA,CAAA,CAAA;AAAoB,IACtB,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACF,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAEA,QAAA,KAAoC,EAAAC,kBAAA,CAAA,CAAAC,SAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,EAAAA,SAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,UAAA,EAAAA,SAAA,CAAA,QAAA,CAAA,CAAA,EAAAA,SAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,QAAA,EAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,OACpB,EAAA;AAA0B,QACzCC,sBAAA,CAAA,KAAA,EAAA;AAED,UAAa,KAAA,EAAAF,kBAAA,CAAAC,SAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA;AAAA,SAAA,EAAA;AAAA,UAAAE,eAAA,CAAAC,oBAAA,EAAA;AAAA,YAAA,OAAA,EAAA,KAAA;AAAA,YAIX,GAAA,EAAA,GAAA;AAAA,YAAA,KAAA,EAAA,YAAA;AAAA,YAAA,KAAA,EAAAH,SAAA,CAAA,KAAA,CAAA;AAAA,YAAA,QAAA,EAAA,EAAA;AAAA,YAIA,QAAA,EAAAA,SAAA,CAAA,QAAA,CAAA;AAAA,WACD,EAAA,IAAA,EAAA,CAAA,EAAA,CAAA,OAAA,EAAA,UAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}