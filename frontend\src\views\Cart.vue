<template>
  <div class="cart-page">
    <div class="container">
      <div class="page-header">
        <h1>购物车</h1>
        <p v-if="!isCartEmpty">您有 {{ cartCount }} 件商品</p>
      </div>
      
      <div v-if="isCartEmpty" class="empty-cart">
        <el-icon size="80" color="#ccc">
          <ShoppingCart />
        </el-icon>
        <h2>购物车为空</h2>
        <p>快去挑选您喜欢的巧克力吧！</p>
        <router-link to="/products" class="btn btn-primary">去购物</router-link>
      </div>
      
      <div v-else class="cart-content">
        <div class="cart-items">
          <div
            v-for="item in cartItems"
            :key="item.id"
            class="cart-item"
          >
            <div class="item-image">
              <img v-if="item.product.image_url" :src="item.product.image_url" :alt="item.product.name" />
              <div v-else class="placeholder-image">🍫</div>
            </div>
            
            <div class="item-details">
              <h3 class="item-name">{{ item.product.name }}</h3>
              <p class="item-description">{{ item.product.description }}</p>
              <p class="item-category">{{ item.product.category }}</p>
            </div>
            
            <div class="item-price">
              <span class="price">¥{{ item.product.price.toFixed(2) }}</span>
            </div>
            
            <div class="item-quantity">
              <el-input-number
                v-model="item.quantity"
                :min="1"
                :max="item.product.stock_quantity"
                @change="updateQuantity(item.id, item.quantity)"
              />
            </div>
            
            <div class="item-total">
              <span class="total">¥{{ (item.product.price * item.quantity).toFixed(2) }}</span>
            </div>
            
            <div class="item-actions">
              <el-button
                type="danger"
                text
                @click="removeItem(item.id)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </div>
        </div>
        
        <div class="cart-summary">
          <div class="summary-card">
            <h3>订单摘要</h3>
            <div class="summary-row">
              <span>商品总计</span>
              <span>¥{{ cartTotal.toFixed(2) }}</span>
            </div>
            <div class="summary-row">
              <span>配送费</span>
              <span>免费</span>
            </div>
            <div class="summary-row total-row">
              <span>总计</span>
              <span>¥{{ cartTotal.toFixed(2) }}</span>
            </div>
            
            <div class="summary-actions">
              <el-button @click="clearCart" size="large">清空购物车</el-button>
              <el-button type="primary" size="large" @click="goToCheckout">
                去结账
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { ShoppingCart, Delete } from '@element-plus/icons-vue'

export default {
  name: 'Cart',
  components: {
    ShoppingCart,
    Delete
  },
  setup() {
    const store = useStore()
    const router = useRouter()
    
    const cartItems = computed(() => store.getters['cart/cartItems'])
    const cartTotal = computed(() => store.getters['cart/cartTotal'])
    const cartCount = computed(() => store.getters['cart/cartCount'])
    const isCartEmpty = computed(() => store.getters['cart/isCartEmpty'])
    
    const updateQuantity = async (id, quantity) => {
      try {
        await store.dispatch('cart/updateCartItem', { id, quantity })
      } catch (error) {
        console.error('Update quantity error:', error)
      }
    }
    
    const removeItem = async (id) => {
      try {
        await store.dispatch('cart/removeFromCart', id)
      } catch (error) {
        console.error('Remove item error:', error)
      }
    }
    
    const clearCart = async () => {
      try {
        await store.dispatch('cart/clearCart')
      } catch (error) {
        console.error('Clear cart error:', error)
      }
    }
    
    const goToCheckout = () => {
      router.push('/checkout')
    }
    
    return {
      cartItems,
      cartTotal,
      cartCount,
      isCartEmpty,
      updateQuantity,
      removeItem,
      clearCart,
      goToCheckout
    }
  }
}
</script>

<style lang="scss" scoped>
.cart-page {
  padding: 40px 0;
  min-height: 60vh;
  
  .page-header {
    text-align: center;
    margin-bottom: 40px;
    
    h1 {
      font-size: 36px;
      font-weight: 800;
      color: var(--color-ink);
      margin-bottom: 8px;
    }
    
    p {
      font-size: 16px;
      color: #666;
    }
  }
  
  .empty-cart {
    text-align: center;
    padding: 80px 20px;
    
    h2 {
      font-size: 24px;
      color: var(--color-ink);
      margin: 24px 0 16px 0;
    }
    
    p {
      font-size: 16px;
      color: #666;
      margin-bottom: 32px;
    }
  }
  
  .cart-content {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 40px;
    
    .cart-items {
      .cart-item {
        display: grid;
        grid-template-columns: 80px 1fr 100px 120px 100px 80px;
        gap: 20px;
        align-items: center;
        padding: 20px;
        border: 1px solid #f0f0f0;
        border-radius: var(--radius-lg);
        margin-bottom: 16px;
        background: white;
        
        .item-image {
          width: 80px;
          height: 80px;
          border-radius: var(--radius-sm);
          overflow: hidden;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          
          .placeholder-image {
            width: 100%;
            height: 100%;
            @include chocolate-gradient;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
          }
        }
        
        .item-details {
          .item-name {
            font-size: 18px;
            font-weight: 600;
            color: var(--color-ink);
            margin: 0 0 4px 0;
          }
          
          .item-description {
            font-size: 14px;
            color: #666;
            margin: 0 0 4px 0;
          }
          
          .item-category {
            font-size: 12px;
            color: var(--color-primary);
            font-weight: 600;
            margin: 0;
          }
        }
        
        .item-price {
          .price {
            font-size: 16px;
            font-weight: 600;
            color: var(--color-ink);
          }
        }
        
        .item-total {
          .total {
            font-size: 18px;
            font-weight: 700;
            color: var(--color-primary);
          }
        }
      }
    }
    
    .cart-summary {
      .summary-card {
        @include card-style;
        position: sticky;
        top: 100px;
        
        h3 {
          font-size: 20px;
          font-weight: 700;
          color: var(--color-ink);
          margin-bottom: 20px;
        }
        
        .summary-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid #f0f0f0;
          
          &.total-row {
            border-bottom: none;
            font-size: 18px;
            font-weight: 700;
            color: var(--color-ink);
            margin-top: 12px;
            padding-top: 20px;
            border-top: 2px solid var(--color-primary);
          }
        }
        
        .summary-actions {
          margin-top: 24px;
          display: flex;
          flex-direction: column;
          gap: 12px;
          
          .el-button {
            width: 100%;
          }
        }
      }
    }
  }
}

@include respond-to('mobile') {
  .cart-page {
    .cart-content {
      grid-template-columns: 1fr;
      gap: 20px;
      
      .cart-items {
        .cart-item {
          grid-template-columns: 60px 1fr;
          gap: 12px;
          
          .item-image {
            width: 60px;
            height: 60px;
          }
          
          .item-details {
            grid-column: 2;
          }
          
          .item-price,
          .item-quantity,
          .item-total,
          .item-actions {
            grid-column: 1 / -1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 12px;
          }
        }
      }
    }
  }
}
</style>
