import axios from 'axios'
import Cookies from 'js-cookie'
import router from '@/router'

// 创建axios实例
const api = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || 'http://localhost:8080/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 添加认证token
    const token = Cookies.get('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 显示加载状态
    if (config.showLoading !== false) {
      // 可以在这里触发全局loading状态
    }
    
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    // 隐藏加载状态
    
    return response
  },
  error => {
    // 隐藏加载状态
    
    // 处理不同的错误状态
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          Cookies.remove('auth_token')
          delete api.defaults.headers.common['Authorization']
          router.push('/login')
          break
          
        case 403:
          // 禁止访问
          console.error('Access forbidden:', data.message)
          break
          
        case 404:
          // 资源不存在
          console.error('Resource not found:', data.message)
          break
          
        case 422:
          // 验证错误
          console.error('Validation error:', data.message)
          break
          
        case 500:
          // 服务器错误
          console.error('Server error:', data.message)
          break
          
        default:
          console.error('API error:', data.message || 'Unknown error')
      }
    } else if (error.request) {
      // 网络错误
      console.error('Network error:', error.message)
    } else {
      // 其他错误
      console.error('Error:', error.message)
    }
    
    return Promise.reject(error)
  }
)

// API方法封装
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  logout: () => api.post('/auth/logout'),
  getProfile: () => api.get('/users/profile'),
  updateProfile: (data) => api.put('/users/profile', data)
}

export const productsAPI = {
  getProducts: (params) => api.get('/products', { params }),
  getProduct: (id) => api.get(`/products/${id}`),
  getProductsByCategory: (category, params) => api.get(`/products/category/${category}`, { params }),
  searchProducts: (params) => api.get('/products/search', { params })
}

export const cartAPI = {
  getCart: () => api.get('/cart'),
  addToCart: (data) => api.post('/cart/add', data),
  updateCartItem: (id, data) => api.put(`/cart/update/${id}`, data),
  removeFromCart: (id) => api.delete(`/cart/remove/${id}`),
  clearCart: () => api.delete('/cart/clear')
}

export const ordersAPI = {
  getOrders: (params) => api.get('/orders', { params }),
  getOrder: (id) => api.get(`/orders/${id}`),
  createOrder: (data) => api.post('/orders', data),
  updateOrderStatus: (id, data) => api.put(`/orders/${id}/status`, data)
}

export default api
