// X Oberon 巧克力主题色彩变量
:root {
  --color-primary: #f4b83a; /* 温暖黄色 */
  --color-primary-700: #cf941a; /* 深黄色 */
  --color-teal: #34d0c6; /* 青色 */
  --color-teal-700: #0d9488; /* 深青色 */
  --color-ink: #111111; /* 深色文字 */
  --color-bg: #fff8e7; /* 温暖背景色 */
  --color-sand: #ffe4a3; /* 沙色 */
  --color-chocolate: #8B4513; /* 巧克力色 */
  --color-chocolate-light: #D2691E; /* 浅巧克力色 */
  --color-success: #10b981; /* 成功色 */
  --color-error: #ef4444; /* 错误色 */
  --color-warning: #f59e0b; /* 警告色 */
  --color-info: #3b82f6; /* 信息色 */
  
  --radius-lg: 20px;
  --radius-md: 12px;
  --radius-sm: 8px;
  --shadow-1: 0 8px 24px rgba(0,0,0,.08);
  --shadow-2: 0 16px 40px rgba(0,0,0,.12);
  
  --container-width: min(1100px, 92vw);
  --header-height: 80px;
}

// 巧克力渐变
@mixin chocolate-gradient {
  background: linear-gradient(135deg, var(--color-chocolate), var(--color-chocolate-light));
}

// 主色调渐变
@mixin primary-gradient {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-700));
}

// 卡片样式
@mixin card-style {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-1);
  padding: 24px;
}

// 按钮样式
@mixin btn-primary {
  background: var(--color-primary);
  color: var(--color-ink);
  border: none;
  padding: 12px 20px;
  border-radius: 999px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: var(--color-primary-700);
    transform: translateY(-1px);
  }
}

// 响应式断点
$breakpoints: (
  'mobile': 768px,
  'tablet': 1024px,
  'desktop': 1200px
);

@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (max-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}
