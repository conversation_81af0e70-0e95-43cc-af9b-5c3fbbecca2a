{"version": 3, "file": "scrollbar.js", "sources": ["../../../../../../../packages/components/virtual-list/src/components/scrollbar.ts"], "sourcesContent": ["import {\n  computed,\n  defineComponent,\n  h,\n  onBeforeUnmount,\n  reactive,\n  ref,\n  unref,\n  watch,\n  withModifiers,\n} from 'vue'\nimport { BAR_MAP } from '@element-plus/components/scrollbar'\nimport { cAF, rAF } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { HORIZONTAL, SCROLLBAR_MIN_SIZE, ScrollbarDirKey } from '../defaults'\nimport { virtualizedScrollbarProps } from '../props'\nimport { renderThumbStyle } from '../utils'\n\nimport type { CSSProperties } from 'vue'\n\ninterface ScrollState {\n  isDragging: boolean\n  traveled: number\n  [key: string]: unknown\n}\n\nconst ScrollBar = defineComponent({\n  name: 'ElVirtualScrollBar',\n  props: virtualizedScrollbarProps,\n  emits: ['scroll', 'start-move', 'stop-move'],\n  setup(props, { emit }) {\n    const GAP = computed(() => props.startGap + props.endGap) // top 2 + bottom 2 | left 2 + right 2\n\n    const nsVirtualScrollbar = useNamespace('virtual-scrollbar')\n    const nsScrollbar = useNamespace('scrollbar')\n    // DOM refs\n    const trackRef = ref<HTMLElement>()\n    const thumbRef = ref<HTMLElement>()\n\n    // local variables\n    let frameHandle: null | number = null\n    let onselectstartStore: null | typeof document.onselectstart = null\n\n    // data\n    const state = reactive<ScrollState>({\n      isDragging: false,\n      traveled: 0,\n    })\n\n    const bar = computed(() => BAR_MAP[props.layout])\n\n    const trackSize = computed(() => props.clientSize! - unref(GAP))\n\n    const trackStyle = computed<CSSProperties>(() => ({\n      position: 'absolute',\n      width: `${\n        HORIZONTAL === props.layout ? trackSize.value : props.scrollbarSize\n      }px`,\n      height: `${\n        HORIZONTAL === props.layout ? props.scrollbarSize : trackSize.value\n      }px`,\n      [ScrollbarDirKey[props.layout]]: '2px',\n      right: '2px',\n      bottom: '2px',\n      borderRadius: '4px',\n    }))\n\n    const thumbSize = computed(() => {\n      const ratio = props.ratio!\n      if (ratio >= 100) {\n        return Number.POSITIVE_INFINITY\n      }\n\n      if (ratio >= 50) {\n        return (ratio * trackSize.value) / 100\n      }\n\n      const SCROLLBAR_MAX_SIZE = trackSize.value / 3\n      return Math.floor(\n        Math.min(\n          Math.max((ratio * trackSize.value) / 100, SCROLLBAR_MIN_SIZE),\n          SCROLLBAR_MAX_SIZE\n        )\n      )\n    })\n\n    const thumbStyle = computed<CSSProperties>(() => {\n      if (!Number.isFinite(thumbSize.value)) {\n        return {\n          display: 'none',\n        }\n      }\n\n      const thumb = `${thumbSize.value}px`\n\n      const style = renderThumbStyle(\n        {\n          bar: bar.value,\n          size: thumb,\n          move: state.traveled,\n        },\n        props.layout\n      )\n\n      return style\n    })\n\n    const totalSteps = computed(() =>\n      Math.ceil(props.clientSize! - thumbSize.value - unref(GAP))\n    )\n\n    const attachEvents = () => {\n      window.addEventListener('mousemove', onMouseMove)\n      window.addEventListener('mouseup', onMouseUp)\n\n      const thumbEl = unref(thumbRef)\n\n      if (!thumbEl) return\n\n      onselectstartStore = document.onselectstart\n      document.onselectstart = () => false\n\n      thumbEl.addEventListener('touchmove', onMouseMove, { passive: true })\n      thumbEl.addEventListener('touchend', onMouseUp)\n    }\n\n    const detachEvents = () => {\n      window.removeEventListener('mousemove', onMouseMove)\n      window.removeEventListener('mouseup', onMouseUp)\n\n      document.onselectstart = onselectstartStore\n      onselectstartStore = null\n\n      const thumbEl = unref(thumbRef)\n      if (!thumbEl) return\n\n      thumbEl.removeEventListener('touchmove', onMouseMove)\n      thumbEl.removeEventListener('touchend', onMouseUp)\n    }\n\n    const onThumbMouseDown = (e: Event | KeyboardEvent | MouseEvent) => {\n      e.stopImmediatePropagation()\n      if (\n        (e as KeyboardEvent).ctrlKey ||\n        [1, 2].includes((e as MouseEvent).button)\n      ) {\n        return\n      }\n\n      state.isDragging = true\n      state[bar.value.axis] =\n        (e.currentTarget as HTMLElement)[bar.value.offset] -\n        ((e as MouseEvent)[bar.value.client] -\n          (e.currentTarget as HTMLElement).getBoundingClientRect()[\n            bar.value.direction\n          ])\n\n      emit('start-move')\n      attachEvents()\n    }\n\n    const onMouseUp = () => {\n      state.isDragging = false\n      state[bar.value.axis] = 0\n      emit('stop-move')\n      detachEvents()\n    }\n\n    const onMouseMove = (e: MouseEvent | TouchEvent) => {\n      const { isDragging } = state\n      if (!isDragging) return\n      if (!thumbRef.value || !trackRef.value) return\n\n      const prevPage = state[bar.value.axis]\n      if (!prevPage) return\n\n      cAF(frameHandle!)\n      // using the current track's offset top/left - the current pointer's clientY/clientX\n      // to get the relative position of the pointer to the track.\n      const offset =\n        (trackRef.value.getBoundingClientRect()[bar.value.direction] -\n          (e as MouseEvent)[bar.value.client]) *\n        -1\n\n      // find where the thumb was clicked on.\n      const thumbClickPosition =\n        thumbRef.value[bar.value.offset] - (prevPage as number)\n      /**\n       *  +--------------+                                   +--------------+\n       *  |              -  <--------- thumb.offsetTop       |              |\n       *  |             |+|             <--+                 |              |\n       *  |              -                 |                 |              |\n       *  |   Content    |                 |                 |              |\n       *  |              |                 |                 |              |\n       *  |              |                 |                 |              |\n       *  |              |                 |                 |              -\n       *  |              |                 +-->              |             |+|\n       *  |              |                                   |              -\n       *  +--------------+                                   +--------------+\n       */\n\n      // using the current position - prev position to\n\n      const distance = offset - thumbClickPosition\n      // get how many steps in total.\n      // gap of 2 on top, 2 on bottom, in total 4.\n      // using totalSteps ÷ totalSize getting each step's size * distance to get the new\n      // scroll offset to scrollTo\n      frameHandle = rAF(() => {\n        state.traveled = Math.max(0, Math.min(distance, totalSteps.value))\n        emit('scroll', distance, totalSteps.value)\n      })\n    }\n\n    const clickTrackHandler = (e: MouseEvent) => {\n      const offset = Math.abs(\n        (e.target as HTMLElement).getBoundingClientRect()[bar.value.direction] -\n          e[bar.value.client]\n      )\n      const thumbHalf = thumbRef.value![bar.value.offset] / 2\n      const distance = offset - thumbHalf\n\n      state.traveled = Math.max(0, Math.min(distance, totalSteps.value))\n      emit('scroll', distance, totalSteps.value)\n    }\n\n    watch(\n      () => props.scrollFrom,\n      (v) => {\n        if (state.isDragging) return\n        /**\n         *  this is simply mapping the current scrollbar offset\n         *\n         *  formula 1:\n         *    v = scrollOffset / (estimatedTotalSize - clientSize)\n         *    traveled = v * (clientSize - thumbSize - GAP) --> v * totalSteps\n         *\n         *  formula 2:\n         *    traveled = (v * clientSize) / (clientSize / totalSteps) --> (v * clientSize) * (totalSteps / clientSize) --> v * totalSteps\n         */\n        state.traveled = Math.ceil(v! * totalSteps.value)\n      }\n    )\n\n    onBeforeUnmount(() => {\n      detachEvents()\n    })\n\n    return () => {\n      return h(\n        'div',\n        {\n          role: 'presentation',\n          ref: trackRef,\n          class: [\n            nsVirtualScrollbar.b(),\n            props.class,\n            (props.alwaysOn || state.isDragging) && 'always-on',\n          ],\n          style: trackStyle.value,\n          onMousedown: withModifiers(clickTrackHandler, ['stop', 'prevent']),\n          onTouchstartPrevent: onThumbMouseDown,\n        },\n        h(\n          'div',\n          {\n            ref: thumbRef,\n            class: nsScrollbar.e('thumb'),\n            style: thumbStyle.value,\n            onMousedown: onThumbMouseDown,\n          },\n          []\n        )\n      )\n    }\n  },\n})\n\nexport default ScrollBar\n"], "names": ["defineComponent", "virtualizedScrollbarProps", "computed", "useNamespace", "ref", "reactive", "BAR_MAP", "unref", "HORIZONTAL", "ScrollbarDirKey", "SCROLLBAR_MIN_SIZE", "renderThumbStyle", "cAF", "rAF", "watch", "onBeforeUnmount", "h", "withModifiers"], "mappings": ";;;;;;;;;;;;AAiBK,MAAC,SAAS,GAAGA,mBAAe,CAAC;AAClC,EAAE,IAAI,EAAE,oBAAoB;AAC5B,EAAE,KAAK,EAAEC,+BAAyB;AAClC,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,WAAW,CAAC;AAC9C,EAAE,KAAK,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE;AACzB,IAAI,MAAM,GAAG,GAAGC,YAAQ,CAAC,MAAM,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AAC9D,IAAI,MAAM,kBAAkB,GAAGC,kBAAY,CAAC,mBAAmB,CAAC,CAAC;AACjE,IAAI,MAAM,WAAW,GAAGA,kBAAY,CAAC,WAAW,CAAC,CAAC;AAClD,IAAI,MAAM,QAAQ,GAAGC,OAAG,EAAE,CAAC;AAC3B,IAAI,MAAM,QAAQ,GAAGA,OAAG,EAAE,CAAC;AAC3B,IAAI,IAAI,WAAW,GAAG,IAAI,CAAC;AAC3B,IAAI,IAAI,kBAAkB,GAAG,IAAI,CAAC;AAClC,IAAI,MAAM,KAAK,GAAGC,YAAQ,CAAC;AAC3B,MAAM,UAAU,EAAE,KAAK;AACvB,MAAM,QAAQ,EAAE,CAAC;AACjB,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,GAAG,GAAGH,YAAQ,CAAC,MAAMI,YAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;AACtD,IAAI,MAAM,SAAS,GAAGJ,YAAQ,CAAC,MAAM,KAAK,CAAC,UAAU,GAAGK,SAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AACpE,IAAI,MAAM,UAAU,GAAGL,YAAQ,CAAC,OAAO;AACvC,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,KAAK,EAAE,CAAC,EAAEM,mBAAU,KAAK,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;AACvF,MAAM,MAAM,EAAE,CAAC,EAAEA,mBAAU,KAAK,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;AACxF,MAAM,CAACC,wBAAe,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK;AAC5C,MAAM,KAAK,EAAE,KAAK;AAClB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,YAAY,EAAE,KAAK;AACzB,KAAK,CAAC,CAAC,CAAC;AACR,IAAI,MAAM,SAAS,GAAGP,YAAQ,CAAC,MAAM;AACrC,MAAM,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AAChC,MAAM,IAAI,KAAK,IAAI,GAAG,EAAE;AACxB,QAAQ,OAAO,MAAM,CAAC,iBAAiB,CAAC;AACxC,OAAO;AACP,MAAM,IAAI,KAAK,IAAI,EAAE,EAAE;AACvB,QAAQ,OAAO,KAAK,GAAG,SAAS,CAAC,KAAK,GAAG,GAAG,CAAC;AAC7C,OAAO;AACP,MAAM,MAAM,kBAAkB,GAAG,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC;AACrD,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,GAAG,GAAG,EAAEQ,2BAAkB,CAAC,EAAE,kBAAkB,CAAC,CAAC,CAAC;AACnH,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,UAAU,GAAGR,YAAQ,CAAC,MAAM;AACtC,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;AAC7C,QAAQ,OAAO;AACf,UAAU,OAAO,EAAE,MAAM;AACzB,SAAS,CAAC;AACV,OAAO;AACP,MAAM,MAAM,KAAK,GAAG,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAC3C,MAAM,MAAM,KAAK,GAAGS,sBAAgB,CAAC;AACrC,QAAQ,GAAG,EAAE,GAAG,CAAC,KAAK;AACtB,QAAQ,IAAI,EAAE,KAAK;AACnB,QAAQ,IAAI,EAAE,KAAK,CAAC,QAAQ;AAC5B,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;AACvB,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,UAAU,GAAGT,YAAQ,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS,CAAC,KAAK,GAAGK,SAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAClG,IAAI,MAAM,YAAY,GAAG,MAAM;AAC/B,MAAM,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AACxD,MAAM,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AACpD,MAAM,MAAM,OAAO,GAAGA,SAAK,CAAC,QAAQ,CAAC,CAAC;AACtC,MAAM,IAAI,CAAC,OAAO;AAClB,QAAQ,OAAO;AACf,MAAM,kBAAkB,GAAG,QAAQ,CAAC,aAAa,CAAC;AAClD,MAAM,QAAQ,CAAC,aAAa,GAAG,MAAM,KAAK,CAAC;AAC3C,MAAM,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,WAAW,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;AAC5E,MAAM,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;AACtD,KAAK,CAAC;AACN,IAAI,MAAM,YAAY,GAAG,MAAM;AAC/B,MAAM,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AAC3D,MAAM,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AACvD,MAAM,QAAQ,CAAC,aAAa,GAAG,kBAAkB,CAAC;AAClD,MAAM,kBAAkB,GAAG,IAAI,CAAC;AAChC,MAAM,MAAM,OAAO,GAAGA,SAAK,CAAC,QAAQ,CAAC,CAAC;AACtC,MAAM,IAAI,CAAC,OAAO;AAClB,QAAQ,OAAO;AACf,MAAM,OAAO,CAAC,mBAAmB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AAC5D,MAAM,OAAO,CAAC,mBAAmB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;AACzD,KAAK,CAAC;AACN,IAAI,MAAM,gBAAgB,GAAG,CAAC,CAAC,KAAK;AACpC,MAAM,CAAC,CAAC,wBAAwB,EAAE,CAAC;AACnC,MAAM,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE;AAClD,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC;AAC9B,MAAM,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AACvJ,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;AACzB,MAAM,YAAY,EAAE,CAAC;AACrB,KAAK,CAAC;AACN,IAAI,MAAM,SAAS,GAAG,MAAM;AAC5B,MAAM,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;AAC/B,MAAM,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAChC,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC;AACxB,MAAM,YAAY,EAAE,CAAC;AACrB,KAAK,CAAC;AACN,IAAI,MAAM,WAAW,GAAG,CAAC,CAAC,KAAK;AAC/B,MAAM,MAAM,EAAE,UAAU,EAAE,GAAG,KAAK,CAAC;AACnC,MAAM,IAAI,CAAC,UAAU;AACrB,QAAQ,OAAO;AACf,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK;AAC5C,QAAQ,OAAO;AACf,MAAM,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC7C,MAAM,IAAI,CAAC,QAAQ;AACnB,QAAQ,OAAO;AACf,MAAMK,OAAG,CAAC,WAAW,CAAC,CAAC;AACvB,MAAM,MAAM,MAAM,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9G,MAAM,MAAM,kBAAkB,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;AAC7E,MAAM,MAAM,QAAQ,GAAG,MAAM,GAAG,kBAAkB,CAAC;AACnD,MAAM,WAAW,GAAGC,OAAG,CAAC,MAAM;AAC9B,QAAQ,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;AAC3E,QAAQ,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;AACnD,OAAO,CAAC,CAAC;AACT,KAAK,CAAC;AACN,IAAI,MAAM,iBAAiB,GAAG,CAAC,CAAC,KAAK;AACrC,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;AAC3G,MAAM,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC7D,MAAM,MAAM,QAAQ,GAAG,MAAM,GAAG,SAAS,CAAC;AAC1C,MAAM,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;AACzE,MAAM,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;AACjD,KAAK,CAAC;AACN,IAAIC,SAAK,CAAC,MAAM,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,KAAK;AACzC,MAAM,IAAI,KAAK,CAAC,UAAU;AAC1B,QAAQ,OAAO;AACf,MAAM,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;AACvD,KAAK,CAAC,CAAC;AACP,IAAIC,mBAAe,CAAC,MAAM;AAC1B,MAAM,YAAY,EAAE,CAAC;AACrB,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,MAAM;AACjB,MAAM,OAAOC,KAAC,CAAC,KAAK,EAAE;AACtB,QAAQ,IAAI,EAAE,cAAc;AAC5B,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,KAAK,EAAE;AACf,UAAU,kBAAkB,CAAC,CAAC,EAAE;AAChC,UAAU,KAAK,CAAC,KAAK;AACrB,UAAU,CAAC,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,UAAU,KAAK,WAAW;AAC7D,SAAS;AACT,QAAQ,KAAK,EAAE,UAAU,CAAC,KAAK;AAC/B,QAAQ,WAAW,EAAEC,iBAAa,CAAC,iBAAiB,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AAC1E,QAAQ,mBAAmB,EAAE,gBAAgB;AAC7C,OAAO,EAAED,KAAC,CAAC,KAAK,EAAE;AAClB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC;AACrC,QAAQ,KAAK,EAAE,UAAU,CAAC,KAAK;AAC/B,QAAQ,WAAW,EAAE,gBAAgB;AACrC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;AACd,KAAK,CAAC;AACN,GAAG;AACH,CAAC;;;;"}