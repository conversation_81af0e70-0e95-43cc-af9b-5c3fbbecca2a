<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>美食外卖 · 首页</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Bangers&family=Inter:wght@400;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/styles.css" />
  </head>
  <body>
    <div class="collage sprinkles"></div>
    <header>
      <div class="container nav">
        <a class="brand" href="index.html">
          <span class="brand-mark">🍽️</span>
          <span>美食外卖</span>
        </a>
        <nav>
          <ul>
            <li><a href="products.html">商家</a></li>
            <li><a href="story.html">关于我们</a></li>
            <li><a href="benefits.html">优势</a></li>
            <li><a href="subscribe.html">订阅</a></li>
          </ul>
        </nav>
      </div>
    </header>

    <main>
      <section class="hero">
        <div class="container hero-inner">
          <div class="reveal">
            <h1>美食外卖<br />新鲜送达 · 快速便捷</h1>
            <p>精选优质商家，新鲜食材现做现送。基于 Go + Gin + MySQL + Redis 构建的现代化外卖平台，为您提供极致的订餐体验。</p>
            <div style="display:flex; gap:12px; margin-top:18px;">
              <a class="btn btn-primary" href="products.html">浏览商家</a>
              <a class="btn" href="story.html">了解平台</a>
            </div>
          </div>
          <div class="hero-card reveal" style="transform:rotate(-3deg);">
            <div class="box-stack">
              <div class="choco-box choco-box--1">🍕 披萨</div>
              <div class="choco-box choco-box--2">🍔 汉堡</div>
              <div class="choco-box choco-box--3">🍜 面条</div>
            </div>
          </div>
        </div>
      </section>

      <section class="section">
        <div class="container stickers reveal">
          <span class="sticker">Gin</span>
          <span class="sticker">MySQL</span>
          <span class="sticker">Redis</span>
          <span class="sticker">Go语言</span>
        </div>
      </section>

      <section class="section">
        <div class="container reveal">
          <h2 class="section-title">美食外卖 · 核心特性</h2>
          <p class="section-sub">基于 Go + Gin + MySQL + Redis 构建的现代化外卖平台。</p>
          <div class="grid grid-3">
            <article class="product-card reveal">
              <div class="product-art"></div>
              <div class="product-title">精选商家</div>
              <div class="badge">品质保证</div>
              <div class="price">1000+ 优质商家</div>
              <a class="btn btn-primary" href="products.html">浏览商家</a>
            </article>
            <article class="product-card reveal">
              <div class="product-art" style="background:linear-gradient(135deg,#60a5fa,#2563eb);"></div>
              <div class="product-title">快速配送</div>
              <div class="badge">30分钟送达</div>
              <div class="price">新鲜热乎</div>
              <a class="btn btn-primary" href="products.html">立即下单</a>
            </article>
            <article class="product-card reveal">
              <div class="product-art" style="background:linear-gradient(135deg,#f97316,#ef4444);"></div>
              <div class="product-title">安全支付</div>
              <div class="badge">多种支付方式</div>
              <div class="price">安全 · 便捷</div>
              <a class="btn btn-primary" href="products.html">了解更多</a>
            </article>
          </div>
        </div>
      </section>

      <section class="ooo-block">
        <div class="container reveal">
          <div class="ooo-text">美食外卖 has</div>
          <div class="ooo-row" style="margin:10px 0 20px;">
            <div class="ooo">🍕</div>
            <div class="ooo">🍔</div>
            <div class="ooo">🍜</div>
            <div class="ooo">🥗</div>
            <div class="ooo">🧁</div>
          </div>
          <div class="ooo-text">delicious food</div>
        </div>
      </section>

      <section class="section" style="background: #ffefc3;">
        <div class="container grid grid-2 reveal">
          <div class="feature">
            <h3 class="section-title" style="font-size:36px;">实时追踪</h3>
            <p>订单状态实时更新，配送进度一目了然。</p>
          </div>
          <div class="feature">
            <h3 class="section-title" style="font-size:36px;">优惠活动</h3>
            <p>新用户立减，满减优惠，让美食更实惠。</p>
          </div>
        </div>
      </section>

      <section class="section">
        <div class="container newsletter reveal">
          <h3 class="section-title" style="font-size:34px;">订阅优惠</h3>
          <p class="section-sub">订阅我们的优惠信息，第一时间获取美食折扣。</p>
          <form action="#" onsubmit="return false;">
            <input type="email" placeholder="你的邮箱" />
            <button class="btn btn-primary" type="submit">订阅</button>
          </form>
        </div>
      </section>
    </main>

    <footer>
      <div class="container" style="display:flex; justify-content:space-between; align-items:center; gap:16px;">
        <div>© 2025 XiangYueYuan</div>
        <div class="links">
          <a href="products.html">商店</a>
          <a href="story.html">关于我们</a>
          <a href="benefits.html">优势</a>
          <a href="subscribe.html">订阅</a>
        </div>
      </div>
    </footer>
    <script src="assets/js/app.js"></script>
  </body>
</html>


