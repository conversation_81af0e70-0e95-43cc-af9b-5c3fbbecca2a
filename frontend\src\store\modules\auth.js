import api from '@/utils/api'
import Cookies from 'js-cookie'

const state = {
  user: null,
  token: Cookies.get('auth_token') || null,
  isAuthenticated: false
}

const mutations = {
  SET_USER(state, user) {
    state.user = user
    state.isAuthenticated = !!user
  },
  
  SET_TOKEN(state, token) {
    state.token = token
    if (token) {
      Cookies.set('auth_token', token, { expires: 7 })
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`
    } else {
      Cookies.remove('auth_token')
      delete api.defaults.headers.common['Authorization']
    }
  },
  
  CLEAR_AUTH(state) {
    state.user = null
    state.token = null
    state.isAuthenticated = false
    Cookies.remove('auth_token')
    delete api.defaults.headers.common['Authorization']
  }
}

const actions = {
  // 初始化认证状态
  async initAuth({ commit, state }) {
    if (state.token) {
      api.defaults.headers.common['Authorization'] = `Bearer ${state.token}`
      try {
        const response = await api.get('/auth/profile')
        commit('SET_USER', response.data.data)
      } catch (error) {
        commit('CLEAR_AUTH')
      }
    }
  },
  
  // 登录
  async login({ commit }, credentials) {
    try {
      const response = await api.post('/auth/login', credentials)
      const { token, user } = response.data.data
      
      commit('SET_TOKEN', token)
      commit('SET_USER', user)
      
      return { success: true, user }
    } catch (error) {
      throw new Error(error.response?.data?.message || '登录失败')
    }
  },
  
  // 注册
  async register({ commit }, userData) {
    try {
      const response = await api.post('/auth/register', userData)
      return { success: true, data: response.data.data }
    } catch (error) {
      throw new Error(error.response?.data?.message || '注册失败')
    }
  },
  
  // 登出
  async logout({ commit }) {
    try {
      await api.post('/auth/logout')
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      commit('CLEAR_AUTH')
    }
  },
  
  // 更新用户资料
  async updateProfile({ commit }, profileData) {
    try {
      const response = await api.put('/users/profile', profileData)
      commit('SET_USER', response.data.data)
      return { success: true, user: response.data.data }
    } catch (error) {
      throw new Error(error.response?.data?.message || '更新资料失败')
    }
  }
}

const getters = {
  user: state => state.user,
  token: state => state.token,
  isAuthenticated: state => state.isAuthenticated,
  userName: state => state.user?.username || '',
  userEmail: state => state.user?.email || ''
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
