let notificationId = 0

const state = {
  notifications: []
}

const mutations = {
  ADD_NOTIFICATION(state, notification) {
    state.notifications.push({
      id: ++notificationId,
      ...notification,
      timestamp: Date.now()
    })
  },
  
  REMOVE_NOTIFICATION(state, id) {
    state.notifications = state.notifications.filter(n => n.id !== id)
  },
  
  CLEAR_NOTIFICATIONS(state) {
    state.notifications = []
  }
}

const actions = {
  showNotification({ commit }, { type = 'info', message, duration = 4000 }) {
    const notification = { type, message }
    commit('ADD_NOTIFICATION', notification)
    
    // 自动移除通知
    if (duration > 0) {
      setTimeout(() => {
        commit('REMOVE_NOTIFICATION', notification.id)
      }, duration)
    }
    
    return notification
  },
  
  removeNotification({ commit }, id) {
    commit('REMOVE_NOTIFICATION', id)
  },
  
  clearNotifications({ commit }) {
    commit('CLEAR_NOTIFICATIONS')
  }
}

const getters = {
  notifications: state => state.notifications,
  notificationCount: state => state.notifications.length
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
