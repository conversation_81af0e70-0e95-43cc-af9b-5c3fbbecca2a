{"version": 3, "file": "date-picker.js", "sources": ["../../../../../../packages/components/date-picker/src/date-picker.tsx"], "sourcesContent": ["import { computed, defineComponent, provide, reactive, ref, toRef } from 'vue'\nimport {\n  CommonPicker,\n  DEFAULT_FORMATS_DATE,\n  DEFAULT_FORMATS_DATEPICKER,\n  PICKER_POPPER_OPTIONS_INJECTION_KEY,\n} from '@element-plus/components/time-picker'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport {\n  ElDatePickerPanel,\n  ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY,\n} from '@element-plus/components/date-picker-panel'\nimport { datePickerProps } from './props'\n\nimport type {\n  DateModelType,\n  SingleOrRange,\n} from '@element-plus/components/time-picker'\nimport type { DatePickerExpose } from './instance'\n\nexport default defineComponent({\n  name: 'ElDatePicker',\n  install: null,\n  props: datePickerProps,\n  emits: [UPDATE_MODEL_EVENT],\n  setup(props, { expose, emit, slots }) {\n    const isDefaultFormat = computed(() => {\n      return !props.format\n    })\n    provide(ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY, isDefaultFormat)\n    provide(\n      PICKER_POPPER_OPTIONS_INJECTION_KEY,\n      reactive(toRef(props, 'popperOptions'))\n    )\n\n    const commonPicker = ref<InstanceType<typeof CommonPicker>>()\n    const refProps: DatePickerExpose = {\n      focus: () => {\n        commonPicker.value?.focus()\n      },\n      blur: () => {\n        commonPicker.value?.blur()\n      },\n      handleOpen: () => {\n        commonPicker.value?.handleOpen()\n      },\n      handleClose: () => {\n        commonPicker.value?.handleClose()\n      },\n    }\n\n    expose(refProps)\n\n    const onModelValueUpdated = (val: SingleOrRange<DateModelType> | null) => {\n      emit(UPDATE_MODEL_EVENT, val)\n    }\n\n    return () => {\n      // since props always have all defined keys on it, {format, ...props} will always overwrite format\n      // pick props.format or provide default value here before spreading\n      const format =\n        props.format ??\n        (DEFAULT_FORMATS_DATEPICKER[props.type] || DEFAULT_FORMATS_DATE)\n\n      return (\n        <CommonPicker\n          {...props}\n          format={format}\n          type={props.type}\n          ref={commonPicker}\n          onUpdate:modelValue={onModelValueUpdated}\n        >\n          {{\n            default: (scopedProps: /**FIXME: remove any type */ any) => (\n              <ElDatePickerPanel border={false} {...scopedProps}>\n                {slots}\n              </ElDatePickerPanel>\n            ),\n            'range-separator': slots['range-separator'],\n          }}\n        </CommonPicker>\n      )\n    }\n  },\n})\n"], "names": ["defineComponent", "name", "datePickerProps", "install", "props", "UPDATE_MODEL_EVENT", "emit", "computed", "slots", "provide", "ROOT_PICKER_IS_DEFAULT_FORMAT_INJECTION_KEY", "PICKER_POPPER_OPTIONS_INJECTION_KEY", "reactive", "toRef", "ref", "commonPicker", "focus", "blur", "handleOpen", "handleClose", "onModelValueUpdated", "val", "DEFAULT_FORMATS_DATEPICKER", "DEFAULT_FORMATS_DATE", "_createVNode", "CommonPicker", "_mergeProps", "default"], "mappings": ";;;;;;;;;;;;;;;;;;AAoBA,EAAA,OAAA,EAAA,IAAeA;AACbC,EAAAA,OAD6BC,qBAAA;AAE7BC,EAAAA,KAAAA,EAAO,yBAFsB,CAAA;AAG7BC,EAAAA,KAAK,MAHwB,EAAA;IAIxB,MAAGC;;IACH;KAAQ;IAAUC,MAAV,eAAA,GAAAC,YAAA,CAAA,MAAA;AAAgBC,MAAAA,OAAAA,CAAAA,KAAAA,CAAAA,MAAAA,CAAAA;AAAhB,KAAyB,CAAA,CAAA;AACpC,IAAAC,WAAqB,CAAAC,qDAAkB,EAAA,eAAA,CAAA,CAAA;eAC9B,CAAAC,+CAAP,EAAAC,YAAA,CAAAC,SAAA,CAAA,KAAA,EAAA,eAAA,CAAA,CAAA,CAAA,CAAA;AACD,IAAA,MAFD,YAAA,GAAAC,OAAA,EAAA,CAAA;AAGAL,IAAAA,MAAAA,QAAQC,GAAAA;AACRD,MAAAA,KAAO,EACLE,MAAAA;QAIII,IAAAA,EAAAA,CAAAA;AACN,QAAA,CAAA,iBAAmC,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,EAAA,CAAA;AACjCC,OAAAA;UACc,EAAA,MAAA;QAFmB,IAAA,EAAA,CAAA;AAIjCC,QAAAA,CAAAA,KAAY,YAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,EAAA,CAAA;;MAEX,UANgC,EAAA,MAAA;AAOjCC,QAAAA,IAAAA,EAAAA,CAAAA;QACEH,CAAY,EAAA,GAAA,YAAZ,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAA,EAAA,CAAA;OAR+B;AAUjCI,MAAAA,WAAW,EAAE,MAAM;QACjBJ,IAAY,EAAA,CAAA;AACb,QAAA,CAAA,EAAA,GAAA,YAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAA,EAAA,CAAA;OAZH;KAeM,CAAA;;IAEN,MAAMK,mBAAmB,GAAIC,CAAAA,GAA6C,KAAA;AACxEf,MAAAA,IAAI,CAACD,wBAAD,EAAqBgB,GAArB,CAAJ,CAAA;KADF,CAAA;;AAIA,MAAA,IAAA,EAAa,CAAA;AACX,MAAA,MAAA,MAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,EAAA,GAAAC,sCAAA,CAAA,KAAA,CAAA,IAAA,CAAA,IAAAC,gCAAA,CAAA;AACA,MAAA,OAAAC,eAAA,CAAAC,iBAAA,EAAAC,cAAA,CAAA,KAAA,EAAA;AACA,QAAA,QAAY,EAAA,MACL;AAGP,QAAA,MAAA,EAAA,KAAA,CAAA,IAAA;AAAA,QAAA,KAAA,EAAA,YAAA;QAAA,qBAAA,EAAA,mBAAA;AAAA,OAAA,CAAA,EAAA;QAAA,OAMyBN,EAAAA,CAAAA,WAAAA,KAAAA,eAAAA,CAAAA,uBAAAA,EAAAA,cAAAA,CAAAA;AANzB,UAAA,QAAA,EAAA,KAAA;AASMO,SAAAA,EAAAA,WAAS,CAAA,EAAA,OAAA,CAAA,KAAA,CAAA,GAAA,KAAA,GAAA;UAAA,OACoB,EAAA,MAAA,CAAA,KAAA,CAAA;AADpB,SAAA,CAAA;AAAA,QAAA,iBAAA,EAAA,KAAA,CAAA,iBAAA,CAAA;QAAA,CATf;;AAAA,GAAA;;;;;"}