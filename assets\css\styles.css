/*
  Site styles for the "Happi" themed multi-page demo
  - Colorful, playful look inspired by the provided long image
  - Responsive layout with a simple grid
*/

:root {
  --color-primary: #f4b83a; /* warm yellow */
  --color-primary-700: #cf941a; /* darker yellow for hovers */
  --color-teal: #34d0c6;
  --color-teal-700: #0d9488;
  --color-ink: #111111;
  --color-bg: #fff8e7;
  --color-sand: #ffe4a3;
  --radius-lg: 20px;
  --radius-md: 12px;
  --radius-sm: 8px;
  --shadow-1: 0 8px 24px rgba(0,0,0,.08);
  --shadow-2: 0 16px 40px rgba(0,0,0,.12);
}

/* CSS Reset (lightweight) */
*, *::before, *::after { box-sizing: border-box; }
html, body { height: 100%; }
body {
  margin: 0;
  font-family: Inter, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Noto Sans", "Apple Color Emoji", "Segoe UI Emoji";
  color: var(--color-ink);
  background: var(--color-bg);
}
img { max-width: 100%; display: block; }
a { color: inherit; text-decoration: none; }
button { font: inherit; }

/* Reveal on scroll */
.reveal { opacity: 0; transform: translateY(24px); transition: opacity .6s ease, transform .6s ease; }
.reveal.in-view { opacity: 1; transform: translateY(0); }

/* Utilities */
.container { width: min(1100px, 92vw); margin: 0 auto; }
.btn {
  display: inline-flex; align-items: center; justify-content: center;
  padding: 12px 20px; border-radius: 999px; border: 0; cursor: pointer;
  background: var(--color-ink); color: white; transition: transform .08s ease, background .2s ease;
}
.btn:hover { transform: translateY(-1px); }
.btn-primary { background: var(--color-primary); color: #111; box-shadow: var(--shadow-1); }
.btn-primary:hover { background: var(--color-primary-700); }
.badge {
  display: inline-flex; align-items: center; gap: 8px; border-radius: 999px; padding: 8px 14px; font-weight: 600;
  background: #000; color: #fff; font-size: 14px;
}
.grid { display: grid; gap: 24px; }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-2 { grid-template-columns: repeat(2, 1fr); }
@media (max-width: 900px) { .grid-3, .grid-2 { grid-template-columns: 1fr; } }

/* Top announcement bar */
.topbar {
  background: #000; color: #fff; font-size: 13px; padding: 8px 0;
}
.topbar .container { display: flex; justify-content: center; align-items: center; gap: 8px; }

/* Header / Navigation */
header {
  position: sticky; top: 0; z-index: 10; background: var(--color-primary);
  border-bottom: 2px solid rgba(0,0,0,.08);
}
.nav { display: flex; align-items: center; justify-content: space-between; padding: 16px 0; }
.brand { display: flex; align-items: center; gap: 10px; font-weight: 800; letter-spacing: 1px; }
.brand-mark {
  width: 40px; height: 40px; border-radius: 10px; background: #000; color: #fff; display: grid; place-items: center;
  font-family: "Bangers", system-ui; font-size: 22px; transform: rotate(-6deg);
}
.nav ul { list-style: none; display: flex; gap: 18px; padding: 0; margin: 0; }
.nav a { font-weight: 700; }
.nav a:hover { text-decoration: underline; }

/* Hero */
.hero {
  position: relative;
  background: linear-gradient(180deg, var(--color-teal) 0%, #9deedd 52%, var(--color-primary) 52%, var(--color-primary) 100%);
}
.hero-inner { display: grid; grid-template-columns: 1.1fr .9fr; gap: 40px; align-items: center; padding: 48px 0 56px; }
.hero h1 {
  font-family: "Bangers", system-ui; font-size: clamp(44px, 6vw, 76px); line-height: .95; margin: 0 0 16px 0;
}
.hero p { font-size: 18px; }
.hero-card {
  background: #fff; border-radius: var(--radius-lg); box-shadow: var(--shadow-1); padding: 18px; transform: rotate(-6deg);
}
.hero-blob {
  aspect-ratio: 1 / 1; border-radius: 32% 68% 65% 35% / 46% 35% 65% 54%; background: #222; width: 100%;
}

/* Wave divider at bottom of hero */
.hero::after {
  content: ""; position: absolute; left: 0; right: 0; bottom: -1px; height: 60px;
  background: radial-gradient(35px 28px at 20px 30px, transparent 62%, var(--color-primary) 63%) repeat-x;
  background-size: 60px 60px; filter: drop-shadow(0 -2px 0 rgba(0,0,0,.05));
}

/* Section Blocks */
.section { padding: 56px 0; }
.section-title {
  font-family: "Bangers", system-ui; font-size: clamp(34px, 5vw, 56px); margin: 0 0 8px 0; letter-spacing: 1px;
}
.section-sub { color: rgba(0,0,0,.65); margin-bottom: 28px; }

/* Product cards */
.product-card { background: #fff; border-radius: var(--radius-lg); padding: 20px; box-shadow: var(--shadow-1); display: grid; gap: 14px; }
.product-art { height: 160px; border-radius: var(--radius-md); background: linear-gradient(135deg, #2dd4bf, #14b8a6); }
.product-title { font-weight: 800; font-size: 20px; }
.price { font-weight: 800; }

/* Feature pills */
.feature {
  background: var(--color-sand); border: 2px dashed #111; padding: 18px; border-radius: var(--radius-lg);
}

/* Newsletter */
.newsletter {
  background: #fff; border-radius: var(--radius-lg); padding: 24px; box-shadow: var(--shadow-1);
}
.newsletter form { display: grid; gap: 12px; grid-template-columns: 1fr auto; }
.newsletter input[type="email"] {
  padding: 12px 14px; border-radius: 999px; border: 2px solid #000; outline: none;
}
@media (max-width: 620px) { .newsletter form { grid-template-columns: 1fr; } }

/* Footer */
footer { padding: 28px 0 56px; color: rgba(0,0,0,.7); }
footer .links { display: flex; gap: 16px; flex-wrap: wrap; }

/* Page banners */
.page-banner { background: var(--color-primary); padding: 36px 0; border-bottom: 2px solid rgba(0,0,0,.08); }
.page-banner h2 {
  font-family: "Bangers", system-ui; font-size: clamp(32px, 4.6vw, 60px); margin: 0;
}

/* Collage header (top colorful faces mock) */
.collage {
  height: clamp(180px, 26vw, 300px);
  background:
   radial-gradient(60px 60px at 10% 30%, #ff7b54 0 60%, transparent 61%),
   radial-gradient(70px 70px at 24% 55%, #111 0 60%, transparent 61%),
   radial-gradient(56px 56px at 30% 20%, #f97316 0 60%, transparent 61%),
   radial-gradient(72px 72px at 50% 40%, #0ea5e9 0 60%, transparent 61%),
   radial-gradient(54px 54px at 64% 20%, #fbbf24 0 60%, transparent 61%),
   radial-gradient(70px 70px at 78% 60%, #ef4444 0 60%, transparent 61%),
   radial-gradient(64px 64px at 90% 26%, #34d399 0 60%, transparent 61%),
   var(--color-teal);
}

/* Floating product box stack */
.box-stack { position: relative; height: 320px; perspective: 900px; }
.choco-box {
  position: absolute; inset: auto auto 0 auto; left: 0; right: 0; margin: 0 auto;
  width: min(360px, 72%); height: 210px; border-radius: 16px; background: #1f2937; color: #fff;
  transform-style: preserve-3d; box-shadow: var(--shadow-2);
  display: grid; place-items: center; font-family: "Bangers", system-ui; font-size: 40px; letter-spacing: 1px;
}
.choco-box::before { content: "HAPPI"; position: absolute; top: 20px; left: 24px; font-size: 42px; }
.choco-box--1 { background: #0ea5e9; transform: rotateZ(-8deg) rotateX(8deg) translateY(0); animation: floatY 6s ease-in-out infinite; }
.choco-box--2 { background: #22c55e; transform: translateY(-30px) rotateZ(6deg) rotateX(10deg); animation: floatY 7s ease-in-out -1s infinite; }
.choco-box--3 { background: #f59e0b; transform: translateY(-60px) rotateZ(-3deg) rotateX(12deg); animation: floatY 8s ease-in-out -2s infinite; }

@keyframes floatY {
  0%,100% { transform: translateY(var(--y,0)) rotateZ(var(--rz,0deg)) rotateX(var(--rx,10deg)); }
  50% { transform: translateY(calc(var(--y,0) - 14px)) rotateZ(calc(var(--rz,0deg) + .6deg)) rotateX(calc(var(--rx,10deg) + .6deg)); }
}

/* Stickers row */
.stickers { display: flex; gap: 12px; flex-wrap: wrap; align-items: center; }
.sticker {
  border: 3px solid #000; border-radius: 999px; padding: 8px 14px; background: #fff; font-weight: 800; transform: rotate(var(--r, -4deg));
  animation: pop 3s ease-in-out infinite; box-shadow: 2px 4px 0 rgba(0,0,0,.2);
}
.sticker:nth-child(2) { --r: 3deg; animation-delay: .2s; }
.sticker:nth-child(3) { --r: -6deg; animation-delay: .4s; }
.sticker:nth-child(4) { --r: 5deg; animation-delay: .6s; }
@keyframes pop { 0%,100% { transform: translateY(0) rotate(var(--r)); } 50% { transform: translateY(-6px) rotate(calc(var(--r) + 2deg)); } }

/* OOO headline */
.ooo-block { background: var(--color-primary); padding: 60px 0 80px; position: relative; }
.ooo-text { font-family: "Bangers", system-ui; font-size: clamp(38px, 6vw, 86px); line-height: .9; }
.ooo-row { display: flex; gap: 10px; align-items: center; flex-wrap: wrap; }
.ooo { display: inline-grid; place-items: center; width: 68px; height: 68px; border-radius: 50%; background: #ff7b54; border: 3px solid #000; box-shadow: 3px 6px 0 rgba(0,0,0,.2); animation: ooo 2.6s ease-in-out infinite; }
.ooo:nth-child(odd) { background: #34d399; animation-delay: .2s; }
.ooo:nth-child(3n) { background: #60a5fa; animation-delay: .4s; }
@keyframes ooo { 0%,100% { transform: translateY(0) rotate(-3deg); } 50% { transform: translateY(-10px) rotate(3deg); } }

/* Dots / sprinkles */
.sprinkles { position: relative; }
.sprinkles::after {
  content: ""; position: absolute; inset: -20px -10px auto auto; width: 160px; height: 120px;
  background:
   radial-gradient(5px 5px at 10% 20%, #000 98%, transparent 99%),
   radial-gradient(5px 5px at 30% 70%, #000 98%, transparent 99%),
   radial-gradient(5px 5px at 60% 40%, #000 98%, transparent 99%),
   radial-gradient(5px 5px at 80% 75%, #000 98%, transparent 99%),
   radial-gradient(5px 5px at 50% 10%, #000 98%, transparent 99%);
  opacity: .2; filter: blur(.2px);
}





/* Blankify: hide text while preserving layout (applies only when body has class "blankify") */
body.blankify main p,
body.blankify main h1,
body.blankify main h2,
body.blankify main h3,
body.blankify main h4,
body.blankify main h5,
body.blankify main h6,
body.blankify main .section-title,
body.blankify main .section-sub,
body.blankify main .product-title,
body.blankify main .price,
body.blankify main .badge,
body.blankify main .ooo-text,
body.blankify main .sticker,
body.blankify main strong,
body.blankify .page-banner h2,
body.blankify .topbar .container {
  color: transparent !important;
  text-shadow: none !important;
}

/* Keep layout but hide button labels in content area */
body.blankify main .btn { color: transparent !important; }

/* Hide placeholders inside main forms while keeping input size */
body.blankify main input::placeholder { color: transparent; }

/* 购物车按钮样式 */
.btn-cart {
  position: relative;
  background: var(--color-teal);
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background 0.2s ease;
}

.btn-cart:hover {
  background: var(--color-teal-700);
}

.cart-count {
  background: var(--color-primary);
  color: var(--color-ink);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal {
  background: white;
  padding: 30px;
  border-radius: var(--radius-lg);
  max-width: 400px;
  width: 90%;
  box-shadow: var(--shadow-2);
}

.modal h3 {
  margin-top: 0;
  color: var(--color-ink);
  text-align: center;
}

.modal input {
  width: 100%;
  padding: 12px;
  margin: 10px 0;
  border: 2px solid #e5e7eb;
  border-radius: var(--radius-sm);
  font-size: 16px;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.modal input:focus {
  outline: none;
  border-color: var(--color-primary);
}

.modal-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

/* 通知样式 */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: var(--radius-sm);
  color: white;
  font-weight: 600;
  z-index: 10000;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  box-shadow: var(--shadow-1);
}

.notification.show {
  transform: translateX(0);
}

.notification-success {
  background: #10b981;
}

.notification-error {
  background: #ef4444;
}

.notification-info {
  background: #3b82f6;
}

.notification-warning {
  background: #f59e0b;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav {
    flex-direction: column;
    gap: 20px;
  }

  .nav ul {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .hero-inner {
    flex-direction: column;
    text-align: center;
  }

  .grid-3 {
    grid-template-columns: 1fr;
  }

  .grid-2 {
    grid-template-columns: 1fr;
  }

  .modal {
    margin: 20px;
    max-width: none;
  }
}
