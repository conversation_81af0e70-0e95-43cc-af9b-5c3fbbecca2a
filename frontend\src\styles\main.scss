@import 'variables';

// CSS Reset
*, *::before, *::after {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  font-family: Inter, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Noto Sans", "Apple Color Emoji", "Segoe UI Emoji";
  color: var(--color-ink);
  background: var(--color-bg);
  line-height: 1.6;
}

img {
  max-width: 100%;
  display: block;
}

a {
  color: inherit;
  text-decoration: none;
}

button {
  font: inherit;
  cursor: pointer;
}

// 通用样式
.container {
  width: var(--container-width);
  margin: 0 auto;
  padding: 0 20px;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 20px;
  border-radius: 999px;
  border: 0;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
  text-decoration: none;
  
  &:hover {
    transform: translateY(-1px);
  }
  
  &.btn-primary {
    @include btn-primary;
  }
  
  &.btn-secondary {
    background: var(--color-ink);
    color: white;
    
    &:hover {
      background: #333;
    }
  }
  
  &.btn-outline {
    background: transparent;
    border: 2px solid var(--color-primary);
    color: var(--color-primary);
    
    &:hover {
      background: var(--color-primary);
      color: var(--color-ink);
    }
  }
}

.card {
  @include card-style;
}

// 网格系统
.grid {
  display: grid;
  gap: 24px;
  
  &.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
  
  &.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
  
  &.grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

// 动画
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.slide-enter-active, .slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(100%);
}

.slide-leave-to {
  transform: translateX(-100%);
}

// 滚动显示动画
.reveal {
  opacity: 0;
  transform: translateY(24px);
  transition: opacity 0.6s ease, transform 0.6s ease;
  
  &.in-view {
    opacity: 1;
    transform: translateY(0);
  }
}

// Element Plus 主题定制
.el-button--primary {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--color-ink);
  
  &:hover {
    background-color: var(--color-primary-700);
    border-color: var(--color-primary-700);
  }
}

.el-input__wrapper {
  border-radius: var(--radius-sm);
}

.el-card {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-1);
}

// 响应式设计
@include respond-to('mobile') {
  .container {
    padding: 0 16px;
  }
  
  .grid {
    gap: 16px;
    
    &.grid-2,
    &.grid-3,
    &.grid-4 {
      grid-template-columns: 1fr;
    }
  }
  
  .btn {
    padding: 10px 16px;
    font-size: 14px;
  }
}
