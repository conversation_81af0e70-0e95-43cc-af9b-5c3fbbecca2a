import { productsAPI } from '@/utils/api'

const state = {
  products: [],
  featuredProducts: [],
  currentProduct: null,
  categories: ['黑巧克力', '牛奶巧克力', '白巧克力', '坚果巧克力'],
  loading: false,
  pagination: {
    page: 1,
    pageSize: 12,
    total: 0,
    totalPages: 0
  }
}

const mutations = {
  SET_PRODUCTS(state, products) {
    state.products = products
  },
  
  SET_FEATURED_PRODUCTS(state, products) {
    state.featuredProducts = products
  },
  
  SET_CURRENT_PRODUCT(state, product) {
    state.currentProduct = product
  },
  
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  
  SET_PAGINATION(state, pagination) {
    state.pagination = { ...state.pagination, ...pagination }
  },
  
  ADD_PRODUCT(state, product) {
    state.products.unshift(product)
  },
  
  UPDATE_PRODUCT(state, updatedProduct) {
    const index = state.products.findIndex(p => p.id === updatedProduct.id)
    if (index !== -1) {
      state.products.splice(index, 1, updatedProduct)
    }
  },
  
  REMOVE_PRODUCT(state, productId) {
    state.products = state.products.filter(p => p.id !== productId)
  }
}

const actions = {
  // 加载产品列表
  async loadProducts({ commit }, params = {}) {
    commit('SET_LOADING', true)
    try {
      const response = await productsAPI.getProducts(params)
      const { data, page, page_size, total, total_pages } = response.data.data
      
      commit('SET_PRODUCTS', data)
      commit('SET_PAGINATION', {
        page,
        pageSize: page_size,
        total,
        totalPages: total_pages
      })
      
      return { success: true, data: response.data.data }
    } catch (error) {
      throw new Error(error.response?.data?.message || '加载产品失败')
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 加载热门产品
  async loadFeaturedProducts({ commit }) {
    try {
      const response = await productsAPI.getProducts({ page: 1, page_size: 8 })
      commit('SET_FEATURED_PRODUCTS', response.data.data.data)
      return { success: true }
    } catch (error) {
      console.error('Load featured products error:', error)
      // 设置默认产品数据
      commit('SET_FEATURED_PRODUCTS', [
        {
          id: 1,
          name: '经典黑巧克力',
          description: '70%可可含量的经典黑巧克力',
          price: 28.00,
          category: '黑巧克力',
          image_url: '',
          stock_quantity: 100,
          is_available: true
        },
        {
          id: 2,
          name: '丝滑牛奶巧克力',
          description: '香甜丝滑的牛奶巧克力',
          price: 25.00,
          category: '牛奶巧克力',
          image_url: '',
          stock_quantity: 150,
          is_available: true
        }
      ])
    }
  },
  
  // 加载单个产品
  async loadProduct({ commit }, productId) {
    commit('SET_LOADING', true)
    try {
      const response = await productsAPI.getProduct(productId)
      commit('SET_CURRENT_PRODUCT', response.data.data)
      return { success: true, data: response.data.data }
    } catch (error) {
      throw new Error(error.response?.data?.message || '加载产品详情失败')
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 按分类加载产品
  async loadProductsByCategory({ commit }, { category, params = {} }) {
    commit('SET_LOADING', true)
    try {
      const response = await productsAPI.getProductsByCategory(category, params)
      const { data, page, page_size, total, total_pages } = response.data.data
      
      commit('SET_PRODUCTS', data)
      commit('SET_PAGINATION', {
        page,
        pageSize: page_size,
        total,
        totalPages: total_pages
      })
      
      return { success: true, data: response.data.data }
    } catch (error) {
      throw new Error(error.response?.data?.message || '加载分类产品失败')
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 搜索产品
  async searchProducts({ commit }, params) {
    commit('SET_LOADING', true)
    try {
      const response = await productsAPI.searchProducts(params)
      const { data, page, page_size, total, total_pages } = response.data.data
      
      commit('SET_PRODUCTS', data)
      commit('SET_PAGINATION', {
        page,
        pageSize: page_size,
        total,
        totalPages: total_pages
      })
      
      return { success: true, data: response.data.data }
    } catch (error) {
      throw new Error(error.response?.data?.message || '搜索产品失败')
    } finally {
      commit('SET_LOADING', false)
    }
  }
}

const getters = {
  products: state => state.products,
  featuredProducts: state => state.featuredProducts,
  currentProduct: state => state.currentProduct,
  categories: state => state.categories,
  loading: state => state.loading,
  pagination: state => state.pagination,
  
  // 按分类获取产品
  productsByCategory: state => category => {
    return state.products.filter(product => product.category === category)
  },
  
  // 获取可用产品
  availableProducts: state => {
    return state.products.filter(product => product.is_available && product.stock_quantity > 0)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
