{"version": 3, "file": "panel-date-pick.js", "sources": ["../../../../../../../packages/components/date-picker-panel/src/props/panel-date-pick.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { panelSharedProps } from './shared'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type { DayOrDays } from '@element-plus/components/time-picker'\n\nexport const panelDatePickProps = buildProps({\n  ...panelSharedProps,\n  parsedValue: {\n    type: definePropType<DayOrDays>([Object, Array]),\n  },\n  visible: {\n    type: Boolean,\n    default: true,\n  },\n  format: {\n    type: String,\n    default: '',\n  },\n} as const)\n\nexport type PanelDatePickProps = ExtractPropTypes<typeof panelDatePickProps>\nexport type PanelDatePickPropsPublic = __ExtractPublicPropTypes<\n  typeof panelDatePickProps\n>\n"], "names": ["buildProps", "panelSharedProps", "definePropType"], "mappings": ";;;;;;;AAEY,MAAC,kBAAkB,GAAGA,kBAAU,CAAC;AAC7C,EAAE,GAAGC,uBAAgB;AACrB,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAEC,sBAAc,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACzC,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,CAAC;;;;"}