<template>
  <div class="orders-page">
    <div class="container">
      <div class="page-header">
        <h1>我的订单</h1>
        <p>查看您的订单历史和状态</p>
      </div>
      
      <div v-loading="loading" class="orders-content">
        <div v-if="orders.length === 0 && !loading" class="empty-orders">
          <el-icon size="80" color="#ccc">
            <Document />
          </el-icon>
          <h2>暂无订单</h2>
          <p>您还没有任何订单，快去购买您喜欢的巧克力吧！</p>
          <router-link to="/products" class="btn btn-primary">去购物</router-link>
        </div>
        
        <div v-else class="orders-list">
          <div
            v-for="order in orders"
            :key="order.id"
            class="order-card"
            @click="goToOrderDetail(order.id)"
          >
            <div class="order-header">
              <div class="order-info">
                <h3>订单号: {{ order.id }}</h3>
                <p class="order-date">{{ formatDate(order.created_at) }}</p>
              </div>
              <div class="order-status">
                <el-tag :type="getStatusType(order.status)">
                  {{ getStatusText(order.status) }}
                </el-tag>
              </div>
            </div>
            
            <div class="order-details">
              <div class="order-amount">
                <span class="label">订单金额:</span>
                <span class="amount">¥{{ order.total_amount.toFixed(2) }}</span>
              </div>
              <div class="order-payment">
                <span class="label">支付方式:</span>
                <span>{{ getPaymentMethodText(order.payment_method) }}</span>
              </div>
              <div class="order-address">
                <span class="label">配送地址:</span>
                <span>{{ order.shipping_address }}</span>
              </div>
            </div>
            
            <div class="order-actions">
              <el-button size="small" @click.stop="goToOrderDetail(order.id)">
                查看详情
              </el-button>
              <el-button
                v-if="order.status === 'pending'"
                type="danger"
                size="small"
                @click.stop="cancelOrder(order.id)"
              >
                取消订单
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 分页 -->
        <div v-if="pagination.totalPages > 1" class="pagination">
          <el-pagination
            v-model:current-page="pagination.page"
            :page-size="pagination.pageSize"
            :total="pagination.total"
            layout="prev, pager, next"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { Document } from '@element-plus/icons-vue'

export default {
  name: 'Orders',
  components: {
    Document
  },
  setup() {
    const store = useStore()
    const router = useRouter()
    
    const orders = computed(() => store.getters['orders/orders'])
    const loading = computed(() => store.getters['orders/loading'])
    const pagination = computed(() => store.getters['orders/pagination'])
    
    const loadOrders = async () => {
      try {
        await store.dispatch('orders/loadOrders', {
          page: pagination.value.page,
          page_size: pagination.value.pageSize
        })
      } catch (error) {
        store.dispatch('notifications/showNotification', {
          type: 'error',
          message: error.message
        })
      }
    }
    
    const goToOrderDetail = (orderId) => {
      router.push(`/orders/${orderId}`)
    }
    
    const cancelOrder = async (orderId) => {
      try {
        await store.dispatch('orders/updateOrderStatus', {
          orderId,
          status: 'cancelled'
        })
        
        // 重新加载订单列表
        await loadOrders()
      } catch (error) {
        console.error('Cancel order error:', error)
      }
    }
    
    const handlePageChange = (page) => {
      store.commit('orders/SET_PAGINATION', { page })
      loadOrders()
    }
    
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleString('zh-CN')
    }
    
    const getStatusType = (status) => {
      const statusMap = {
        pending: '',
        confirmed: 'success',
        preparing: 'warning',
        shipping: 'info',
        delivered: 'success',
        cancelled: 'danger'
      }
      return statusMap[status] || ''
    }
    
    const getStatusText = (status) => {
      const statusMap = {
        pending: '待确认',
        confirmed: '已确认',
        preparing: '制作中',
        shipping: '配送中',
        delivered: '已送达',
        cancelled: '已取消'
      }
      return statusMap[status] || status
    }
    
    const getPaymentMethodText = (method) => {
      const methodMap = {
        alipay: '支付宝',
        wechat: '微信支付',
        credit_card: '信用卡'
      }
      return methodMap[method] || method
    }
    
    onMounted(() => {
      loadOrders()
    })
    
    return {
      orders,
      loading,
      pagination,
      goToOrderDetail,
      cancelOrder,
      handlePageChange,
      formatDate,
      getStatusType,
      getStatusText,
      getPaymentMethodText
    }
  }
}
</script>

<style lang="scss" scoped>
.orders-page {
  padding: 40px 0;
  
  .page-header {
    text-align: center;
    margin-bottom: 40px;
    
    h1 {
      font-size: 36px;
      font-weight: 800;
      color: var(--color-ink);
      margin-bottom: 8px;
    }
    
    p {
      font-size: 16px;
      color: #666;
    }
  }
  
  .orders-content {
    min-height: 400px;
  }
  
  .empty-orders {
    text-align: center;
    padding: 80px 20px;
    
    h2 {
      font-size: 24px;
      color: var(--color-ink);
      margin: 24px 0 16px 0;
    }
    
    p {
      font-size: 16px;
      color: #666;
      margin-bottom: 32px;
    }
  }
  
  .orders-list {
    .order-card {
      @include card-style;
      margin-bottom: 20px;
      cursor: pointer;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-2);
      }
      
      .order-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 16px;
        
        .order-info {
          h3 {
            font-size: 18px;
            font-weight: 600;
            color: var(--color-ink);
            margin: 0 0 4px 0;
          }
          
          .order-date {
            font-size: 14px;
            color: #666;
            margin: 0;
          }
        }
      }
      
      .order-details {
        margin-bottom: 16px;
        
        > div {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 4px 0;
          
          .label {
            font-weight: 600;
            color: var(--color-ink);
          }
          
          .amount {
            font-size: 18px;
            font-weight: 700;
            color: var(--color-primary);
          }
        }
      }
      
      .order-actions {
        display: flex;
        gap: 12px;
        justify-content: flex-end;
      }
    }
  }
  
  .pagination {
    display: flex;
    justify-content: center;
    margin-top: 40px;
  }
}

@include respond-to('mobile') {
  .orders-page {
    .orders-list {
      .order-card {
        .order-header {
          flex-direction: column;
          gap: 12px;
        }
        
        .order-actions {
          justify-content: stretch;
          
          .el-button {
            flex: 1;
          }
        }
      }
    }
  }
}
</style>
