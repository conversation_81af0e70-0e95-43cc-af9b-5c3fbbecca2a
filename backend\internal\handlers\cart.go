package handlers

import (
	"chocolate-shop/internal/models"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/jmoiron/sqlx"
)

type CartHandler struct {
	db  *sqlx.DB
	rdb *redis.Client
}

func NewCartHandler(db *sqlx.DB, rdb *redis.Client) *CartHandler {
	return &CartHandler{
		db:  db,
		rdb: rdb,
	}
}

// GetCart 获取购物车
func (h *CartHandler) GetCart(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "User not authenticated",
		})
		return
	}

	var cartItems []models.CartItem
	err := h.db.Select(&cartItems, `
		SELECT ci.*, p.name, p.description, p.price, p.category, p.image_url, p.stock_quantity, p.is_available
		FROM cart_items ci
		JOIN products p ON ci.product_id = p.id
		WHERE ci.user_id = $1 AND p.is_available = true
		ORDER BY ci.created_at DESC
	`, userID)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to retrieve cart",
		})
		return
	}

	// 填充产品信息
	for i := range cartItems {
		cartItems[i].Product = &models.Product{
			ID:            cartItems[i].ProductID,
			Name:          cartItems[i].Product.Name,
			Description:   cartItems[i].Product.Description,
			Price:         cartItems[i].Product.Price,
			Category:      cartItems[i].Product.Category,
			ImageURL:      cartItems[i].Product.ImageURL,
			StockQuantity: cartItems[i].Product.StockQuantity,
			IsAvailable:   cartItems[i].Product.IsAvailable,
		}
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Cart retrieved successfully",
		Data:    cartItems,
	})
}

// AddToCart 添加到购物车
func (h *CartHandler) AddToCart(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "User not authenticated",
		})
		return
	}

	var req models.AddToCartRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid request data: " + err.Error(),
		})
		return
	}

	// 检查产品是否存在且可用
	var product models.Product
	err := h.db.Get(&product, "SELECT * FROM products WHERE id = $1 AND is_available = true", req.ProductID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Message: "Product not found or not available",
		})
		return
	}

	// 检查库存
	if product.StockQuantity < req.Quantity {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Insufficient stock",
		})
		return
	}

	// 检查是否已在购物车中
	var existingItem models.CartItem
	err = h.db.Get(&existingItem, "SELECT * FROM cart_items WHERE user_id = $1 AND product_id = $2", userID, req.ProductID)
	
	if err == nil {
		// 更新数量
		newQuantity := existingItem.Quantity + req.Quantity
		if newQuantity > product.StockQuantity {
			c.JSON(http.StatusBadRequest, models.APIResponse{
				Success: false,
				Message: "Total quantity exceeds stock",
			})
			return
		}

		_, err = h.db.Exec(`
			UPDATE cart_items 
			SET quantity = $1, updated_at = CURRENT_TIMESTAMP 
			WHERE user_id = $2 AND product_id = $3
		`, newQuantity, userID, req.ProductID)
	} else {
		// 添加新项
		_, err = h.db.Exec(`
			INSERT INTO cart_items (user_id, product_id, quantity)
			VALUES ($1, $2, $3)
		`, userID, req.ProductID, req.Quantity)
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to add to cart",
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Product added to cart successfully",
	})
}

// UpdateCartItem 更新购物车项
func (h *CartHandler) UpdateCartItem(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "User not authenticated",
		})
		return
	}

	cartItemID := c.Param("id")
	
	var req struct {
		Quantity int `json:"quantity" binding:"required,min=1"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Invalid request data: " + err.Error(),
		})
		return
	}

	// 检查购物车项是否存在
	var cartItem models.CartItem
	err := h.db.Get(&cartItem, "SELECT * FROM cart_items WHERE id = $1 AND user_id = $2", cartItemID, userID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Message: "Cart item not found",
		})
		return
	}

	// 检查产品库存
	var product models.Product
	err = h.db.Get(&product, "SELECT stock_quantity FROM products WHERE id = $1 AND is_available = true", cartItem.ProductID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Message: "Product not found or not available",
		})
		return
	}

	if product.StockQuantity < req.Quantity {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Message: "Insufficient stock",
		})
		return
	}

	// 更新数量
	_, err = h.db.Exec(`
		UPDATE cart_items 
		SET quantity = $1, updated_at = CURRENT_TIMESTAMP 
		WHERE id = $2 AND user_id = $3
	`, req.Quantity, cartItemID, userID)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to update cart item",
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Cart item updated successfully",
	})
}

// RemoveFromCart 从购物车移除
func (h *CartHandler) RemoveFromCart(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "User not authenticated",
		})
		return
	}

	cartItemID := c.Param("id")

	result, err := h.db.Exec("DELETE FROM cart_items WHERE id = $1 AND user_id = $2", cartItemID, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to remove from cart",
		})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Success: false,
			Message: "Cart item not found",
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Item removed from cart successfully",
	})
}

// ClearCart 清空购物车
func (h *CartHandler) ClearCart(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Success: false,
			Message: "User not authenticated",
		})
		return
	}

	_, err := h.db.Exec("DELETE FROM cart_items WHERE user_id = $1", userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Message: "Failed to clear cart",
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: "Cart cleared successfully",
	})
}
