<template>
  <div class="contact-page">
    <div class="container">
      <div class="page-header">
        <h1>联系我们</h1>
        <p>我们很乐意听到您的声音</p>
      </div>
      
      <div class="contact-content">
        <div class="contact-info">
          <h2>联系方式</h2>
          <div class="info-item">
            <el-icon><Phone /></el-icon>
            <div>
              <h3>客服热线</h3>
              <p>************</p>
            </div>
          </div>
          <div class="info-item">
            <el-icon><Message /></el-icon>
            <div>
              <h3>邮箱</h3>
              <p><EMAIL></p>
            </div>
          </div>
          <div class="info-item">
            <el-icon><Location /></el-icon>
            <div>
              <h3>地址</h3>
              <p>上海市浦东新区巧克力大道123号</p>
            </div>
          </div>
        </div>
        
        <div class="contact-form">
          <h2>发送消息</h2>
          <el-form
            ref="contactFormRef"
            :model="contactForm"
            :rules="contactRules"
            @submit.prevent="handleSubmit"
          >
            <el-form-item prop="name">
              <el-input
                v-model="contactForm.name"
                placeholder="您的姓名"
                size="large"
              />
            </el-form-item>
            
            <el-form-item prop="email">
              <el-input
                v-model="contactForm.email"
                placeholder="您的邮箱"
                size="large"
              />
            </el-form-item>
            
            <el-form-item prop="subject">
              <el-input
                v-model="contactForm.subject"
                placeholder="主题"
                size="large"
              />
            </el-form-item>
            
            <el-form-item prop="message">
              <el-input
                v-model="contactForm.message"
                type="textarea"
                :rows="6"
                placeholder="您的消息"
              />
            </el-form-item>
            
            <el-form-item>
              <el-button
                type="primary"
                size="large"
                :loading="loading"
                @click="handleSubmit"
              >
                发送消息
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useStore } from 'vuex'
import { Phone, Message, Location } from '@element-plus/icons-vue'

export default {
  name: 'Contact',
  components: {
    Phone,
    Message,
    Location
  },
  setup() {
    const store = useStore()
    
    const contactFormRef = ref()
    const loading = ref(false)
    
    const contactForm = reactive({
      name: '',
      email: '',
      subject: '',
      message: ''
    })
    
    const contactRules = {
      name: [
        { required: true, message: '请输入您的姓名', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
      ],
      subject: [
        { required: true, message: '请输入主题', trigger: 'blur' }
      ],
      message: [
        { required: true, message: '请输入消息内容', trigger: 'blur' },
        { min: 10, message: '消息内容至少10个字符', trigger: 'blur' }
      ]
    }
    
    const handleSubmit = async () => {
      if (!contactFormRef.value) return
      
      try {
        await contactFormRef.value.validate()
        
        loading.value = true
        
        // 模拟发送消息
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        store.dispatch('notifications/showNotification', {
          type: 'success',
          message: '消息发送成功，我们会尽快回复您！'
        })
        
        // 重置表单
        contactFormRef.value.resetFields()
        
      } catch (error) {
        store.dispatch('notifications/showNotification', {
          type: 'error',
          message: '发送失败，请稍后重试'
        })
      } finally {
        loading.value = false
      }
    }
    
    return {
      contactFormRef,
      contactForm,
      contactRules,
      loading,
      handleSubmit
    }
  }
}
</script>

<style lang="scss" scoped>
.contact-page {
  padding: 40px 0;
  
  .page-header {
    text-align: center;
    margin-bottom: 60px;
    
    h1 {
      font-size: 36px;
      font-weight: 800;
      color: var(--color-ink);
      margin-bottom: 16px;
    }
    
    p {
      font-size: 18px;
      color: #666;
    }
  }
  
  .contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    max-width: 1000px;
    margin: 0 auto;
    
    .contact-info {
      h2 {
        font-size: 24px;
        font-weight: 700;
        color: var(--color-ink);
        margin-bottom: 32px;
      }
      
      .info-item {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        margin-bottom: 32px;
        
        .el-icon {
          font-size: 24px;
          color: var(--color-primary);
          margin-top: 4px;
        }
        
        h3 {
          font-size: 18px;
          font-weight: 600;
          color: var(--color-ink);
          margin: 0 0 4px 0;
        }
        
        p {
          font-size: 16px;
          color: #666;
          margin: 0;
        }
      }
    }
    
    .contact-form {
      @include card-style;
      
      h2 {
        font-size: 24px;
        font-weight: 700;
        color: var(--color-ink);
        margin-bottom: 32px;
        text-align: center;
      }
      
      .el-button {
        width: 100%;
      }
    }
  }
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-input__wrapper),
:deep(.el-textarea__inner) {
  border-radius: var(--radius-sm);
}

@include respond-to('mobile') {
  .contact-page {
    .contact-content {
      grid-template-columns: 1fr;
      gap: 40px;
    }
  }
}
</style>
