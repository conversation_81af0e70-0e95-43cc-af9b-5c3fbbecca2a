{"version": 3, "file": "color-picker2.js", "sources": ["../../../../../../packages/components/color-picker/src/color-picker.vue"], "sourcesContent": ["<template>\n  <el-tooltip\n    ref=\"popper\"\n    :visible=\"showPicker\"\n    :show-arrow=\"false\"\n    :fallback-placements=\"['bottom', 'top', 'right', 'left']\"\n    :offset=\"0\"\n    :gpu-acceleration=\"false\"\n    :popper-class=\"[ns.be('picker', 'panel'), popperClass]\"\n    :stop-popper-mouse-event=\"false\"\n    pure\n    effect=\"light\"\n    trigger=\"click\"\n    :teleported=\"teleported\"\n    :transition=\"`${ns.namespace.value}-zoom-in-top`\"\n    :persistent=\"persistent\"\n    :append-to=\"appendTo\"\n    @hide=\"setShowPicker(false)\"\n  >\n    <template #content>\n      <el-color-picker-panel\n        ref=\"pickerPanelRef\"\n        v-bind=\"panelProps\"\n        v-click-outside:[triggerRef]=\"handleClickOutside\"\n        :border=\"false\"\n        @keydown.esc=\"handleEsc\"\n      >\n        <template #footer>\n          <div>\n            <el-button\n              :class=\"ns.be('footer', 'link-btn')\"\n              text\n              size=\"small\"\n              @click=\"clear\"\n            >\n              {{ t('el.colorpicker.clear') }}\n            </el-button>\n            <el-button\n              plain\n              size=\"small\"\n              :class=\"ns.be('footer', 'btn')\"\n              @click=\"confirmValue\"\n            >\n              {{ t('el.colorpicker.confirm') }}\n            </el-button>\n          </div>\n        </template>\n      </el-color-picker-panel>\n    </template>\n    <template #default>\n      <div\n        :id=\"buttonId\"\n        ref=\"triggerRef\"\n        v-bind=\"$attrs\"\n        :class=\"btnKls\"\n        role=\"button\"\n        :aria-label=\"buttonAriaLabel\"\n        :aria-labelledby=\"buttonAriaLabelledby\"\n        :aria-description=\"\n          t('el.colorpicker.description', { color: modelValue || '' })\n        \"\n        :aria-disabled=\"colorDisabled\"\n        :tabindex=\"colorDisabled ? undefined : tabindex\"\n        @keydown=\"handleKeyDown\"\n        @focus=\"handleFocus\"\n        @blur=\"handleBlur\"\n      >\n        <div :class=\"ns.be('picker', 'trigger')\" @click=\"handleTrigger\">\n          <span :class=\"[ns.be('picker', 'color'), ns.is('alpha', showAlpha)]\">\n            <span\n              :class=\"ns.be('picker', 'color-inner')\"\n              :style=\"{\n                backgroundColor: displayedColor,\n              }\"\n            >\n              <el-icon\n                v-show=\"modelValue || showPanelColor\"\n                :class=\"[ns.be('picker', 'icon'), ns.is('icon-arrow-down')]\"\n              >\n                <arrow-down />\n              </el-icon>\n              <el-icon\n                v-show=\"!modelValue && !showPanelColor\"\n                :class=\"[ns.be('picker', 'empty'), ns.is('icon-close')]\"\n              >\n                <close />\n              </el-icon>\n            </span>\n          </span>\n        </div>\n      </div>\n    </template>\n  </el-tooltip>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, nextTick, provide, ref, watch } from 'vue'\nimport { debounce, pick } from 'lodash-unified'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { reactiveComputed } from '@vueuse/core'\nimport { ClickOutside as vClickOutside } from '@element-plus/directives'\nimport { ElTooltip } from '@element-plus/components/tooltip'\nimport { ElButton } from '@element-plus/components/button'\nimport {\n  useFormDisabled,\n  useFormItem,\n  useFormItemInputId,\n  useFormSize,\n} from '@element-plus/components/form'\nimport {\n  useEmptyValues,\n  useFocusController,\n  useLocale,\n  useNamespace,\n} from '@element-plus/hooks'\nimport {\n  CHANGE_EVENT,\n  EVENT_CODE,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { debugWarn } from '@element-plus/utils'\nimport { ArrowDown, Close } from '@element-plus/icons-vue'\nimport { colorPickerEmits, colorPickerProps } from './color-picker'\nimport {\n  ElColorPickerPanel,\n  ROOT_COMMON_COLOR_INJECTION_KEY,\n  colorPickerPanelProps,\n} from '@element-plus/components/color-picker-panel'\nimport Color from '@element-plus/components/color-picker-panel/src/utils/color'\nimport { useCommonColor } from '@element-plus/components/color-picker-panel/src/composables/use-common-color'\n\nimport type { ColorPickerPanelInstance } from '@element-plus/components/color-picker-panel'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\n\ndefineOptions({\n  name: 'ElColorPicker',\n})\nconst props = defineProps(colorPickerProps)\n\nconst emit = defineEmits(colorPickerEmits)\n\nconst { t } = useLocale()\nconst ns = useNamespace('color')\nconst { formItem } = useFormItem()\nconst colorSize = useFormSize()\nconst colorDisabled = useFormDisabled()\nconst { valueOnClear, isEmptyValue } = useEmptyValues(props, null)\nconst commonColor = useCommonColor(props, emit)\nconst { inputId: buttonId, isLabeledByFormItem } = useFormItemInputId(props, {\n  formItemContext: formItem,\n})\n\nconst popper = ref<TooltipInstance>()\nconst triggerRef = ref()\nconst pickerPanelRef = ref<ColorPickerPanelInstance>()\nconst showPicker = ref(false)\nconst showPanelColor = ref(false)\n\n// active-change is used to prevent modelValue changes from triggering.\nlet shouldActiveChange = true\n\nconst { isFocused, handleFocus, handleBlur } = useFocusController(triggerRef, {\n  disabled: colorDisabled,\n  beforeBlur(event) {\n    return popper.value?.isFocusInsideContent(event)\n  },\n  afterBlur() {\n    setShowPicker(false)\n    resetColor()\n  },\n})\n\nconst color = reactiveComputed(\n  () => pickerPanelRef.value?.color ?? commonColor.color\n) as Color\n\nconst panelProps = computed(() =>\n  pick(props, Object.keys(colorPickerPanelProps))\n)\n\nconst displayedColor = computed(() => {\n  if (!props.modelValue && !showPanelColor.value) {\n    return 'transparent'\n  }\n  return displayedRgb(color, props.showAlpha)\n})\n\nconst currentColor = computed(() => {\n  return !props.modelValue && !showPanelColor.value ? '' : color.value\n})\n\nconst buttonAriaLabel = computed<string | undefined>(() => {\n  return !isLabeledByFormItem.value\n    ? props.ariaLabel || t('el.colorpicker.defaultLabel')\n    : undefined\n})\n\nconst buttonAriaLabelledby = computed<string | undefined>(() => {\n  return isLabeledByFormItem.value ? formItem?.labelId : undefined\n})\n\nconst btnKls = computed(() => {\n  return [\n    ns.b('picker'),\n    ns.is('disabled', colorDisabled.value),\n    ns.bm('picker', colorSize.value),\n    ns.is('focused', isFocused.value),\n  ]\n})\n\nfunction displayedRgb(color: Color, showAlpha: boolean) {\n  const { r, g, b, a } = color.toRgb()\n  return showAlpha ? `rgba(${r}, ${g}, ${b}, ${a})` : `rgb(${r}, ${g}, ${b})`\n}\n\nfunction setShowPicker(value: boolean) {\n  showPicker.value = value\n}\n\nconst debounceSetShowPicker = debounce(setShowPicker, 100, { leading: true })\nfunction show() {\n  if (colorDisabled.value) return\n  setShowPicker(true)\n}\n\nfunction hide() {\n  debounceSetShowPicker(false)\n  resetColor()\n}\n\nfunction resetColor() {\n  nextTick(() => {\n    if (props.modelValue) {\n      color.fromString(props.modelValue)\n    } else {\n      color.value = ''\n      nextTick(() => {\n        showPanelColor.value = false\n      })\n    }\n  })\n}\n\nfunction handleTrigger() {\n  if (colorDisabled.value) return\n  if (showPicker.value) {\n    resetColor()\n  }\n  debounceSetShowPicker(!showPicker.value)\n}\n\nfunction confirmValue() {\n  const value = isEmptyValue(color.value) ? valueOnClear.value : color.value\n  emit(UPDATE_MODEL_EVENT, value)\n  emit(CHANGE_EVENT, value)\n  if (props.validateEvent) {\n    formItem?.validate('change').catch((err) => debugWarn(err))\n  }\n  debounceSetShowPicker(false)\n  // check if modelValue change, if not change, then reset color.\n  nextTick(() => {\n    const newColor = new Color({\n      enableAlpha: props.showAlpha,\n      format: props.colorFormat || '',\n      value: props.modelValue,\n    })\n    if (!color.compare(newColor)) {\n      resetColor()\n    }\n  })\n}\n\nfunction clear() {\n  debounceSetShowPicker(false)\n  emit(UPDATE_MODEL_EVENT, valueOnClear.value)\n  emit(CHANGE_EVENT, valueOnClear.value)\n  if (props.modelValue !== valueOnClear.value && props.validateEvent) {\n    formItem?.validate('change').catch((err) => debugWarn(err))\n  }\n  resetColor()\n}\n\nfunction handleClickOutside() {\n  if (!showPicker.value) return\n  hide()\n  isFocused.value && focus()\n}\n\nfunction handleEsc(event: KeyboardEvent) {\n  event.preventDefault()\n  event.stopPropagation()\n  setShowPicker(false)\n  resetColor()\n}\n\nfunction handleKeyDown(event: KeyboardEvent) {\n  switch (event.code) {\n    case EVENT_CODE.enter:\n    case EVENT_CODE.numpadEnter:\n    case EVENT_CODE.space:\n      event.preventDefault()\n      event.stopPropagation()\n      show()\n      pickerPanelRef?.value?.inputRef?.focus()\n      break\n    case EVENT_CODE.esc:\n      handleEsc(event)\n      break\n  }\n}\n\nfunction focus() {\n  triggerRef.value.focus()\n}\n\nfunction blur() {\n  triggerRef.value.blur()\n}\n\nwatch(\n  () => currentColor.value,\n  (val) => {\n    shouldActiveChange && emit('activeChange', val)\n    shouldActiveChange = true\n  }\n)\n\nwatch(\n  () => color.value,\n  () => {\n    if (!props.modelValue && !showPanelColor.value) {\n      showPanelColor.value = true\n    }\n  }\n)\n\nwatch(\n  () => props.modelValue,\n  (newVal) => {\n    if (!newVal) {\n      showPanelColor.value = false\n    } else if (newVal && newVal !== color.value) {\n      shouldActiveChange = false\n      color.fromString(newVal)\n    }\n  }\n)\n\nprovide(ROOT_COMMON_COLOR_INJECTION_KEY, commonColor)\n\ndefineExpose({\n  /**\n   * @description current color object\n   */\n  color,\n  /**\n   * @description manually show ColorPicker\n   */\n  show,\n  /**\n   * @description manually hide ColorPicker\n   */\n  hide,\n  /**\n   * @description focus the input element\n   */\n  focus,\n  /**\n   * @description blur the input element\n   */\n  blur,\n})\n</script>\n"], "names": ["useLocale", "useNamespace", "useFormItem", "useFormSize", "useFormDisabled", "useEmptyValues", "useCommonColor", "useFormItemInputId", "ref", "useFocusController", "color", "reactiveComputed", "computed", "pick", "colorPickerPanelProps", "debounce", "nextTick", "CHANGE_EVENT", "debugWarn", "Color", "UPDATE_MODEL_EVENT", "EVENT_CODE", "watch", "provide", "ROOT_COMMON_COLOR_INJECTION_KEY", "_openBlock", "_createBlock", "_unref", "ElTooltip"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAsIc,CAAA;AAAA,EACZ,IAAM,EAAA,eAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAE,CAAE,EAAA,GAAIA,eAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAKC,qBAAa,OAAO,CAAA,CAAA;AAC/B,IAAM,MAAA,EAAE,QAAS,EAAA,GAAIC,uBAAY,EAAA,CAAA;AACjC,IAAA,MAAM,YAAYC,8BAAY,EAAA,CAAA;AAC9B,IAAA,MAAM,gBAAgBC,kCAAgB,EAAA,CAAA;AACtC,IAAA,MAAM,EAAE,YAAc,EAAA,YAAA,EAAiB,GAAAC,sBAAA,CAAe,OAAO,IAAI,CAAA,CAAA;AACjE,IAAM,MAAA,WAAA,GAAcC,6BAAe,CAAA,KAAA,EAAO,IAAI,CAAA,CAAA;AAC9C,IAAA,MAAM,EAAE,OAAS,EAAA,QAAA,EAAU,mBAAoB,EAAA,GAAIC,+BAAmB,KAAO,EAAA;AAAA,MAC3E,eAAiB,EAAA,QAAA;AAAA,KAClB,CAAA,CAAA;AAED,IAAA,MAAM,SAASC,OAAqB,EAAA,CAAA;AACpC,IAAA,MAAM,aAAaA,OAAI,EAAA,CAAA;AACvB,IAAA,MAAM,iBAAiBA,OAA8B,EAAA,CAAA;AACrD,IAAM,MAAA,UAAA,GAAaA,QAAI,KAAK,CAAA,CAAA;AAC5B,IAAM,MAAA,cAAA,GAAiBA,QAAI,KAAK,CAAA,CAAA;AAGhC,IAAA,IAAI,kBAAqB,GAAA,IAAA,CAAA;AAEzB,IAAA,MAAM,EAAE,SAAW,EAAA,WAAA,EAAa,UAAW,EAAA,GAAIC,2BAAmB,UAAY,EAAA;AAAA,MAC5E,QAAU,EAAA,aAAA;AAAA,MACV,WAAW,KAAO,EAAA;AAChB,QAAO,IAAA,EAAA,CAAA;AAAwC,QACjD,OAAA,CAAA,EAAA,GAAA,MAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OACY;AACV,MAAA,SAAA,GAAA;AACA,QAAW,aAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QACb,UAAA,EAAA,CAAA;AAAA,OACD;AAED,KAAA,CAAA,CAAA;AAAc,IAAA,MACNC,OAAA,GAAAC,qBAAsB,CAAA,MAAA;AAAqB,MACnD,IAAA,EAAA,EAAA,EAAA,CAAA;AAEA,MAAA,OAAmB,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,cAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,KAAA,IAAA,GAAA,EAAA,GAAA,WAAA,CAAA,KAAA,CAAA;AAAA,KAAA,CAAS;AACoB,IAChD,MAAA,UAAA,GAAAC,YAAA,CAAA,MAAAC,kBAAA,CAAA,KAAA,EAAA,MAAA,CAAA,IAAA,CAAAC,sCAAA,CAAA,CAAA,CAAA,CAAA;AAEA,IAAM,MAAA,cAAA,GAAiBF,aAAS,MAAM;AACpC,MAAA,IAAI,CAAC,KAAA,CAAM,UAAc,IAAA,CAAC,eAAe,KAAO,EAAA;AAC9C,QAAO,OAAA,aAAA,CAAA;AAAA,OACT;AACA,MAAO,OAAA,YAAA,CAAaF,OAAO,EAAA,KAAA,CAAM,SAAS,CAAA,CAAA;AAAA,KAC3C,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAeE,aAAS,MAAM;AAClC,MAAA,OAAO,CAAC,KAAM,CAAA,UAAA,IAAc,CAAC,cAAe,CAAA,KAAA,GAAQ,KAAKF,OAAM,CAAA,KAAA,CAAA;AAAA,KAChE,CAAA,CAAA;AAED,IAAM,MAAA,eAAA,GAAkBE,aAA6B,MAAM;AACzD,MAAA,OAAO,CAAC,mBAAoB,CAAA,KAAA,GACxB,MAAM,SAAa,IAAA,CAAA,CAAE,6BAA6B,CAClD,GAAA,KAAA,CAAA,CAAA;AAAA,KACL,CAAA,CAAA;AAED,IAAM,MAAA,oBAAA,GAAuBA,aAA6B,MAAM;AAC9D,MAAO,OAAA,mBAAA,CAAoB,KAAQ,GAAA,QAAA,IAAoB,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,OAAA,GAAA,KAAA,CAAA,CAAA;AAAA,KACxD,CAAA,CAAA;AAED,IAAM,MAAA,MAAA,GAASA,aAAS,MAAM;AAC5B,MAAO,OAAA;AAAA,QACL,EAAA,CAAG,EAAE,QAAQ,CAAA;AAAA,QACb,EAAG,CAAA,EAAA,CAAG,UAAY,EAAA,aAAA,CAAc,KAAK,CAAA;AAAA,QACrC,EAAG,CAAA,EAAA,CAAG,QAAU,EAAA,SAAA,CAAU,KAAK,CAAA;AAAA,QAC/B,EAAG,CAAA,EAAA,CAAG,SAAW,EAAA,SAAA,CAAU,KAAK,CAAA;AAAA,OAClC,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAS,SAAA,YAAA,CAAaF,QAAc,SAAoB,EAAA;AACtD,MAAA,MAAM,EAAE,CAAG,EAAA,CAAA,EAAG,GAAG,CAAE,EAAA,GAAIA,OAAM,KAAM,EAAA,CAAA;AACnC,MAAA,OAAO,SAAY,GAAA,CAAA,KAAA,EAAQ,CAAC,CAAA,EAAA,EAAK,CAAC,CAAK,EAAA,EAAA,CAAC,CAAK,EAAA,EAAA,CAAC,MAAM,CAAO,IAAA,EAAA,CAAC,CAAK,EAAA,EAAA,CAAC,KAAK,CAAC,CAAA,CAAA,CAAA,CAAA;AAAA,KAC1E;AAEA,IAAA,SAAS,cAAc,KAAgB,EAAA;AACrC,MAAA,UAAA,CAAW,KAAQ,GAAA,KAAA,CAAA;AAAA,KACrB;AAEA,IAAA,MAAM,wBAAwBK,sBAAS,CAAA,aAAA,EAAe,KAAK,EAAE,OAAA,EAAS,MAAM,CAAA,CAAA;AAC5E,IAAA,SAAS,IAAO,GAAA;AACd,MAAA,IAAI,cAAc,KAAO;AACzB,QAAA,OAAA;AAAkB,MACpB,aAAA,CAAA,IAAA,CAAA,CAAA;AAEA,KAAA;AACE,IAAA,SAAA,IAAA,GAAA;AACA,MAAW,qBAAA,CAAA,KAAA,CAAA,CAAA;AAAA,MACb,UAAA,EAAA,CAAA;AAEA,KAAA;AACE,IAAA,SAAA,UAAe,GAAA;AACb,MAAAC,mBAAsB;AACpB,QAAM,IAAA,KAAA,CAAA;AAA2B,UAC5BN,OAAA,CAAA,UAAA,CAAA,KAAA,CAAA,UAAA,CAAA,CAAA;AACL,SAAA,MAAA;AACA,UAAAA,OAAA,CAAA,KAAe,GAAA,EAAA,CAAA;AACb,UAAAM,YAAA,CAAA,MAAA;AAAuB,YACxB,cAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAAA,WACH,CAAA,CAAA;AAAA,SACD;AAAA,OACH,CAAA,CAAA;AAEA,KAAA;AACE,IAAA,sBAAyB,GAAA;AACzB,MAAA,IAAI,aAAkB,CAAA,KAAA;AACpB,QAAW,OAAA;AAAA,MACb,IAAA,UAAA,CAAA,KAAA,EAAA;AACA,QAAsB,UAAA,EAAA,CAAA;AAAiB,OACzC;AAEA,MAAA,qBAAwB,CAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AACtB,KAAA;AACA,IAAA;AACA,MAAA,0BAAwB,CAAAN,OAAA,CAAA,KAAA,CAAA,GAAA,YAAA,CAAA,KAAA,GAAAA,OAAA,CAAA,KAAA,CAAA;AACxB,MAAA,IAAI,yBAAqB,EAAA,KAAA,CAAA,CAAA;AACvB,MAAU,IAAA,CAAAO,kBAAA,EAAA;AAAgD,MAC5D,IAAA,KAAA,CAAA,aAAA,EAAA;AACA,QAAA,QAAA,IAAA,IAAA,GAAA,KAA2B,CAAA,GAAA,QAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAAC,eAAA,CAAA,CAAA,CAAA,CAAA;AAE3B,OAAA;AACE,MAAM,2BAAqB,CAAA,CAAA;AAAA,MAAAF;AACN,QACnB,MAAA,WAA6B,IAAAG,gBAAA,CAAA;AAAA,UAC7B,WAAa,EAAA,KAAA,CAAA,SAAA;AAAA,UACd,MAAA,EAAA,KAAA,CAAA,WAAA,IAAA,EAAA;AACD,UAAA,KAAK,EAAA,KAAc,CAAA,UAAA;AACjB,SAAW,CAAA,CAAA;AAAA,QACb,IAAA,CAAAT,OAAA,CAAA,OAAA,CAAA,QAAA,CAAA,EAAA;AAAA,UACD,UAAA,EAAA,CAAA;AAAA,SACH;AAEA,OAAA,CAAA,CAAA;AACE,KAAA;AACA,IAAK,SAAA,KAAA,GAAA;AACL,MAAK;AACL,MAAA,IAAI,CAAMU,wBAAA,EAAe,YAAa,CAAA,KAAA,CAAA,CAAA;AACpC,MAAU,IAAA,CAAAH,kBAAA,EAAA,YAAmB,CAAA,KAAM,CAAC,CAAQ;AAAc,MAC5D,IAAA,KAAA,CAAA,UAAA,KAAA,YAAA,CAAA,KAAA,IAAA,KAAA,CAAA,aAAA,EAAA;AACA,QAAW,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAAC,eAAA,CAAA,CAAA,CAAA,CAAA;AAAA,OACb;AAEA,MAAA,UAA8B,EAAA,CAAA;AAC5B,KAAI;AACJ,IAAK,SAAA,kBAAA,GAAA;AACL,MAAA,IAAA,CAAA,gBAAyB;AAAA,QAC3B,OAAA;AAEA,MAAA,IAAA,EAAA,CAAS;AACP,MAAA,SAAqB,CAAA,KAAA,IAAA,KAAA,EAAA,CAAA;AACrB,KAAA;AACA,IAAA,SAAA,SAAmB,CAAA,KAAA,EAAA;AACnB,MAAW,KAAA,CAAA,cAAA,EAAA,CAAA;AAAA,MACb,KAAA,CAAA,eAAA,EAAA,CAAA;AAEA,MAAA,qBAAuB;AACrB,MAAA;AAAoB,KAAA;AACF,IAAA,SACX,aAAW,CAAA,KAAA,EAAA;AAAA,MAAA,QACA,EAAA,CAAA;AACd,MAAA,QAAA,KAAqB,CAAA,IAAA;AACrB,QAAA,KAAAG,eAAsB,CAAA,KAAA,CAAA;AACtB,QAAK,KAAAA,eAAA,CAAA,WAAA,CAAA;AACL,QAAgB,KAAAA,eAAA,CAAA,KAAA;AAChB,UAAA,KAAA,CAAA,cAAA,EAAA,CAAA;AAAA,eACc,CAAA,eAAA,EAAA,CAAA;AACd,UAAA,IAAA,EAAA,CAAA;AACA,UAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,cAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,QAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,EAAA,CAAA;AAAA,UACJ,MAAA;AAAA,QACF,KAAAA,eAAA,CAAA,GAAA;AAEA,UAAA,SAAiB,CAAA,KAAA,CAAA,CAAA;AACf,UAAA,MAAA;AAAuB,OACzB;AAEA,KAAA;AACE,IAAA,SAAA;AAAsB,MACxB,UAAA,CAAA,KAAA,CAAA,KAAA,EAAA,CAAA;AAEA,KAAA;AAAA,IAAA,SACqB,IAAA,GAAA;AAAA,MACnB,UAAS,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA;AACP,KAAsB;AACtB,IAAqBC,SAAA,CAAA,MAAA,YAAA,CAAA,KAAA,EAAA,CAAA,GAAA,KAAA;AAAA,MACvB,kBAAA,IAAA,IAAA,CAAA,cAAA,EAAA,GAAA,CAAA,CAAA;AAAA,MACF,kBAAA,GAAA,IAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AAAA,IAAAA,gBACcZ,OAAA,CAAA,KAAA,EAAA,MAAA;AAAA,MACZ,IAAM,CAAA,KAAA,CAAA,UAAA,IAAA,CAAA,cAAA,CAAA,KAAA,EAAA;AACJ,QAAA,cAAyB,CAAA,KAAA,GAAA;AACvB,OAAA;AAAuB,KACzB,CAAA,CAAA;AAAA,IACFY,SAAA,CAAA,MAAA,KAAA,CAAA,UAAA,EAAA,CAAA,MAAA,KAAA;AAAA,MACF,IAAA,CAAA,MAAA,EAAA;AAEA,QAAA,cAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAAA,aACc,IAAA,MAAA,IAAA,MAAA,KAAAZ,OAAA,CAAA,KAAA,EAAA;AAAA,QACA,kBAAA,GAAA,KAAA,CAAA;AACV,QAAAA,OAAK,CAAQ,UAAA,CAAA,MAAA,CAAA,CAAA;AACX,OAAA;AAAuB,KAAA,CAAA,CAAA;AAEvB,IAAqBa,WAAA,CAAAC,gDAAA,EAAA,WAAA,CAAA,CAAA;AACrB,IAAA,MAAA,CAAA;AAAuB,aACzBd,OAAA;AAAA,MACF,IAAA;AAAA,MACF,IAAA;AAEA,MAAA,KAAA;AAEA,MAAa,IAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAAA,OAAAe,aAAA,EAAA,EAAAC,eAAA,CAAAC,SAAA,CAAAC,iBAAA,CAAA,EAAA;AAAA,QAIX,OAAA,EAAA,QAAA;AAAA,QAAA,GAAA,EAAA,MAAA;AAAA,QAAA,OAAA,EAAA,UAAA,CAAA,KAAA;AAAA,QAAA,YAAA,EAAA,KAAA;AAAA,QAIA,qBAAA,EAAA,CAAA,QAAA,EAAA,KAAA,EAAA,OAAA,EAAA,MAAA,CAAA;AAAA,QAAA,MAAA,EAAA,CAAA;AAAA,QAAA,kBAAA,EAAA,KAAA;AAAA,QAAA,cAAA,EAAA,CAAAD,SAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,QAAA,EAAA,OAAA,CAAA,EAAA,IAAA,CAAA,WAAA,CAAA;AAAA,QAIA,yBAAA,EAAA,KAAA;AAAA,QAAA,IAAA,EAAA,EAAA;AAAA,QAAA,MAAA,EAAA,OAAA;AAAA,QAAA,OAAA,EAAA,OAAA;AAAA,QAIA,UAAA,EAAA,IAAA,CAAA,UAAA;AAAA,QAAA,UAAA,EAAA,CAAA,EAAAA,SAAA,CAAA,EAAA,CAAA,CAAA,SAAA,CAAA,KAAA,CAAA,YAAA,CAAA;AAAA,QAAA,UAAA,EAAA,IAAA,CAAA,UAAA;AAAA,QAAA,WAAA,EAAA,IAAA,CAAA,QAAA;AAAA,QAIA,MAAA,EAAA,CAAA,MAAA,KAAA,aAAA,CAAA,KAAA,CAAA;AAAA,OACD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}