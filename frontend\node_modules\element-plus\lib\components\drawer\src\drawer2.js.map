{"version": 3, "file": "drawer2.js", "sources": ["../../../../../../packages/components/drawer/src/drawer.vue"], "sourcesContent": ["<template>\n  <el-teleport\n    :to=\"appendTo\"\n    :disabled=\"appendTo !== 'body' ? false : !appendToBody\"\n  >\n    <transition\n      :name=\"ns.b('fade')\"\n      @after-enter=\"afterEnter\"\n      @after-leave=\"afterLeave\"\n      @before-leave=\"beforeLeave\"\n    >\n      <el-overlay\n        v-show=\"visible\"\n        :mask=\"modal\"\n        :overlay-class=\"modalClass\"\n        :z-index=\"zIndex\"\n        @click=\"onModalClick\"\n      >\n        <el-focus-trap\n          loop\n          :trapped=\"visible\"\n          :focus-trap-el=\"drawerRef\"\n          :focus-start-el=\"focusStartRef\"\n          @focus-after-trapped=\"onOpenAutoFocus\"\n          @focus-after-released=\"onCloseAutoFocus\"\n          @focusout-prevented=\"onFocusoutPrevented\"\n          @release-requested=\"onCloseRequested\"\n        >\n          <el-splitter\n            :class=\"ns.b('splitter')\"\n            :layout=\"isHorizontal ? 'horizontal' : 'vertical'\"\n          >\n            <el-splitter-panel\n              v-if=\"['rtl', 'btt'].includes(direction)\"\n              @click=\"onModalClick\"\n            />\n            <el-splitter-panel :resizable=\"resizable\" :size=\"drawerSize\">\n              <div\n                ref=\"drawerRef\"\n                aria-modal=\"true\"\n                :aria-label=\"title || undefined\"\n                :aria-labelledby=\"!title ? titleId : undefined\"\n                :aria-describedby=\"bodyId\"\n                v-bind=\"$attrs\"\n                :class=\"[ns.b(), direction, visible && 'open']\"\n                role=\"dialog\"\n                @click.stop\n              >\n                <span\n                  ref=\"focusStartRef\"\n                  :class=\"ns.e('sr-focus')\"\n                  tabindex=\"-1\"\n                />\n                <header\n                  v-if=\"withHeader\"\n                  :class=\"[ns.e('header'), headerClass]\"\n                >\n                  <slot\n                    v-if=\"!$slots.title\"\n                    name=\"header\"\n                    :close=\"handleClose\"\n                    :title-id=\"titleId\"\n                    :title-class=\"ns.e('title')\"\n                  >\n                    <span\n                      v-if=\"!$slots.title\"\n                      :id=\"titleId\"\n                      role=\"heading\"\n                      :aria-level=\"headerAriaLevel\"\n                      :class=\"ns.e('title')\"\n                    >\n                      {{ title }}\n                    </span>\n                  </slot>\n                  <slot v-else name=\"title\">\n                    <!-- DEPRECATED SLOT -->\n                  </slot>\n                  <button\n                    v-if=\"showClose\"\n                    :aria-label=\"t('el.drawer.close')\"\n                    :class=\"ns.e('close-btn')\"\n                    type=\"button\"\n                    @click=\"handleClose\"\n                  >\n                    <el-icon :class=\"ns.e('close')\">\n                      <close />\n                    </el-icon>\n                  </button>\n                </header>\n                <template v-if=\"rendered\">\n                  <div :id=\"bodyId\" :class=\"[ns.e('body'), bodyClass]\">\n                    <slot />\n                  </div>\n                </template>\n                <div\n                  v-if=\"$slots.footer\"\n                  :class=\"[ns.e('footer'), footerClass]\"\n                >\n                  <slot name=\"footer\" />\n                </div>\n              </div>\n            </el-splitter-panel>\n            <el-splitter-panel\n              v-if=\"['ltr', 'ttb'].includes(direction)\"\n              @click=\"onModalClick\"\n            />\n          </el-splitter>\n        </el-focus-trap>\n      </el-overlay>\n    </transition>\n  </el-teleport>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, ref, useSlots } from 'vue'\nimport { Close } from '@element-plus/icons-vue'\nimport { ElOverlay } from '@element-plus/components/overlay'\nimport ElFocusTrap from '@element-plus/components/focus-trap'\nimport ElTeleport from '@element-plus/components/teleport'\nimport ElSplitter, { ElSplitterPanel } from '@element-plus/components/splitter'\nimport { useDialog } from '@element-plus/components/dialog'\nimport ElIcon from '@element-plus/components/icon'\nimport { useDeprecated, useLocale, useNamespace } from '@element-plus/hooks'\nimport { drawerEmits, drawerProps } from './drawer'\nimport { addUnit } from '@element-plus/utils'\n\ndefineOptions({\n  name: 'ElDrawer',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(drawerProps)\ndefineEmits(drawerEmits)\nconst slots = useSlots()\n\nuseDeprecated(\n  {\n    scope: 'el-drawer',\n    from: 'the title slot',\n    replacement: 'the header slot',\n    version: '3.0.0',\n    ref: 'https://element-plus.org/en-US/component/drawer.html#slots',\n  },\n  computed(() => !!slots.title)\n)\n\nconst drawerRef = ref<HTMLElement>()\nconst focusStartRef = ref<HTMLElement>()\nconst ns = useNamespace('drawer')\nconst { t } = useLocale()\nconst {\n  afterEnter,\n  afterLeave,\n  beforeLeave,\n  visible,\n  rendered,\n  titleId,\n  bodyId,\n  zIndex,\n  onModalClick,\n  onOpenAutoFocus,\n  onCloseAutoFocus,\n  onFocusoutPrevented,\n  onCloseRequested,\n  handleClose,\n} = useDialog(props, drawerRef)\n\nconst isHorizontal = computed(\n  () => props.direction === 'rtl' || props.direction === 'ltr'\n)\nconst drawerSize = computed(() => addUnit(props.size))\n\ndefineExpose({\n  handleClose,\n  afterEnter,\n  afterLeave,\n})\n</script>\n"], "names": ["useSlots", "useDeprecated", "computed", "ref", "useNamespace", "useDialog", "addUnit", "_openBlock", "_createBlock", "_unref", "ElTeleport"], "mappings": ";;;;;;;;;;;;;;;;;;;uCA8Hc,CAAA;AAAA,EACZ,IAAM,EAAA,UAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;;;;AAIA,IAAA,MAAM,QAAQA,YAAS,EAAA,CAAA;AAEvB,IAAAC,mBAAA,CAAA;AAAA,MACE,KAAA,EAAA,WAAA;AAAA,MAAA,IACS,EAAA,gBAAA;AAAA,MAAA,WACD,EAAA,iBAAA;AAAA,MAAA,OACO,EAAA,OAAA;AAAA,MAAA,GACJ,EAAA,4DAAA;AAAA,KAAA,EAAAC,YACJ,CAAA,MAAA,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IACP,MAAA,SAAA,GAAAC,OAAA,EAAA,CAAA;AAAA,IAAA,MACS,aAAQ,UAAM,EAAK,CAAA;AAAA,IAC9B,MAAA,EAAA,GAAAC,oBAAA,CAAA,QAAA,CAAA,CAAA;AAEA,IAAA,MAAM,yBAA6B,EAAA,CAAA;AACnC,IAAA,MAAM;AACN,MAAM;AACN,MAAM,UAAI;AACV,MAAM,WAAA;AAAA,MACJ,OAAA;AAAA,MACA,QAAA;AAAA,MACA,OAAA;AAAA,MACA,MAAA;AAAA,MACA,MAAA;AAAA,MACA,YAAA;AAAA,MACA,eAAA;AAAA,MACA,gBAAA;AAAA,MACA,mBAAA;AAAA,MACA,gBAAA;AAAA,MACA,WAAA;AAAA,KACA,GAAAC,mBAAA,CAAA,KAAA,EAAA,SAAA,CAAA,CAAA;AAAA,IACA,MAAA,YAAA,GAAAH,YAAA,CAAA,MAAA,KAAA,CAAA,SAAA,KAAA,KAAA,IAAA,KAAA,CAAA,SAAA,KAAA,KAAA,CAAA,CAAA;AAAA,IACA,MAAA,UAAA,GAAAA,YAAA,CAAA,MAAAI,aAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AAAA,IACF,MAAc,CAAA;AAEd,MAAA,WAAqB;AAAA,MACnB,UAAM;AAAiD,MACzD,UAAA;AACA,KAAA,CAAA,CAAA;AAEA,IAAa,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACX,OAAAC,aAAA,EAAA,EAAAC,eAAA,CAAAC,SAAA,CAAAC,kBAAA,CAAA,EAAA;AAAA,QACA,EAAA,EAAA,IAAA,CAAA,QAAA;AAAA,QACA,QAAA,EAAA,IAAA,CAAA,QAAA,KAAA,MAAA,GAAA,KAAA,GAAA,CAAA,IAAA,CAAA,YAAA;AAAA,OACD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}