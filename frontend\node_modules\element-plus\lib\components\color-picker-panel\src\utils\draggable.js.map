{"version": 3, "file": "draggable.js", "sources": ["../../../../../../../packages/components/color-picker-panel/src/utils/draggable.ts"], "sourcesContent": ["import { isClient } from '@element-plus/utils'\n\nlet isDragging = false\n\nexport interface DraggableOptions {\n  drag?: (event: MouseEvent | TouchEvent) => void\n  start?: (event: MouseEvent | TouchEvent) => void\n  end?: (event: MouseEvent | TouchEvent) => void\n}\n\nexport function draggable(element: HTMLElement, options: DraggableOptions) {\n  if (!isClient) return\n\n  const moveFn = function (event: MouseEvent | TouchEvent) {\n    options.drag?.(event)\n  }\n\n  const upFn = function (event: MouseEvent | TouchEvent) {\n    document.removeEventListener('mousemove', moveFn)\n    document.removeEventListener('mouseup', upFn)\n    document.removeEventListener('touchmove', moveFn)\n    document.removeEventListener('touchend', upFn)\n    document.onselectstart = null\n    document.ondragstart = null\n\n    isDragging = false\n\n    options.end?.(event)\n  }\n\n  const downFn = function (event: MouseEvent | TouchEvent) {\n    if (isDragging) return\n    event.preventDefault()\n    document.onselectstart = () => false\n    document.ondragstart = () => false\n    document.addEventListener('mousemove', moveFn)\n    document.addEventListener('mouseup', upFn)\n    document.addEventListener('touchmove', moveFn)\n    document.addEventListener('touchend', upFn)\n\n    isDragging = true\n\n    options.start?.(event)\n  }\n\n  element.addEventListener('mousedown', downFn)\n  element.addEventListener('touchstart', downFn, { passive: false })\n}\n"], "names": ["isClient"], "mappings": ";;;;;;AACA,IAAI,UAAU,GAAG,KAAK,CAAC;AAChB,SAAS,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE;AAC5C,EAAE,IAAI,CAACA,aAAQ;AACf,IAAI,OAAO;AACX,EAAE,MAAM,MAAM,GAAG,SAAS,KAAK,EAAE;AACjC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACnE,GAAG,CAAC;AACJ,EAAE,MAAM,IAAI,GAAG,SAAS,KAAK,EAAE;AAC/B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;AACtD,IAAI,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AAClD,IAAI,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;AACtD,IAAI,QAAQ,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AACnD,IAAI,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC;AAClC,IAAI,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;AAChC,IAAI,UAAU,GAAG,KAAK,CAAC;AACvB,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAClE,GAAG,CAAC;AACJ,EAAE,MAAM,MAAM,GAAG,SAAS,KAAK,EAAE;AACjC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,UAAU;AAClB,MAAM,OAAO;AACb,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;AAC3B,IAAI,QAAQ,CAAC,aAAa,GAAG,MAAM,KAAK,CAAC;AACzC,IAAI,QAAQ,CAAC,WAAW,GAAG,MAAM,KAAK,CAAC;AACvC,IAAI,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;AACnD,IAAI,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AAC/C,IAAI,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;AACnD,IAAI,QAAQ,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AAChD,IAAI,UAAU,GAAG,IAAI,CAAC;AACtB,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACpE,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;AAChD,EAAE,OAAO,CAAC,gBAAgB,CAAC,YAAY,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;AACrE;;;;"}