{"version": 3, "file": "picker.js", "sources": ["../../../../../../../packages/components/time-picker/src/common/picker.vue"], "sourcesContent": ["<template>\n  <el-tooltip\n    ref=\"refPopper\"\n    :visible=\"pickerVisible\"\n    effect=\"light\"\n    pure\n    trigger=\"click\"\n    v-bind=\"$attrs\"\n    role=\"dialog\"\n    teleported\n    :transition=\"`${nsDate.namespace.value}-zoom-in-top`\"\n    :popper-class=\"[`${nsDate.namespace.value}-picker__popper`, popperClass!]\"\n    :popper-style=\"popperStyle\"\n    :popper-options=\"elPopperOptions\"\n    :fallback-placements=\"fallbackPlacements\"\n    :gpu-acceleration=\"false\"\n    :placement=\"placement\"\n    :stop-popper-mouse-event=\"false\"\n    :hide-after=\"0\"\n    persistent\n    @before-show=\"onBeforeShow\"\n    @show=\"onShow\"\n    @hide=\"onHide\"\n  >\n    <template #default>\n      <el-input\n        v-if=\"!isRangeInput\"\n        :id=\"(id as string | undefined)\"\n        ref=\"inputRef\"\n        container-role=\"combobox\"\n        :model-value=\"(displayValue as string)\"\n        :name=\"(name as string | undefined)\"\n        :size=\"pickerSize\"\n        :disabled=\"pickerDisabled\"\n        :placeholder=\"placeholder\"\n        :class=\"[\n          nsDate.b('editor'),\n          nsDate.bm('editor', type),\n          nsDate.is('focus', pickerVisible),\n          $attrs.class,\n        ]\"\n        :style=\"$attrs.style\"\n        :readonly=\"\n          !editable ||\n          readonly ||\n          isDatesPicker ||\n          isMonthsPicker ||\n          isYearsPicker ||\n          type === 'week'\n        \"\n        :aria-label=\"ariaLabel\"\n        :tabindex=\"tabindex\"\n        :validate-event=\"false\"\n        @input=\"onUserInput\"\n        @focus=\"handleFocus\"\n        @blur=\"handleBlur\"\n        @keydown=\"handleKeydownInput\"\n        @change=\"handleChange\"\n        @mousedown=\"onMouseDownInput\"\n        @mouseenter=\"onMouseEnter\"\n        @mouseleave=\"onMouseLeave\"\n        @touchstart.passive=\"onTouchStartInput\"\n        @click.stop\n      >\n        <template #prefix>\n          <el-icon\n            v-if=\"triggerIcon\"\n            :class=\"nsInput.e('icon')\"\n            @mousedown.prevent=\"onMouseDownInput\"\n            @touchstart.passive=\"onTouchStartInput\"\n          >\n            <component :is=\"triggerIcon\" />\n          </el-icon>\n        </template>\n        <template #suffix>\n          <el-icon\n            v-if=\"showClearBtn && clearIcon\"\n            :class=\"`${nsInput.e('icon')} clear-icon`\"\n            @mousedown.prevent=\"NOOP\"\n            @click=\"onClearIconClick\"\n          >\n            <component :is=\"clearIcon\" />\n          </el-icon>\n        </template>\n      </el-input>\n      <picker-range-trigger\n        v-else\n        :id=\"(id as string[] | undefined)\"\n        ref=\"inputRef\"\n        :model-value=\"displayValue\"\n        :name=\"(name as string[] | undefined)\"\n        :disabled=\"pickerDisabled\"\n        :readonly=\"!editable || readonly\"\n        :start-placeholder=\"startPlaceholder\"\n        :end-placeholder=\"endPlaceholder\"\n        :class=\"rangeInputKls\"\n        :style=\"$attrs.style\"\n        :aria-label=\"ariaLabel\"\n        :tabindex=\"tabindex\"\n        autocomplete=\"off\"\n        role=\"combobox\"\n        @click=\"onMouseDownInput\"\n        @focus=\"handleFocus\"\n        @blur=\"handleBlur\"\n        @start-input=\"handleStartInput\"\n        @start-change=\"handleStartChange\"\n        @end-input=\"handleEndInput\"\n        @end-change=\"handleEndChange\"\n        @mousedown=\"onMouseDownInput\"\n        @mouseenter=\"onMouseEnter\"\n        @mouseleave=\"onMouseLeave\"\n        @touchstart.passive=\"onTouchStartInput\"\n        @keydown=\"handleKeydownInput\"\n      >\n        <template #prefix>\n          <el-icon\n            v-if=\"triggerIcon\"\n            :class=\"[nsInput.e('icon'), nsRange.e('icon')]\"\n          >\n            <component :is=\"triggerIcon\" />\n          </el-icon>\n        </template>\n        <template #range-separator>\n          <slot name=\"range-separator\">\n            <span :class=\"nsRange.b('separator')\">{{ rangeSeparator }}</span>\n          </slot>\n        </template>\n        <template #suffix>\n          <el-icon\n            v-if=\"clearIcon\"\n            :class=\"clearIconKls\"\n            @mousedown.prevent=\"NOOP\"\n            @click=\"onClearIconClick\"\n          >\n            <component :is=\"clearIcon\" />\n          </el-icon>\n        </template>\n      </picker-range-trigger>\n    </template>\n    <template #content>\n      <slot\n        :visible=\"pickerVisible\"\n        :actual-visible=\"pickerActualVisible\"\n        :parsed-value=\"parsedValue\"\n        :format=\"format\"\n        :date-format=\"dateFormat\"\n        :time-format=\"timeFormat\"\n        :unlink-panels=\"unlinkPanels\"\n        :type=\"type\"\n        :default-value=\"defaultValue\"\n        :show-now=\"showNow\"\n        :show-confirm=\"showConfirm\"\n        :show-footer=\"showFooter\"\n        :show-week-number=\"showWeekNumber\"\n        @pick=\"onPick\"\n        @select-range=\"setSelectionRange\"\n        @set-picker-option=\"onSetPickerOption\"\n        @calendar-change=\"onCalendarChange\"\n        @panel-change=\"onPanelChange\"\n        @mousedown.stop\n      />\n    </template>\n  </el-tooltip>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  inject,\n  nextTick,\n  onBeforeUnmount,\n  provide,\n  ref,\n  unref,\n  useAttrs,\n  watch,\n} from 'vue'\nimport { onClickOutside, unrefElement } from '@vueuse/core'\nimport {\n  useEmptyValues,\n  useFocusController,\n  useNamespace,\n} from '@element-plus/hooks'\nimport {\n  useFormDisabled,\n  useFormItem,\n  useFormSize,\n} from '@element-plus/components/form'\nimport ElInput from '@element-plus/components/input'\nimport ElIcon from '@element-plus/components/icon'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport { NOOP, debugWarn, isArray } from '@element-plus/utils'\nimport {\n  CHANGE_EVENT,\n  EVENT_CODE,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { Calendar, Clock } from '@element-plus/icons-vue'\nimport { dayOrDaysToDate, valueEquals } from '../utils'\nimport {\n  PICKER_BASE_INJECTION_KEY,\n  PICKER_POPPER_OPTIONS_INJECTION_KEY,\n  ROOT_COMMON_PICKER_INJECTION_KEY,\n} from '../constants'\nimport { useCommonPicker } from '../composables/use-common-picker'\nimport { timePickerDefaultProps } from './props'\nimport PickerRangeTrigger from './picker-range-trigger.vue'\n\nimport type { InputInstance } from '@element-plus/components/input'\nimport type { Dayjs } from 'dayjs'\nimport type { ComponentPublicInstance, Ref } from 'vue'\nimport type { Options } from '@popperjs/core'\nimport type { DayOrDays, TimePickerDefaultProps, UserInput } from './props'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\n\ndefineOptions({\n  name: 'Picker',\n})\n\nconst props = defineProps(timePickerDefaultProps)\nconst emit = defineEmits([\n  UPDATE_MODEL_EVENT,\n  CHANGE_EVENT,\n  'focus',\n  'blur',\n  'clear',\n  'calendar-change',\n  'panel-change',\n  'visible-change',\n  'keydown',\n])\nconst attrs = useAttrs()\n\nconst nsDate = useNamespace('date')\nconst nsInput = useNamespace('input')\nconst nsRange = useNamespace('range')\n\nconst { formItem } = useFormItem()\nconst elPopperOptions = inject(\n  PICKER_POPPER_OPTIONS_INJECTION_KEY,\n  {} as Options\n)\nconst { valueOnClear } = useEmptyValues(props, null)\n\nconst refPopper = ref<TooltipInstance>()\nconst inputRef = ref<InputInstance>()\nconst valueOnOpen = ref<TimePickerDefaultProps['modelValue'] | null>(null)\nlet hasJustTabExitedInput = false\n\nconst pickerDisabled = useFormDisabled()\n\nconst commonPicker = useCommonPicker(props, emit)\nconst {\n  parsedValue,\n  pickerActualVisible,\n  userInput,\n  pickerVisible,\n  pickerOptions,\n  valueIsEmpty,\n  emitInput,\n  onPick,\n  //@ts-ignore\n  onSetPickerOption,\n  //@ts-ignore\n  onCalendarChange,\n  //@ts-ignore\n  onPanelChange,\n} = commonPicker\n\nconst { isFocused, handleFocus, handleBlur } = useFocusController(inputRef, {\n  disabled: pickerDisabled,\n  beforeFocus() {\n    return props.readonly\n  },\n  afterFocus() {\n    pickerVisible.value = true\n  },\n  beforeBlur(event) {\n    return (\n      !hasJustTabExitedInput && refPopper.value?.isFocusInsideContent(event)\n    )\n  },\n  afterBlur() {\n    handleChange()\n    pickerVisible.value = false\n    hasJustTabExitedInput = false\n    props.validateEvent &&\n      formItem?.validate('blur').catch((err) => debugWarn(err))\n  },\n})\n\nconst hovering = ref(false)\n\nconst rangeInputKls = computed(() => [\n  nsDate.b('editor'),\n  nsDate.bm('editor', props.type),\n  nsInput.e('wrapper'),\n  nsDate.is('disabled', pickerDisabled.value),\n  nsDate.is('active', pickerVisible.value),\n  nsRange.b('editor'),\n  pickerSize ? nsRange.bm('editor', pickerSize.value) : '',\n  attrs.class,\n])\n\nconst clearIconKls = computed(() => [\n  nsInput.e('icon'),\n  nsRange.e('close-icon'),\n  !showClearBtn.value ? nsRange.e('close-icon--hidden') : '',\n])\n\nwatch(pickerVisible, (val) => {\n  if (!val) {\n    userInput.value = null\n    nextTick(() => {\n      emitChange(props.modelValue)\n    })\n  } else {\n    nextTick(() => {\n      if (val) {\n        valueOnOpen.value = props.modelValue\n      }\n    })\n  }\n})\nconst emitChange = (\n  val: TimePickerDefaultProps['modelValue'] | null,\n  isClear?: boolean\n) => {\n  // determine user real change only\n  if (isClear || !valueEquals(val, valueOnOpen.value)) {\n    emit(CHANGE_EVENT, val)\n    // Set the value of valueOnOpen when clearing to avoid triggering change events multiple times.\n    isClear && (valueOnOpen.value = val)\n    props.validateEvent &&\n      formItem?.validate('change').catch((err) => debugWarn(err))\n  }\n}\nconst emitKeydown = (e: KeyboardEvent) => {\n  emit('keydown', e)\n}\n\nconst refInput = computed<HTMLInputElement[]>(() => {\n  if (inputRef.value) {\n    return Array.from<HTMLInputElement>(\n      inputRef.value.$el.querySelectorAll('input')\n    )\n  }\n  return []\n})\n\n// @ts-ignore\nconst setSelectionRange = (start: number, end: number, pos?: 'min' | 'max') => {\n  const _inputs = refInput.value\n  if (!_inputs.length) return\n  if (!pos || pos === 'min') {\n    _inputs[0].setSelectionRange(start, end)\n    _inputs[0].focus()\n  } else if (pos === 'max') {\n    _inputs[1].setSelectionRange(start, end)\n    _inputs[1].focus()\n  }\n}\n\nconst onBeforeShow = () => {\n  pickerActualVisible.value = true\n}\n\nconst onShow = () => {\n  emit('visible-change', true)\n}\n\nconst onHide = () => {\n  pickerActualVisible.value = false\n  pickerVisible.value = false\n  emit('visible-change', false)\n}\n\nconst handleOpen = () => {\n  pickerVisible.value = true\n}\n\nconst handleClose = () => {\n  pickerVisible.value = false\n}\n\nconst displayValue = computed<UserInput>(() => {\n  if (!pickerOptions.value.panelReady) return ''\n  const formattedValue = formatDayjsToString(parsedValue.value)\n  if (isArray(userInput.value)) {\n    return [\n      userInput.value[0] || (formattedValue && formattedValue[0]) || '',\n      userInput.value[1] || (formattedValue && formattedValue[1]) || '',\n    ]\n  } else if (userInput.value !== null) {\n    return userInput.value\n  }\n  if (!isTimePicker.value && valueIsEmpty.value) return ''\n  if (!pickerVisible.value && valueIsEmpty.value) return ''\n  if (formattedValue) {\n    return isDatesPicker.value || isMonthsPicker.value || isYearsPicker.value\n      ? (formattedValue as Array<string>).join(', ')\n      : formattedValue\n  }\n  return ''\n})\n\nconst isTimeLikePicker = computed(() => props.type.includes('time'))\n\nconst isTimePicker = computed(() => props.type.startsWith('time'))\n\nconst isDatesPicker = computed(() => props.type === 'dates')\n\nconst isMonthsPicker = computed(() => props.type === 'months')\n\nconst isYearsPicker = computed(() => props.type === 'years')\n\nconst triggerIcon = computed(\n  () => props.prefixIcon || (isTimeLikePicker.value ? Clock : Calendar)\n)\n\nconst showClearBtn = computed(\n  () =>\n    props.clearable &&\n    !pickerDisabled.value &&\n    !props.readonly &&\n    !valueIsEmpty.value &&\n    (hovering.value || isFocused.value)\n)\n\nconst onClearIconClick = (event: MouseEvent) => {\n  if (props.readonly || pickerDisabled.value) return\n  if (showClearBtn.value) {\n    event.stopPropagation()\n    // When the handleClear Function was provided, emit null will be executed inside it\n    // There is no need for us to execute emit null twice. #14752\n    if (pickerOptions.value.handleClear) {\n      pickerOptions.value.handleClear()\n    } else {\n      emitInput(valueOnClear.value)\n    }\n    emitChange(valueOnClear.value, true)\n    onHide()\n  }\n  emit('clear')\n}\n\nconst onMouseDownInput = async (event: MouseEvent) => {\n  if (props.readonly || pickerDisabled.value) return\n  if ((event.target as HTMLElement)?.tagName !== 'INPUT' || isFocused.value) {\n    pickerVisible.value = true\n  }\n}\nconst onMouseEnter = () => {\n  if (props.readonly || pickerDisabled.value) return\n  if (!valueIsEmpty.value && props.clearable) {\n    hovering.value = true\n  }\n}\nconst onMouseLeave = () => {\n  hovering.value = false\n}\n\nconst onTouchStartInput = (event: TouchEvent) => {\n  if (props.readonly || pickerDisabled.value) return\n  if (\n    (event.touches[0].target as HTMLElement)?.tagName !== 'INPUT' ||\n    isFocused.value\n  ) {\n    pickerVisible.value = true\n  }\n}\n\nconst isRangeInput = computed(() => {\n  return props.type.includes('range')\n})\n\nconst pickerSize = useFormSize()\n\nconst popperEl = computed(() => unref(refPopper)?.popperRef?.contentRef)\n\nconst stophandle = onClickOutside(\n  inputRef as Ref<ComponentPublicInstance>,\n  (e: PointerEvent) => {\n    const unrefedPopperEl = unref(popperEl)\n    const inputEl = unrefElement(inputRef as Ref<ComponentPublicInstance>)\n    if (\n      (unrefedPopperEl &&\n        (e.target === unrefedPopperEl ||\n          e.composedPath().includes(unrefedPopperEl))) ||\n      e.target === inputEl ||\n      (inputEl && e.composedPath().includes(inputEl))\n    )\n      return\n    pickerVisible.value = false\n  }\n)\n\nonBeforeUnmount(() => {\n  stophandle?.()\n})\n\nconst handleChange = () => {\n  if (userInput.value) {\n    const value = parseUserInputToDayjs(displayValue.value)\n    if (value) {\n      if (isValidValue(value)) {\n        emitInput(dayOrDaysToDate(value))\n        userInput.value = null\n      }\n    }\n  }\n  if (userInput.value === '') {\n    emitInput(valueOnClear.value)\n    emitChange(valueOnClear.value, true)\n    userInput.value = null\n  }\n}\n\nconst parseUserInputToDayjs = (value: UserInput) => {\n  if (!value) return null\n  return pickerOptions.value.parseUserInput!(value)\n}\n\nconst formatDayjsToString = (value: DayOrDays) => {\n  if (!value) return null\n  return pickerOptions.value.formatToString!(value)\n}\n\nconst isValidValue = (value: DayOrDays) => {\n  return pickerOptions.value.isValidValue!(value)\n}\n\nconst handleKeydownInput = async (event: Event | KeyboardEvent) => {\n  if (props.readonly || pickerDisabled.value) return\n\n  const { code } = event as KeyboardEvent\n  emitKeydown(event as KeyboardEvent)\n  if (code === EVENT_CODE.esc) {\n    if (pickerVisible.value === true) {\n      pickerVisible.value = false\n      event.preventDefault()\n      event.stopPropagation()\n    }\n    return\n  }\n\n  if (code === EVENT_CODE.down) {\n    if (pickerOptions.value.handleFocusPicker) {\n      event.preventDefault()\n      event.stopPropagation()\n    }\n    if (pickerVisible.value === false) {\n      pickerVisible.value = true\n      await nextTick()\n    }\n    if (pickerOptions.value.handleFocusPicker) {\n      pickerOptions.value.handleFocusPicker()\n      return\n    }\n  }\n\n  if (code === EVENT_CODE.tab) {\n    hasJustTabExitedInput = true\n    return\n  }\n\n  if (code === EVENT_CODE.enter || code === EVENT_CODE.numpadEnter) {\n    if (\n      userInput.value === null ||\n      userInput.value === '' ||\n      isValidValue(parseUserInputToDayjs(displayValue.value) as DayOrDays)\n    ) {\n      handleChange()\n      pickerVisible.value = false\n    }\n    event.stopPropagation()\n    return\n  }\n\n  // if user is typing, do not let picker handle key input\n  if (userInput.value) {\n    event.stopPropagation()\n    return\n  }\n  if (pickerOptions.value.handleKeydownInput) {\n    pickerOptions.value.handleKeydownInput(event as KeyboardEvent)\n  }\n}\nconst onUserInput = (e: string) => {\n  userInput.value = e\n  // Temporary fix when the picker is dismissed and the input box\n  // is focused, just mimic the behavior of antdesign.\n  if (!pickerVisible.value) {\n    pickerVisible.value = true\n  }\n}\n\nconst handleStartInput = (event: Event) => {\n  const target = event.target as HTMLInputElement\n  if (userInput.value) {\n    userInput.value = [target.value, userInput.value[1]]\n  } else {\n    userInput.value = [target.value, null]\n  }\n}\n\nconst handleEndInput = (event: Event) => {\n  const target = event.target as HTMLInputElement\n  if (userInput.value) {\n    userInput.value = [userInput.value[0], target.value]\n  } else {\n    userInput.value = [null, target.value]\n  }\n}\n\nconst handleStartChange = () => {\n  const values = userInput.value as string[]\n  const value = parseUserInputToDayjs(values && values[0]) as Dayjs\n  const parsedVal = unref(parsedValue) as [Dayjs, Dayjs]\n  if (value && value.isValid()) {\n    userInput.value = [\n      formatDayjsToString(value) as string,\n      displayValue.value?.[1] || null,\n    ]\n    const newValue = [value, parsedVal && (parsedVal[1] || null)] as DayOrDays\n    if (isValidValue(newValue)) {\n      emitInput(dayOrDaysToDate(newValue))\n      userInput.value = null\n    }\n  }\n}\n\nconst handleEndChange = () => {\n  const values = unref(userInput) as string[]\n  const value = parseUserInputToDayjs(values && values[1]) as Dayjs\n  const parsedVal = unref(parsedValue) as [Dayjs, Dayjs]\n  if (value && value.isValid()) {\n    userInput.value = [\n      unref(displayValue)?.[0] || null,\n      formatDayjsToString(value) as string,\n    ]\n    const newValue = [parsedVal && parsedVal[0], value] as DayOrDays\n    if (isValidValue(newValue)) {\n      emitInput(dayOrDaysToDate(newValue))\n      userInput.value = null\n    }\n  }\n}\n\nconst focus = () => {\n  inputRef.value?.focus()\n}\n\nconst blur = () => {\n  inputRef.value?.blur()\n}\n\nprovide(PICKER_BASE_INJECTION_KEY, {\n  props,\n})\nprovide(ROOT_COMMON_PICKER_INJECTION_KEY, commonPicker)\n\ndefineExpose({\n  /**\n   * @description focus input box.\n   */\n  focus,\n  /**\n   * @description blur input box.\n   */\n  blur,\n  /**\n   * @description opens picker\n   */\n  handleOpen,\n  /**\n   * @description closes picker\n   */\n  handleClose,\n  /**\n   * @description pick item manually\n   */\n  onPick,\n})\n</script>\n"], "names": ["useAttrs", "useNamespace", "useFormItem", "inject", "PICKER_POPPER_OPTIONS_INJECTION_KEY", "useEmptyValues", "ref", "useFormDisabled", "useFocusController", "debugWarn", "computed", "watch", "nextTick", "valueEquals", "CHANGE_EVENT", "isArray", "Clock", "Calendar", "useFormSize", "unref", "onClickOutside", "unrefElement", "onBeforeUnmount", "dayOrDaysToDate", "EVENT_CODE", "provide", "PICKER_BASE_INJECTION_KEY", "ROOT_COMMON_PICKER_INJECTION_KEY", "_openBlock", "_createBlock", "_unref", "ElTooltip", "_mergeProps"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;uCAuNc,CAAA;AAAA,EACZ,IAAM,EAAA,QAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;AAcA,IAAA,MAAM,QAAQA,YAAS,EAAA,CAAA;AAEvB,IAAM,MAAA,MAAA,GAASC,mBAAa,MAAM,CAAA,CAAA;AAClC,IAAM,MAAA,OAAA,GAAUA,mBAAa,OAAO,CAAA,CAAA;AACpC,IAAM,MAAA,OAAA,GAAUA,mBAAa,OAAO,CAAA,CAAA;AAEpC,IAAM,MAAA,EAAE,QAAS,EAAA,GAAIC,uBAAY,EAAA,CAAA;AACjC,IAAA,MAAM,eAAkB,GAAAC,UAAA,CAAAC,6CAAA,EAAA,EAAA,CAAA,CAAA;AAAA,IACtB,MAAA,EAAA,YAAA,EAAA,GAAAC,sBAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AAAA,IAAA,MACC,SAAA,GAAAC,OAAA,EAAA,CAAA;AAAA,IACH,MAAA,QAAA,GAAAA,OAAA,EAAA,CAAA;AACA,IAAA,MAAM,WAAE,GAAAA,OAAiB,CAAA,IAAA,CAAA,CAAA;AAEzB,IAAA,IAAA,qBAAuC,GAAA,KAAA,CAAA;AACvC,IAAA,MAAM,cAA8B,GAAAC,kCAAA,EAAA,CAAA;AACpC,IAAM,MAAA,YAAA,kCAAmE,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AACzE,IAAA,MAA4B;AAE5B,MAAA;AAEA,MAAM,mBAAe;AACrB,MAAM,SAAA;AAAA,MACJ,aAAA;AAAA,MACA,aAAA;AAAA,MACA,YAAA;AAAA,MACA,SAAA;AAAA,MACA,MAAA;AAAA,MACA,iBAAA;AAAA,MACA,gBAAA;AAAA,MACA,aAAA;AAAA,KAAA,GAAA,YAAA,CAAA;AAAA,IAEA,MAAA,EAAA,SAAA,EAAA,WAAA,EAAA,UAAA,EAAA,GAAAC,0BAAA,CAAA,QAAA,EAAA;AAAA,MAAA,QAAA,EAAA,cAAA;AAAA,MAEA,WAAA,GAAA;AAAA,QAAA,OAAA,KAAA,CAAA,QAAA,CAAA;AAAA,OAEA;AAAA,MACE,UAAA,GAAA;AAEJ,QAAA,aAAmB,CAAA,KAAA,GAAA,IAAA,CAAA;AAAyD,OAChE;AAAA,MACV,UAAc,CAAA,KAAA,EAAA;AACZ,QAAA,IAAA,EAAA,CAAO;AAAM,QACf,OAAA,CAAA,qBAAA,KAAA,CAAA,EAAA,GAAA,SAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,OACa;AACX,MAAA,SAAA,GAAA;AAAsB,QACxB,YAAA,EAAA,CAAA;AAAA,qBACkB,CAAA,KAAA,GAAA,KAAA,CAAA;AAChB,QAAA,qBACG,GAAA,KAAA,CAAA;AAAoE,QAEzE,KAAA,CAAA,aAAA,KAAA,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAAC,eAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,OACY;AACV,KAAa,CAAA,CAAA;AACb,IAAA,MAAA,QAAA,GAAAH,OAAsB,CAAA,KAAA,CAAA,CAAA;AACtB,IAAwB,MAAA,aAAA,GAAAI,YAAA,CAAA,MAAA;AACxB,MAAM,MAAA,CAAA,CAAA,CAAA,QAAA,CAAA;AACoD,MAC5D,MAAA,CAAA,EAAA,CAAA,QAAA,EAAA,KAAA,CAAA,IAAA,CAAA;AAAA,MACD,OAAA,CAAA,CAAA,CAAA,SAAA,CAAA;AAED,MAAM,MAAA,CAAA,EAAA,CAAA,UAAoB,EAAA,cAAA,CAAA,KAAA,CAAA;AAE1B,MAAM,MAAA,CAAA,EAAA,CAAA,QAAA,EAAgB,aAAe,CAAA,KAAA,CAAA;AAAA,MACnC,OAAO,EAAE,CAAQ,QAAA,CAAA;AAAA,MACjB,UAAU,GAAU,OAAA,CAAA,EAAA,CAAA,QAAU,EAAA,UAAA,CAAA,KAAA,CAAA,GAAA,EAAA;AAAA,MAC9B,KAAA,CAAA,KAAmB;AAAA,KAAA,CACnB,CAAO;AAAmC,IAAA,MACnC,YAAa,GAAAA,YAAA,CAAA,MAAc;AAAK,MACvC,OAAA,CAAQ,EAAE,MAAQ,CAAA;AAAA,MAClB,sBAAqB,CAAA;AAAiC,MACtD,CAAM,YAAA,CAAA,KAAA,GAAA,OAAA,CAAA,CAAA,CAAA,oBAAA,CAAA,GAAA,EAAA;AAAA,KACP,CAAA,CAAA;AAED,IAAMC,SAAA,CAAA,aAAA,EAAe,SAAS;AAAM,MAClC,IAAA,CAAA,GAAQ,EAAE;AAAM,QAChB,SAAsB,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,QACRC,YAAA,CAAA,MAAA;AAA0C,UACzD,UAAA,CAAA,KAAA,CAAA,UAAA,CAAA,CAAA;AAED,SAAM,CAAA,CAAA;AACJ,OAAA,MAAU;AACR,QAAAA,YAAA,CAAA,MAAkB;AAClB,UAAA,IAAA,GAAS,EAAM;AACb,YAAA,iBAA2B,GAAA,KAAA,CAAA,UAAA,CAAA;AAAA,WAC5B;AAAA,SACI,CAAA,CAAA;AACL,OAAA;AACE,KAAA,CAAA,CAAA;AACE,IAAA,MAAA,UAAA,GAAA,CAAY,YAAc,KAAA;AAAA,MAC5B,IAAA,OAAA,IAAA,CAAAC,iBAAA,CAAA,GAAA,EAAA,WAAA,CAAA,KAAA,CAAA,EAAA;AAAA,QACF,IAAC,CAAAC,kBAAA,EAAA,GAAA,CAAA,CAAA;AAAA,QACH,OAAA,KAAA,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA,CAAA;AAAA,QACD,KAAA,CAAA,aAAA,KAAA,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAAL,eAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACD,OAAM;AAKJ,KAAA,CAAA;AACE,IAAA,MAAA,qBAAsB;AAEtB,MAAA,IAAA,CAAA,SAAY;AACZ,KAAM,CAAA;AACsD,IAC9D,MAAA,QAAA,GAAAC,YAAA,CAAA,MAAA;AAAA,MACF,IAAA,QAAA,CAAA,KAAA,EAAA;AACA,QAAM,OAAA,KAAA,CAAA,IAAe,CAAqB,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,gBAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACxC,OAAA;AAAiB,MACnB,OAAA,EAAA,CAAA;AAEA,KAAM,CAAA,CAAA;AACJ,IAAA,MAAI,iBAAgB,GAAA,CAAA,KAAA,EAAA,GAAA,EAAA,GAAA,KAAA;AAClB,MAAA,MAAA,OAAa,GAAA,QAAA,CAAA,KAAA,CAAA;AAAA,MAAA,IACX,CAAS,OAAA,CAAA,MAAM;AAA4B,QAC7C,OAAA;AAAA,MACF,IAAA,CAAA,GAAA,IAAA,GAAA,KAAA,KAAA,EAAA;AACA,QAAA,OAAQ,CAAA,CAAA,CAAA,CAAA,iBAAA,CAAA,KAAA,EAAA,GAAA,CAAA,CAAA;AAAA,QACT,OAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAA;AAGD,OAAA,MAA0B,IAAA,GAAA,KAAA,KAAA,EAAgB;AACxC,QAAA,4BAAyB,CAAA,KAAA,EAAA,GAAA,CAAA,CAAA;AACzB,QAAI,WAAS,KAAQ,EAAA,CAAA;AACrB,OAAI;AACF,KAAA,CAAA;AACA,IAAQ,MAAA,YAAS,GAAA,MAAA;AAAA,MACnB,mBAAmB,CAAO,KAAA,GAAA,IAAA,CAAA;AACxB,KAAA,CAAA;AACA,IAAQ,MAAA,MAAA,GAAG,MAAM;AAAA,MACnB,IAAA,CAAA,gBAAA,EAAA,IAAA,CAAA,CAAA;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,eAAe;AACnB,MAAA,mBAAA,CAAoB,KAAQ,GAAA,KAAA,CAAA;AAAA,MAC9B,aAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAEA,MAAA,IAAM,iBAAe,EAAA,KAAA,CAAA,CAAA;AACnB,KAAA,CAAA;AAA2B,IAC7B,MAAA,UAAA,GAAA,MAAA;AAEA,MAAA,aAAe,CAAM,KAAA,GAAA,IAAA,CAAA;AACnB,KAAA,CAAA;AACA,IAAA,MAAA,WAAsB,GAAA,MAAA;AACtB,MAAA,2BAA4B,CAAA;AAAA,KAC9B,CAAA;AAEA,IAAA,MAAM,eAAmBA,YAAA,CAAA,MAAA;AACvB,MAAA,IAAA,CAAA,aAAsB,CAAA,KAAA,CAAA,UAAA;AAAA,QACxB,OAAA,EAAA,CAAA;AAEA,MAAA,oBAA0B,GAAA,mBAAA,CAAA,WAAA,CAAA,KAAA,CAAA,CAAA;AACxB,MAAA,IAAAK,cAAA,CAAA,SAAsB,CAAA,KAAA,CAAA,EAAA;AAAA,QACxB,OAAA;AAEA,UAAM,SAAA,CAAA,KAAe,qBAA0B,IAAA,cAAA,CAAA,CAAA,CAAA,IAAA,EAAA;AAC7C,UAAI,SAAC,CAAA,KAAc,CAAM,CAAA,CAAA,IAAA,cAAmB,IAAA,cAAA,CAAA,CAAA,CAAA,IAAA,EAAA;AAC5C,SAAM,CAAA;AACN,OAAI,MAAA,IAAA,SAAkB,CAAA,KAAA,KAAQ,IAAA,EAAA;AAC5B,QAAO,OAAA,SAAA,CAAA,KAAA,CAAA;AAAA,OAAA;AAC0D,MAAA,IAC/D,aAAgB,CAAA,SAAyB,YAAA,CAAA,KAAA;AAAsB,QACjE,OAAA,EAAA,CAAA;AAAA,MACF,IAAA,CAAA,aAAqB,CAAA,KAAA,IAAA,YAAgB,CAAA,KAAA;AACnC,QAAA,OAAO,EAAU,CAAA;AAAA,MACnB,IAAA,cAAA,EAAA;AACA,QAAA,OAAK,aAAsB,CAAA,KAAA,IAAA,cAAoB,CAAO,KAAA,IAAA,aAAA,CAAA,KAAA,GAAA,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,cAAA,CAAA;AACtD,OAAA;AACA,MAAA,OAAoB,EAAA,CAAA;AAClB,KAAO,CAAA,CAAA;AAEH,IACN,MAAA,gBAAA,GAAAL,YAAA,CAAA,MAAA,KAAA,CAAA,IAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AACA,IAAO,MAAA,YAAA,GAAAA,YAAA,CAAA,MAAA,KAAA,CAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,IACT,MAAC,aAAA,GAAAA,YAAA,CAAA,MAAA,KAAA,CAAA,IAAA,KAAA,OAAA,CAAA,CAAA;AAED,IAAA,MAAM,6BAA4B,CAAA,MAAA,UAAiB,KAAA;AAEnD,IAAA,MAAM,gBAAwBA,YAAA,CAAA,WAAY,CAAK,IAAA,KAAA;AAE/C,IAAA,MAAM,WAAgB,GAAAA,YAAA,CAAA,MAAe,KAAA,CAAA,eAAsB,gBAAA,CAAA,KAAA,GAAAM,cAAA,GAAAC,iBAAA,CAAA,CAAA,CAAA;AAE3D,IAAA,MAAM,YAAiB,GAAAP,YAAA,CAAA,MAAe,KAAA,CAAA,aAAuB,CAAA,cAAA,CAAA,KAAA,IAAA,CAAA,KAAA,CAAA,QAAA,IAAA,CAAA,YAAA,CAAA,KAAA,KAAA,QAAA,CAAA,KAAA,IAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAE7D,IAAA,MAAM,gBAAgB,GAAA,CAAA,KAAS,KAAM;AAErC,MAAA,IAAM,KAAc,CAAA,QAAA,IAAA,cAAA,CAAA,KAAA;AAAA,QACZ,OAAA;AAAsD,MAC9D,IAAA,YAAA,CAAA,KAAA,EAAA;AAEA,QAAA,KAAqB,CAAA,eAAA,EAAA,CAAA;AAAA,QAEjB,IAAA,aACA,CAAA,KAAA,CAAC;AAG4B,UACjC,aAAA,CAAA,KAAA,CAAA,WAAA,EAAA,CAAA;AAEA,SAAM,MAAA;AACJ,UAAI,SAAkB,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AACtB,SAAA;AACE,QAAA,UAAsB,CAAA,YAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AAGtB,QAAI,MAAA,EAAA,CAAA;AACF,OAAA;AAAgC,MAAA,IAC3B,CAAA,OAAA,CAAA,CAAA;AACL,KAAA,CAAA;AAA4B,IAC9B,MAAA,gBAAA,GAAA,OAAA,KAAA,KAAA;AACA,MAAW,IAAA,EAAA,CAAA;AACX,MAAO,IAAA,KAAA,CAAA,QAAA,IAAA,cAAA,CAAA,KAAA;AAAA,QACT,OAAA;AACA,MAAA,IAAA,CAAK,CAAO,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,MAAA,OAAA,IAAA,SAAA,CAAA,KAAA,EAAA;AAAA,QACd,aAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAEA,OAAM;AACJ,KAAI,CAAA;AACJ,IAAA,MAAK,YAAM,GAAoC,MAAA;AAC7C,MAAA,IAAA,KAAA,CAAA,QAAsB,IAAA,cAAA,CAAA,KAAA;AAAA,QACxB,OAAA;AAAA,MACF,IAAA,CAAA,YAAA,CAAA,KAAA,IAAA,KAAA,CAAA,SAAA,EAAA;AACA,QAAA,iBAAqB,IAAM,CAAA;AACzB,OAAI;AACJ,KAAA,CAAA;AACE,IAAA,MAAA,YAAiB,GAAA,MAAA;AAAA,MACnB,QAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAAA,KACF,CAAA;AACA,IAAA,MAAM,iBAAqB,GAAA,CAAA,KAAA,KAAA;AACzB,MAAA,IAAA,EAAA,CAAA;AAAiB,MACnB,IAAA,KAAA,CAAA,QAAA,IAAA,cAAA,CAAA,KAAA;AAEA,QAAM,OAAA;AACJ,MAAI,IAAA,CAAA,CAAA,EAAA,GAAkB,KAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,MAAA,KAAsB,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,MAAA,OAAA,IAAA,SAAA,CAAA,KAAA,EAAA;AAC5C,QACG,mBAAiB,QAAwB;AAG1C,OAAA;AAAsB,KACxB,CAAA;AAAA,IACF,MAAA,YAAA,GAAAA,YAAA,CAAA,MAAA;AAEA,MAAM,OAAA,KAAA,CAAA,IAAA,CAAA,gBAA8B,CAAA,CAAA;AAClC,KAAO,CAAA,CAAA;AAA2B,IACpC,MAAC,UAAA,GAAAQ,8BAAA,EAAA,CAAA;AAED,IAAA,MAAM,uBAAyB,CAAA,MAAA;AAE/B,MAAA,IAAM;AAEN,MAAA,OAAmB,CAAA,EAAA,GAAA,CAAA,EAAA,GAAAC,SAAA,CAAA,SAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAA,CAAA;AAAA,KACjB,CAAA,CAAA;AAAA,IAAA,MACqB,UAAA,GAAAC,mBAAA,CAAA,QAAA,EAAA,CAAA,CAAA,KAAA;AACnB,MAAM,MAAA,eAAA,GAAAD,kBAAgC,CAAA,CAAA;AACtC,MAAM,MAAA,OAAA,GAAAE,0BAA+D,CAAA,CAAA;AACrE,MAAA,IAAA,sBAEK,MAAE,KAAA,qBACD,YAAE,EAAA,CAAa,QAAW,CAAA,eAAA,CAAA,CAAe,IAC7C,CAAA,CAAA,kBACC,IAAA,OAAA,IAAA,CAAA,CAAW,YAAe,EAAA,CAAA,gBAAkB,CAAA;AAE7C,QAAA,OAAA;AACF,MAAA,aAAA,CAAA,KAAsB,GAAA,KAAA,CAAA;AAAA,KACxB,CAAA,CAAA;AAAA,IACFC,mBAAA,CAAA,MAAA;AAEA,MAAA,UAAA,IAAgB,IAAM,GAAA,KAAA,CAAA,GAAA,UAAA,EAAA,CAAA;AACpB,KAAa,CAAA,CAAA;AAAA,IACf,MAAC,YAAA,GAAA,MAAA;AAED,MAAA,IAAM,eAAe,EAAM;AACzB,QAAA,cAAqB,qBAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AACnB,QAAM,IAAA,KAAA,EAAA;AACN,UAAA,IAAW,YAAA,CAAA,KAAA,CAAA,EAAA;AACT,YAAI,SAAA,CAAAC,qBAAqB,CAAA,KAAA,CAAA,CAAA,CAAA;AACvB,YAAU,SAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACV,WAAA;AAAkB,SACpB;AAAA,OACF;AAAA,MACF,IAAA,SAAA,CAAA,KAAA,KAAA,EAAA,EAAA;AACA,QAAI,SAAA,CAAA,YAAoB,CAAI,KAAA,CAAA,CAAA;AAC1B,QAAA,UAAU,aAAa,CAAK,KAAA,EAAA,IAAA,CAAA,CAAA;AAC5B,QAAW,SAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACX,OAAA;AAAkB,KACpB,CAAA;AAAA,IACF,MAAA,qBAAA,GAAA,CAAA,KAAA,KAAA;AAEA,MAAM,IAAA,CAAA,KAAA;AACJ,QAAI,WAAe,CAAA;AACnB,MAAO,OAAA,aAAA,CAAc,KAAM,CAAA,cAAA,CAAgB,KAAK,CAAA,CAAA;AAAA,KAClD,CAAA;AAEA,IAAM,MAAA,mBAAA,GAAsB,CAAC,KAAqB,KAAA;AAChD,MAAI,IAAA,CAAC;AACL,QAAO,OAAA,IAAA,CAAA;AAAyC,MAClD,OAAA,aAAA,CAAA,KAAA,CAAA,cAAA,CAAA,KAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAO,MAAA,YAAA,GAAA,CAAA,KAAoB,KAAA;AAAmB,MAChD,OAAA,aAAA,CAAA,KAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAI,MAAA,kBAAkB,GAAA,OAAA,KAAe,KAAO;AAE5C,MAAM,IAAA,cAAW,IAAA,cAAA,CAAA,KAAA;AACjB,QAAA,OAAA;AACA,MAAI,MAAA,EAAA,IAAA;AACF,MAAI,WAAA,CAAA,KAAA,CAAA,CAAA;AACF,MAAA,IAAA,IAAA,KAAAC,eAAsB,CAAA,GAAA,EAAA;AACtB,QAAA,IAAA,aAAqB,CAAA,KAAA,KAAA,IAAA,EAAA;AACrB,UAAA,aAAsB,CAAA,KAAA,GAAA,KAAA,CAAA;AAAA,UACxB,KAAA,CAAA,cAAA,EAAA,CAAA;AACA,UAAA,KAAA,CAAA,eAAA,EAAA,CAAA;AAAA,SACF;AAEA,QAAI,OAAA;AACF,OAAI;AACF,MAAA,IAAA,IAAA,KAAqBA,eAAA,CAAA,IAAA,EAAA;AACrB,QAAA,IAAA,aAAsB,CAAA,KAAA,CAAA,iBAAA,EAAA;AAAA,UACxB,KAAA,CAAA,cAAA,EAAA,CAAA;AACA,UAAI,KAAA,CAAA;AACF,SAAA;AACA,QAAA,IAAA,aAAe,CAAA,KAAA,KAAA,KAAA,EAAA;AAAA,UACjB,aAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACA,UAAI,MAAAZ,YAAA,EAAc;AAChB,SAAA;AACA,QAAA,IAAA,aAAA,CAAA,KAAA,CAAA,iBAAA,EAAA;AAAA,UACF,aAAA,CAAA,KAAA,CAAA,iBAAA,EAAA,CAAA;AAAA,UACF,OAAA;AAEA,SAAI;AACF,OAAwB;AACxB,MAAA,IAAA,IAAA,KAAAY,eAAA,CAAA,GAAA,EAAA;AAAA,QACF,qBAAA,GAAA,IAAA,CAAA;AAEA,QAAA,OAAa;AACX,OACE;AAIA,MAAa,IAAA,IAAA,KAAAA,eAAA,CAAA,KAAA,IAAA,IAAA,KAAAA,eAAA,CAAA,WAAA,EAAA;AACb,QAAA,IAAA,SAAA,CAAA,KAAsB,KAAA,IAAA,IAAA,SAAA,CAAA,KAAA,KAAA,EAAA,IAAA,YAAA,CAAA,qBAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA,EAAA;AAAA,UACxB,YAAA,EAAA,CAAA;AACA,UAAA,aAAsB,CAAA,KAAA,GAAA,KAAA,CAAA;AACtB,SAAA;AAAA,QACF,KAAA,CAAA,eAAA,EAAA,CAAA;AAGA,QAAA;AACE,OAAA;AACA,MAAA,IAAA,SAAA,CAAA,KAAA,EAAA;AAAA,QACF,KAAA,CAAA,eAAA,EAAA,CAAA;AACA,QAAI,OAAA;AACF,OAAc;AAA+C,MAC/D,IAAA,aAAA,CAAA,KAAA,CAAA,kBAAA,EAAA;AAAA,QACF,aAAA,CAAA,KAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,CAAA;AACA,OAAM;AACJ,KAAA,CAAA;AAGA,IAAI,MAAA,eAAe,CAAO,KAAA;AACxB,MAAA,SAAA,CAAA,KAAA,GAAsB,CAAA,CAAA;AAAA,MACxB,IAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AAAA,QACF,aAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAEA,OAAM;AACJ,KAAA,CAAA;AACA,IAAA,MAAI,gBAAiB,GAAA,CAAA,KAAA,KAAA;AACnB,MAAA,MAAA,MAAU,QAAQ,CAAC,MAAA,CAAO;AAAyB,MACrD,IAAO,SAAA,CAAA,KAAA,EAAA;AACL,QAAA,SAAA,CAAU,KAAQ,GAAA,CAAC,MAAO,CAAA,KAAA,EAAO,SAAI,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,OACvC,MAAA;AAAA,QACF,SAAA,CAAA,KAAA,GAAA,CAAA,MAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AAEA,OAAM;AACJ,KAAA,CAAA;AACA,IAAA,MAAI,cAAiB,GAAA,CAAA,KAAA,KAAA;AACnB,MAAA,MAAA,MAAU,QAAQ,CAAC,MAAA,CAAA;AAAgC,MACrD,IAAO,SAAA,CAAA,KAAA,EAAA;AACL,QAAA,SAAA,CAAU,KAAQ,GAAA,CAAC,SAAM,CAAA,KAAY,CAAA,CAAA,CAAA,EAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OACvC,MAAA;AAAA,QACF,SAAA,CAAA,KAAA,GAAA,CAAA,IAAA,EAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AAEA,OAAA;AACE,KAAA,CAAA;AACA,IAAA,MAAA,iBAAc,GAAA,MAAA;AACd,MAAM,IAAA,EAAA,CAAA;AACN,MAAI,MAAA,MAAA,GAAe,SAAA,CAAA,KAAW,CAAA;AAC5B,MAAA,MAAA,KAAA,GAAkB,qBAAA,CAAA,MAAA,IAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,MAAA,4BACI,WAAK,CAAA,CAAA;AAAA,MACzB,IAAA,KAAA,IAAA,KAAqB,CAAA,OAAC,EAAK,EAAA;AAAA,QAC7B,SAAA,CAAA,KAAA,GAAA;AACA,UAAA,mBAAkB,CAAA;AAClB,UAAI,CAAA,CAAA,EAAA,GAAA,kBAAwB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,KAAA,IAAA;AAC1B,SAAU,CAAA;AACV,QAAA,MAAA,QAAkB,GAAA,CAAA,KAAA,EAAA,SAAA,KAAA,SAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA;AAAA,QACpB,IAAA,YAAA,CAAA,QAAA,CAAA,EAAA;AAAA,UACF,SAAA,CAAAD,qBAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AAAA,UACF,SAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAEA,SAAA;AACE,OAAM;AACN,KAAA,CAAA;AACA,IAAM,MAAA,wBAA6B;AACnC,MAAI,IAAA,EAAA,CAAA;AACF,MAAA,MAAA,MAAU,GAAQJ,SAAA,CAAA,SAAA,CAAA,CAAA;AAAA,MAAA,MACV,KAAA,GAAA,qBAAsB,CAAA,MAAA,IAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,MAAA,4BACR,WAAK,CAAA,CAAA;AAAA,MAC3B,IAAA,KAAA,IAAA,KAAA,CAAA,OAAA,EAAA,EAAA;AACA,QAAA,kBAAkB;AAClB,UAAI,CAAA,CAAA,EAAA,GAAAA,SAAA,CAAA,YAAwB,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,KAAA,IAAA;AAC1B,UAAU,mBAAA,CAAA,KAAA,CAAgB;AAC1B,SAAA,CAAA;AAAkB,QACpB,MAAA,QAAA,GAAA,CAAA,SAAA,IAAA,SAAA,CAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA;AAAA,QACF,IAAA,YAAA,CAAA,QAAA,CAAA,EAAA;AAAA,UACF,SAAA,CAAAI,qBAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AAEA,UAAM,SAAc,CAAA,KAAA,GAAA,IAAA,CAAA;AAClB,SAAA;AAAsB,OACxB;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,KAAS,SAAY;AAAA,MACvB,IAAA,EAAA,CAAA;AAEA,MAAA,CAAA,EAAA,GAAQ,QAA2B,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,EAAA,CAAA;AAAA,KACjC,CAAA;AAAA,IACF,MAAC,IAAA,GAAA,MAAA;AACD,MAAA,IAAA,EAAQ;AAER,MAAa,CAAA,EAAA,GAAA,QAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,EAAA,CAAA;AAAA,KAAA,CAAA;AAAA,IAAAE,WAAA,CAAAC,mCAAA,EAAA;AAAA,MAAA,KAAA;AAAA,KAIX,CAAA,CAAA;AAAA,IAAAD,WAAA,CAAAE,0CAAA,EAAA,YAAA,CAAA,CAAA;AAAA,IAAA,MAAA,CAAA;AAAA,MAAA,KAAA;AAAA,MAIA,IAAA;AAAA,MAAA,UAAA;AAAA,MAAA,WAAA;AAAA,MAAA,MAAA;AAAA,KAIA,CAAA,CAAA;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAAA,OAAAC,aAAA,EAAA,EAAAC,eAAA,CAAAC,SAAA,CAAAC,iBAAA,CAAA,EAAAC,cAAA,CAAA;AAAA,QAAA,OAAA,EAAA,WAAA;AAAA,QAIA,GAAA,EAAA,SAAA;AAAA,QAAA,OAAA,EAAAF,SAAA,CAAA,aAAA,CAAA;AAAA,QAAA,MAAA,EAAA,OAAA;AAAA,QAAA,IAAA,EAAA,EAAA;AAAA,QAIA,OAAA,EAAA,OAAA;AAAA,OACD,EAAA,IAAA,CAAA,MAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}