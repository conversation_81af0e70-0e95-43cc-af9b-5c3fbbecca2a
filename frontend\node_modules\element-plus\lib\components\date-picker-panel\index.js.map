{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/date-picker-panel/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport DatePickerPanel from './src/date-picker-panel'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElDatePickerPanel: SFCWithInstall<typeof DatePickerPanel> =\n  withInstall(DatePickerPanel)\n\nexport default ElDatePickerPanel\nexport * from './src/constants'\nexport * from './src/props/date-picker-panel'\nexport * from './src/types'\nexport type { DatePickerPanelInstance } from './src/instance'\n"], "names": ["withInstall", "DatePickerPanel"], "mappings": ";;;;;;;;;AAEY,MAAC,iBAAiB,GAAGA,mBAAW,CAACC,4BAAe;;;;;;;;"}