{"version": 3, "file": "basic-year-table.js", "sources": ["../../../../../../../packages/components/date-picker-panel/src/date-picker-com/basic-year-table.vue"], "sourcesContent": ["<template>\n  <table\n    role=\"grid\"\n    :aria-label=\"t('el.datepicker.yearTablePrompt')\"\n    :class=\"ns.b()\"\n    @click=\"handleYearTableClick\"\n    @mousemove=\"handleMouseMove\"\n  >\n    <tbody ref=\"tbodyRef\">\n      <tr v-for=\"(row, rowKey) in rows\" :key=\"rowKey\">\n        <td\n          v-for=\"(cell, cellKey) in row\"\n          :key=\"`${rowKey}_${cellKey}`\"\n          :ref=\"(el) => cell.isSelected && (currentCellRef = el as HTMLElement)\"\n          class=\"available\"\n          :class=\"getCellKls(cell)\"\n          :aria-selected=\"cell.isSelected\"\n          :aria-label=\"String(cell.text)\"\n          :tabindex=\"cell.isSelected ? 0 : -1\"\n          @keydown.space.prevent.stop=\"handleYearTableClick\"\n          @keydown.enter.prevent.stop=\"handleYearTableClick\"\n        >\n          <el-date-picker-cell :cell=\"cell\" />\n        </td>\n      </tr>\n    </tbody>\n  </table>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, nextTick, ref, watch } from 'vue'\nimport dayjs from 'dayjs'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { rangeArr } from '@element-plus/components/time-picker'\nimport { castArray, hasClass } from '@element-plus/utils'\nimport { basicYearTableProps } from '../props/basic-year-table'\nimport { getValidDateOfYear } from '../utils'\nimport ElDatePickerCell from './basic-cell-render'\n\nimport type { Dayjs } from 'dayjs'\n\ntype YearCell = {\n  column: number\n  customClass: string | undefined\n  disabled: boolean\n  end: boolean\n  inRange: boolean\n  row: number\n  selected: Dayjs | undefined\n  isCurrent: boolean | undefined\n  isSelected: boolean\n  start: boolean\n  text: number\n  renderText: string | undefined\n  timestamp: number | undefined\n  type: 'normal' | 'today'\n  date: Date | undefined\n  dayjs: Dayjs | undefined\n}\n\nconst datesInYear = (year: number, lang: string) => {\n  const firstDay = dayjs(String(year)).locale(lang).startOf('year')\n  const lastDay = firstDay.endOf('year')\n  const numOfDays = lastDay.dayOfYear()\n  return rangeArr(numOfDays).map((n) => firstDay.add(n, 'day').toDate())\n}\n\nconst props = defineProps(basicYearTableProps)\nconst emit = defineEmits(['changerange', 'pick', 'select'])\n\nconst ns = useNamespace('year-table')\n\nconst { t, lang } = useLocale()\nconst tbodyRef = ref<HTMLElement>()\nconst currentCellRef = ref<HTMLElement>()\nconst startYear = computed(() => {\n  return Math.floor(props.date.year() / 10) * 10\n})\n\nconst tableRows = ref<YearCell[][]>([[], [], []])\nconst lastRow = ref<number>()\nconst lastColumn = ref<number>()\nconst rows = computed(() => {\n  const rows = tableRows.value\n  const now = dayjs().locale(lang.value).startOf('year')\n\n  for (let i = 0; i < 3; i++) {\n    const row = rows[i]\n    for (let j = 0; j < 4; j++) {\n      if (i * 4 + j >= 10) {\n        break\n      }\n      let cell = row[j]\n      if (!cell) {\n        cell = {\n          row: i,\n          column: j,\n          type: 'normal',\n          inRange: false,\n          start: false,\n          end: false,\n          text: -1,\n          disabled: false,\n          isSelected: false,\n          customClass: undefined,\n          date: undefined,\n          dayjs: undefined,\n          isCurrent: undefined,\n          selected: undefined,\n          renderText: undefined,\n          timestamp: undefined,\n        }\n      }\n      cell.type = 'normal'\n      const index = i * 4 + j + startYear.value\n      const calTime = dayjs().year(index)\n\n      const calEndDate =\n        props.rangeState.endDate ||\n        props.maxDate ||\n        (props.rangeState.selecting && props.minDate) ||\n        null\n\n      cell.inRange =\n        !!(\n          props.minDate &&\n          calTime.isSameOrAfter(props.minDate, 'year') &&\n          calEndDate &&\n          calTime.isSameOrBefore(calEndDate, 'year')\n        ) ||\n        !!(\n          props.minDate &&\n          calTime.isSameOrBefore(props.minDate, 'year') &&\n          calEndDate &&\n          calTime.isSameOrAfter(calEndDate, 'year')\n        )\n\n      if (props.minDate?.isSameOrAfter(calEndDate)) {\n        cell.start = !!(calEndDate && calTime.isSame(calEndDate, 'year'))\n        cell.end = !!(props.minDate && calTime.isSame(props.minDate, 'year'))\n      } else {\n        cell.start = !!(props.minDate && calTime.isSame(props.minDate, 'year'))\n        cell.end = !!(calEndDate && calTime.isSame(calEndDate, 'year'))\n      }\n\n      const isToday = now.isSame(calTime)\n      if (isToday) {\n        cell.type = 'today'\n      }\n      cell.text = index\n      const cellDate = calTime.toDate()\n      cell.disabled = props.disabledDate?.(cellDate) || false\n      cell.date = cellDate\n      cell.customClass = props.cellClassName?.(cellDate)\n      cell.dayjs = calTime\n      cell.timestamp = calTime.valueOf()\n      cell.isSelected = isSelectedCell(cell)\n      row[j] = cell\n    }\n  }\n  return rows\n})\n\nconst focus = () => {\n  currentCellRef.value?.focus()\n}\n\nconst getCellKls = (cell: YearCell) => {\n  const kls: Record<string, boolean> = {}\n  const today = dayjs().locale(lang.value)\n  const year = cell.text\n\n  kls.disabled =\n    props.disabled ||\n    (props.disabledDate\n      ? datesInYear(year, lang.value).every(props.disabledDate)\n      : false)\n\n  kls.today = today.year() === year\n  kls.current =\n    castArray(props.parsedValue).findIndex((d) => d!.year() === year) >= 0\n\n  if (cell.customClass) {\n    kls[cell.customClass] = true\n  }\n  if (cell.inRange) {\n    kls['in-range'] = true\n\n    if (cell.start) {\n      kls['start-date'] = true\n    }\n\n    if (cell.end) {\n      kls['end-date'] = true\n    }\n  }\n  return kls\n}\n\nconst isSelectedCell = (cell: YearCell) => {\n  const year = cell.text\n  return castArray(props.date).findIndex((date) => date.year() === year) >= 0\n}\n\nconst handleYearTableClick = (event: MouseEvent | KeyboardEvent) => {\n  if (props.disabled) return\n  const target = (event.target as HTMLElement)?.closest(\n    'td'\n  ) as HTMLTableCellElement\n  if (!target || !target.textContent || hasClass(target, 'disabled')) return\n\n  const column = target.cellIndex\n  const row = (target.parentNode as HTMLTableRowElement).rowIndex\n  const selectedYear = row * 4 + column + startYear.value\n  const newDate = dayjs().year(selectedYear)\n  if (props.selectionMode === 'range') {\n    if (!props.rangeState.selecting) {\n      emit('pick', { minDate: newDate, maxDate: null })\n      emit('select', true)\n    } else {\n      if (props.minDate && newDate >= props.minDate) {\n        emit('pick', { minDate: props.minDate, maxDate: newDate })\n      } else {\n        emit('pick', { minDate: newDate, maxDate: props.minDate })\n      }\n      emit('select', false)\n    }\n  } else if (props.selectionMode === 'years') {\n    if (event.type === 'keydown') {\n      emit('pick', castArray(props.parsedValue), false)\n      return\n    }\n    const vaildYear = getValidDateOfYear(\n      newDate.startOf('year'),\n      lang.value,\n      props.disabledDate\n    )\n    const newValue = hasClass(target, 'current')\n      ? castArray(props.parsedValue).filter((d) => d?.year() !== selectedYear)\n      : castArray(props.parsedValue).concat([vaildYear])\n    emit('pick', newValue)\n  } else {\n    emit('pick', selectedYear)\n  }\n}\n\nconst handleMouseMove = (event: MouseEvent) => {\n  if (!props.rangeState.selecting) return\n  const target = (event.target as HTMLElement)?.closest(\n    'td'\n  ) as HTMLTableCellElement\n  if (!target) return\n\n  const row = (target.parentNode as HTMLTableRowElement).rowIndex\n  const column = (target as HTMLTableCellElement).cellIndex\n\n  // can not select disabled date\n  if (rows.value[row][column].disabled) return\n\n  // only update rangeState when mouse moves to a new cell\n  // this avoids frequent Date object creation and improves performance\n  if (row !== lastRow.value || column !== lastColumn.value) {\n    lastRow.value = row\n    lastColumn.value = column\n    emit('changerange', {\n      selecting: true,\n      endDate: dayjs()\n        .year(startYear.value)\n        .add(row * 4 + column, 'year'),\n    })\n  }\n}\n\nwatch(\n  () => props.date,\n  async () => {\n    if (tbodyRef.value?.contains(document.activeElement)) {\n      await nextTick()\n      currentCellRef.value?.focus()\n    }\n  }\n)\n\ndefineExpose({\n  /**\n   * @description focus on the current cell\n   */\n  focus,\n})\n</script>\n"], "names": ["lang", "dayjs", "rangeArr", "useNamespace", "useLocale", "ref", "computed", "rows", "<PERSON><PERSON><PERSON><PERSON>", "hasClass", "getValidDateOfYear", "watch", "nextTick", "_openBlock", "_createElementBlock", "_unref"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AA4DA,IAAM,MAAA,WAAA,GAAc,CAAC,IAAA,EAAcA,KAAiB,KAAA;AAClD,MAAM,MAAA,QAAA,GAAWC,yBAAM,CAAA,MAAA,CAAO,IAAI,CAAC,EAAE,MAAOD,CAAAA,KAAI,CAAE,CAAA,OAAA,CAAQ,MAAM,CAAA,CAAA;AAChE,MAAM,MAAA,OAAA,GAAU,QAAS,CAAA,KAAA,CAAM,MAAM,CAAA,CAAA;AACrC,MAAM,MAAA,SAAA,GAAY,QAAQ,SAAU,EAAA,CAAA;AACpC,MAAA,OAAOE,cAAS,CAAA,SAAS,CAAE,CAAA,GAAA,CAAI,CAAC,CAAA,KAAM,QAAS,CAAA,GAAA,CAAI,CAAG,EAAA,KAAK,CAAE,CAAA,MAAA,EAAQ,CAAA,CAAA;AAAA,KACvE,CAAA;AAKA,IAAM,MAAA,EAAA,GAAKC,mBAAa,YAAY,CAAA,CAAA;AAEpC,IAAA,MAAM,EAAE,CAAA,EAAG,IAAK,EAAA,GAAIC,iBAAU,EAAA,CAAA;AAC9B,IAAA,MAAM,WAAWC,OAAiB,EAAA,CAAA;AAClC,IAAA,MAAM,iBAAiBA,OAAiB,EAAA,CAAA;AACxC,IAAM,MAAA,SAAA,GAAYC,aAAS,MAAM;AAC/B,MAAA,OAAO,KAAK,KAAM,CAAA,KAAA,CAAM,KAAK,IAAK,EAAA,GAAI,EAAE,CAAI,GAAA,EAAA,CAAA;AAAA,KAC7C,CAAA,CAAA;AAED,IAAM,MAAA,SAAA,GAAYD,QAAkB,CAAC,IAAI,EAAC,EAAG,EAAE,CAAC,CAAA,CAAA;AAChD,IAAA,MAAM,UAAUA,OAAY,EAAA,CAAA;AAC5B,IAAA,MAAM,aAAaA,OAAY,EAAA,CAAA;AAC/B,IAAM,MAAA,IAAA,GAAOC,aAAS,MAAM;AAC1B,MAAA,IAAA,EAAMC,QAAO,CAAU;AACvB,MAAM,MAAA,KAAA,YAAc,CAAA;AAEpB,MAAA,MAAA,GAAS,GAAIN,yBAAO,EAAA,CAAA,MAAQ,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA;AAC1B,MAAM,KAAA,IAAA,CAAA,GAAA,CAAMM,MAAK,CAAC,EAAA,CAAA,EAAA,EAAA;AAClB,QAAA,MAAA,GAAS,GAAI,KAAO,CAAA,CAAA,CAAA,CAAA;AAClB,QAAI,KAAA,IAAA,CAAI,GAAI,CAAA,EAAA,CAAA,GAAS,CAAA,EAAA,CAAA,EAAA,EAAA;AACnB,UAAA,IAAA,CAAA,GAAA,CAAA,GAAA,CAAA,IAAA,EAAA,EAAA;AAAA,YACF,MAAA;AACA,WAAI;AACJ,UAAA,IAAI,IAAO,GAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AACT,UAAO,IAAA,CAAA,IAAA,EAAA;AAAA,YAAA,IACA,GAAA;AAAA,cACL,GAAQ,EAAA,CAAA;AAAA,cACR,MAAM,EAAA,CAAA;AAAA,cACN,IAAS,EAAA,QAAA;AAAA,cACT,OAAO,EAAA,KAAA;AAAA,cACP,KAAK,EAAA,KAAA;AAAA,cACL,GAAM,EAAA,KAAA;AAAA,cACN,IAAU,EAAA,CAAA,CAAA;AAAA,cACV,QAAY,EAAA,KAAA;AAAA,cACZ,UAAa,EAAA,KAAA;AAAA,cACb,WAAM,EAAA,KAAA,CAAA;AAAA,cACN,IAAO,EAAA,KAAA,CAAA;AAAA,cACP,KAAW,EAAA,KAAA,CAAA;AAAA,cACX,SAAU,EAAA,KAAA,CAAA;AAAA,cACV,QAAY,EAAA,KAAA,CAAA;AAAA,cACZ,UAAW,EAAA,KAAA,CAAA;AAAA,cACb,SAAA,EAAA,KAAA,CAAA;AAAA,aACF,CAAA;AACA,WAAA;AACA,UAAA,IAAA,CAAA,IAAc,GAAA,QAAQ,CAAA;AACtB,UAAA,MAAM,KAAU,GAAA,CAAA,GAAA,CAAA,GAAM,CAAE,GAAA,SAAU,CAAA,KAAA,CAAA;AAElC,UAAM,MAAA,OAAA,GAAAN,yBACE,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAW;AAKnB,UAAA,MACE,UAAC,GACC,KAAM,CACN,UAAA,CAAA,OAAQ,IAAc,KAAA,CAAA,OAAA,IAAe,KAAA,CAAA,UACrC,CAAA,SAAA,IAAA,aACuB,IAAA,IAAA,CAAA;AAS3B,UAAA,IAAI,CAAM,OAAA,GAAA,CAAA,EAAA,KAAuB,CAAA,OAAA,IAAA,OAAA,CAAU,aAAG,CAAA,KAAA,CAAA,OAAA,EAAA,MAAA,CAAA,IAAA,UAAA,IAAA,OAAA,CAAA,cAAA,CAAA,UAAA,EAAA,MAAA,CAAA,CAAA,IAAA,CAAA,EAAA,KAAA,CAAA,OAAA,IAAA,OAAA,CAAA,cAAA,CAAA,KAAA,CAAA,OAAA,EAAA,MAAA,CAAA,IAAA,UAAA,IAAA,OAAA,CAAA,aAAA,CAAA,UAAA,EAAA,MAAA,CAAA,CAAA,CAAA;AAC5C,UAAA,IAAA,CAAA,EAAK,QAAQ,CAAC,gBAAgB,GAAQ,KAAA,CAAA,GAAA,EAAA,CAAO,aAAkB,CAAA,UAAA,CAAA,EAAA;AAC/D,YAAK,IAAA,CAAA,KAAA,GAAS,CAAA,EAAA,qBAAyB,CAAA,MAAA,CAAA,UAAa,EAAA,MAAe,CAAA,CAAA,CAAA;AAAA,YAC9D,IAAA,CAAA,GAAA,GAAA,CAAA,EAAA,KAAA,CAAA,OAAA,IAAA,OAAA,CAAA,MAAA,CAAA,KAAA,CAAA,OAAA,EAAA,MAAA,CAAA,CAAA,CAAA;AACL,WAAK,MAAA;AACL,YAAA,IAAA,CAAK,QAAO,CAAE,eAAsB,IAAA,OAAA,CAAA,oBAAyB,EAAA,MAAA,CAAA,CAAA,CAAA;AAAA,YAC/D,IAAA,CAAA,GAAA,GAAA,CAAA,EAAA,UAAA,IAAA,OAAA,CAAA,MAAA,CAAA,UAAA,EAAA,MAAA,CAAA,CAAA,CAAA;AAEA,WAAM;AACN,UAAA,MAAa,OAAA,GAAA,GAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA;AACX,UAAA,IAAA,OAAY,EAAA;AAAA,YACd,IAAA,CAAA,IAAA,GAAA,OAAA,CAAA;AACA,WAAA;AACA,UAAM,IAAA,CAAA,IAAA,GAAA,KAAW;AACjB,UAAA,MAAgB,QAAA,GAAA,OAAqB,CAAA,MAAA,EAAA,CAAA;AACrC,UAAA,IAAA,CAAK,QAAO,GAAA,CAAA,CAAA,EAAA,GAAA,KAAA,CAAA,YAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA,QAAA,CAAA,KAAA,KAAA,CAAA;AACZ,UAAK,IAAA,CAAA,IAAA,GAAA,QAAoB,CAAA;AACzB,UAAA,IAAA,CAAK,WAAQ,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,aAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA,QAAA,CAAA,CAAA;AACb,UAAK,IAAA,CAAA,KAAA,GAAA;AACL,UAAK,IAAA,CAAA,SAAA,GAAA,iBAAgC,CAAA;AACrC,UAAA,IAAI,CAAC,UAAI,GAAA,cAAA,CAAA,IAAA,CAAA,CAAA;AAAA,UACX,GAAA,CAAA,CAAA,CAAA,GAAA,IAAA,CAAA;AAAA,SACF;AACA,OAAOM;AAAA,MACR,OAAA,KAAA,CAAA;AAED,KAAA,CAAA,CAAA;AACE,IAAA,MAAA,KAAA,GAAA;AAA4B,MAC9B,IAAA,EAAA,CAAA;AAEA,MAAM,CAAA,EAAA,GAAA,cAAiC,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,EAAA,CAAA;AACrC,KAAA,CAAA;AACA,IAAA,MAAA,UAAc,GAAA,CAAA,IAAQ,KAAA;AACtB,MAAA,MAAM,QAAY,CAAA;AAElB,MAAA,MACE,KAAA,GAAAN,yBAAA,EAAM,CACL,MAAA,CAAA,IAAA,CAAA,KAAA,CAAM,CACH;AAGN,MAAI,MAAA,IAAA,GAAc,IAAA,CAAA,IAAA,CAAK;AACvB,MAAA,GAAA,CAAI,QACF,GAAA,KAAA,CAAA,QAAgB,KAAA,KAAA,CAAA,YAAa,GAAW,WAAS,CAAK,IAAM,EAAA,IAAI,CAAK,KAAA,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA,YAAA,CAAA,GAAA,KAAA,CAAA,CAAA;AAEvE,MAAA,GAAA,CAAI,KAAK,GAAa,KAAA,CAAA,IAAA,EAAA,KAAA,IAAA,CAAA;AACpB,MAAI,GAAA,CAAA,OAAK,mBAAe,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,IAAA,EAAA,KAAA,IAAA,CAAA,IAAA,CAAA,CAAA;AAAA,MAC1B,IAAA,IAAA,CAAA,WAAA,EAAA;AACA,QAAA,QAAkB,CAAA,WAAA,CAAA,GAAA,IAAA,CAAA;AAChB,OAAA;AAEA,MAAA,IAAA,YAAgB,EAAA;AACd,QAAA,GAAA,CAAA,cAAgB,IAAI,CAAA;AAAA,QACtB,IAAA,IAAA,CAAA,KAAA,EAAA;AAEA,UAAA,gBAAc,CAAA,GAAA,IAAA,CAAA;AACZ,SAAA;AAAkB,QACpB,IAAA,IAAA,CAAA,GAAA,EAAA;AAAA,UACF,GAAA,CAAA,UAAA,CAAA,GAAA,IAAA,CAAA;AACA,SAAO;AAAA,OACT;AAEA,MAAM,OAAA,GAAA,CAAA;AACJ,KAAA,CAAA;AACA,IAAO,MAAA,cAAgB,GAAA,CAAA,IAAA,KAAM;AAA6C,MAC5E,MAAA,IAAA,GAAA,IAAA,CAAA,IAAA,CAAA;AAEA,MAAM,OAAAO,gBAAA,CAAA,KAAA,CAAA,IAAuB,CAAC,CAAsC,SAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,IAAA,EAAA,KAAA,IAAA,CAAA,IAAA,CAAA,CAAA;AAClE,KAAA,CAAA;AACA,IAAM,MAAA,oBAAwC,GAAA,CAAA,KAAA,KAAA;AAAA,MAC5C,IAAA,EAAA,CAAA;AAAA,MACF,IAAA,KAAA,CAAA,QAAA;AACA,QAAI;AAEJ,MAAA,MAAM,SAAS,CAAO,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA;AACtB,MAAM,IAAA,CAAA,MAAA,WAAiD,CAAA,WAAA,IAAAC,cAAA,CAAA,MAAA,EAAA,UAAA,CAAA;AACvD,QAAA,OAAqB;AACrB,MAAA,MAAM,MAAU,GAAA,MAAA,CAAM,SAAmB,CAAA;AACzC,MAAI,MAAA,GAAA,oBAAiC,CAAA,QAAA,CAAA;AACnC,MAAI,MAAA,YAAO,GAAA,GAAW,GAAW,CAAA,GAAA,MAAA,GAAA,SAAA,CAAA,KAAA,CAAA;AAC/B,MAAA,MAAA,mCAAe,EAAA,CAAA,IAAS,CAAS,YAAA,CAAA,CAAA;AACjC,MAAA,IAAA,KAAK,cAAc,KAAA,OAAA,EAAA;AAAA,QACrB,IAAO,CAAA,KAAA,CAAA,UAAA,CAAA,SAAA,EAAA;AACL,UAAA,IAAI,CAAM,MAAA,EAAA,EAAA,OAAsB,EAAA,OAAA,EAAA,OAAe,EAAA,IAAA,EAAA,CAAA,CAAA;AAC7C,UAAA,IAAA,CAAA,UAAa,IAAE,CAAA,CAAA;AAA0C,SAAA,MACpD;AACL,UAAA,IAAA,aAAe,IAAA,gBAA2B,CAAA,OAAA,EAAA;AAAe,YAC3D,IAAA,CAAA,MAAA,EAAA,EAAA,OAAA,EAAA,KAAA,CAAA,OAAA,EAAA,OAAA,EAAA,OAAA,EAAA,CAAA,CAAA;AACA,WAAA;AAAoB,YACtB,IAAA,CAAA,MAAA,EAAA,EAAA,OAAA,EAAA,OAAA,EAAA,OAAA,EAAA,KAAA,CAAA,OAAA,EAAA,CAAA,CAAA;AAAA,WACF;AACE,UAAI,IAAA,CAAA,eAA0B,CAAA,CAAA;AAC5B,SAAA;AACA,OAAA,MAAA,IAAA,KAAA,CAAA,aAAA,KAAA,OAAA,EAAA;AAAA,QACF,IAAA,KAAA,CAAA,IAAA,KAAA,SAAA,EAAA;AACA,UAAA,IAAM,CAAY,MAAA,EAAAD,gBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,EAAA,KAAA,CAAA,CAAA;AAAA,UAChB,OAAA;AAAsB,SAAA;AACjB,QAAA,MACC,SAAA,GAAAE,0BAAA,CAAA,OAAA,CAAA,OAAA,CAAA,MAAA,CAAA,EAAA,IAAA,CAAA,KAAA,EAAA,KAAA,CAAA,YAAA,CAAA,CAAA;AAAA,QACR,MAAA,QAAA,GAAAD,cAAA,CAAA,MAAA,EAAA,SAAA,CAAA,GAAAD,gBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,IAAA,EAAA,MAAA,YAAA,CAAA,GAAAA,gBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA,MAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA;AACA,QAAM,IAAA,CAAA,MAAA,EAAA,QAAoB,CAAA,CAAA;AAG1B,OAAA,MAAK;AAAgB,QAChB,IAAA,CAAA,MAAA,EAAA,YAAA,CAAA,CAAA;AACL,OAAA;AAAyB,KAC3B,CAAA;AAAA,IACF,MAAA,eAAA,GAAA,CAAA,KAAA,KAAA;AAEA,MAAM,IAAA,EAAA,CAAA;AACJ,MAAI,IAAA,CAAC,KAAM,CAAA,UAAA,CAAW,SAAW;AACjC,QAAM,OAAA;AAAwC,MAC5C,MAAA,MAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA;AAAA,MACF,IAAA,CAAA,MAAA;AACA,QAAA,OAAa;AAEb,MAAM,MAAA,GAAA,GAAO,OAAO,UAAmC,CAAA,QAAA,CAAA;AACvD,MAAA,MAAM,SAAU,MAAgC,CAAA,SAAA,CAAA;AAGhD,MAAA,IAAI,KAAK,KAAM,CAAA,GAAG,CAAE,CAAA,MAAM,EAAE,QAAU;AAItC,QAAA,OAAY;AACV,MAAA,IAAA,GAAA,KAAgB,OAAA,CAAA,KAAA,IAAA,MAAA,KAAA,UAAA,CAAA,KAAA,EAAA;AAChB,QAAA,OAAA,CAAA,KAAmB,GAAA,GAAA,CAAA;AACnB,QAAA,UAAoB,CAAA,KAAA,GAAA,MAAA,CAAA;AAAA,QAAA,IACP,CAAA,aAAA,EAAA;AAAA,UACX,SAAS,EAAM,IAAA;AAEgB,UAChC,OAAA,EAAAP,yBAAA,EAAA,CAAA,IAAA,CAAA,SAAA,CAAA,KAAA,CAAA,CAAA,GAAA,CAAA,GAAA,GAAA,CAAA,GAAA,MAAA,EAAA,MAAA,CAAA;AAAA,SACH,CAAA,CAAA;AAAA,OACF;AAEA,KAAA,CAAA;AAAA,IAAAU,gBACc,KAAA,CAAA,IAAA,EAAA,YAAA;AAAA,MACZ,IAAY,EAAA,EAAA,EAAA,CAAA;AACV,MAAA,IAAA,CAAA,EAAa,GAAA,QAAA,CAAA,KAAgB,KAAA,IAAA,GAAA,oBAAyB,CAAA,QAAA,CAAA,aAAA,CAAA,EAAA;AACpD,QAAA,MAAAC,YAAe,EAAA,CAAA;AACf,QAAA,CAAA,EAAA,GAAA,oBAA4B,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,EAAA,CAAA;AAAA,OAC9B;AAAA,KACF,CAAA,CAAA;AAAA,IACF,MAAA,CAAA;AAEA,MAAa,KAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAAA,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,OAAA,EAAA;AAAA,QAIX,IAAA,EAAA,MAAA;AAAA,QACD,YAAA,EAAAC,SAAA,CAAA,CAAA,CAAA,CAAA,+BAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}