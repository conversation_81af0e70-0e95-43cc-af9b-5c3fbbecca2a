{"version": 3, "file": "input.js", "sources": ["../../../../../../packages/components/input/src/input.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[\n      containerKls,\n      {\n        [nsInput.bm('group', 'append')]: $slots.append,\n        [nsInput.bm('group', 'prepend')]: $slots.prepend,\n      },\n    ]\"\n    :style=\"containerStyle\"\n    @mouseenter=\"handleMouseEnter\"\n    @mouseleave=\"handleMouseLeave\"\n  >\n    <!-- input -->\n    <template v-if=\"type !== 'textarea'\">\n      <!-- prepend slot -->\n      <div v-if=\"$slots.prepend\" :class=\"nsInput.be('group', 'prepend')\">\n        <slot name=\"prepend\" />\n      </div>\n\n      <div ref=\"wrapperRef\" :class=\"wrapperKls\">\n        <!-- prefix slot -->\n        <span v-if=\"$slots.prefix || prefixIcon\" :class=\"nsInput.e('prefix')\">\n          <span :class=\"nsInput.e('prefix-inner')\">\n            <slot name=\"prefix\" />\n            <el-icon v-if=\"prefixIcon\" :class=\"nsInput.e('icon')\">\n              <component :is=\"prefixIcon\" />\n            </el-icon>\n          </span>\n        </span>\n\n        <input\n          :id=\"inputId\"\n          ref=\"input\"\n          :class=\"nsInput.e('inner')\"\n          v-bind=\"attrs\"\n          :name=\"name\"\n          :minlength=\"minlength\"\n          :maxlength=\"maxlength\"\n          :type=\"showPassword ? (passwordVisible ? 'text' : 'password') : type\"\n          :disabled=\"inputDisabled\"\n          :readonly=\"readonly\"\n          :autocomplete=\"autocomplete\"\n          :tabindex=\"tabindex\"\n          :aria-label=\"ariaLabel\"\n          :placeholder=\"placeholder\"\n          :style=\"inputStyle\"\n          :form=\"form\"\n          :autofocus=\"autofocus\"\n          :role=\"containerRole\"\n          :inputmode=\"inputmode\"\n          @compositionstart=\"handleCompositionStart\"\n          @compositionupdate=\"handleCompositionUpdate\"\n          @compositionend=\"handleCompositionEnd\"\n          @input=\"handleInput\"\n          @change=\"handleChange\"\n          @keydown=\"handleKeydown\"\n        />\n\n        <!-- suffix slot -->\n        <span v-if=\"suffixVisible\" :class=\"nsInput.e('suffix')\">\n          <span :class=\"nsInput.e('suffix-inner')\">\n            <template\n              v-if=\"!showClear || !showPwdVisible || !isWordLimitVisible\"\n            >\n              <slot name=\"suffix\" />\n              <el-icon v-if=\"suffixIcon\" :class=\"nsInput.e('icon')\">\n                <component :is=\"suffixIcon\" />\n              </el-icon>\n            </template>\n            <el-icon\n              v-if=\"showClear\"\n              :class=\"[nsInput.e('icon'), nsInput.e('clear')]\"\n              @mousedown.prevent=\"NOOP\"\n              @click=\"clear\"\n            >\n              <component :is=\"clearIcon\" />\n            </el-icon>\n            <el-icon\n              v-if=\"showPwdVisible\"\n              :class=\"[nsInput.e('icon'), nsInput.e('password')]\"\n              @click=\"handlePasswordVisible\"\n            >\n              <component :is=\"passwordIcon\" />\n            </el-icon>\n            <span v-if=\"isWordLimitVisible\" :class=\"nsInput.e('count')\">\n              <span :class=\"nsInput.e('count-inner')\">\n                {{ textLength }} / {{ maxlength }}\n              </span>\n            </span>\n            <el-icon\n              v-if=\"validateState && validateIcon && needStatusIcon\"\n              :class=\"[\n                nsInput.e('icon'),\n                nsInput.e('validateIcon'),\n                nsInput.is('loading', validateState === 'validating'),\n              ]\"\n            >\n              <component :is=\"validateIcon\" />\n            </el-icon>\n          </span>\n        </span>\n      </div>\n\n      <!-- append slot -->\n      <div v-if=\"$slots.append\" :class=\"nsInput.be('group', 'append')\">\n        <slot name=\"append\" />\n      </div>\n    </template>\n\n    <!-- textarea -->\n    <template v-else>\n      <textarea\n        :id=\"inputId\"\n        ref=\"textarea\"\n        :class=\"[nsTextarea.e('inner'), nsInput.is('focus', isFocused)]\"\n        v-bind=\"attrs\"\n        :minlength=\"minlength\"\n        :maxlength=\"maxlength\"\n        :tabindex=\"tabindex\"\n        :disabled=\"inputDisabled\"\n        :readonly=\"readonly\"\n        :autocomplete=\"autocomplete\"\n        :style=\"textareaStyle\"\n        :aria-label=\"ariaLabel\"\n        :placeholder=\"placeholder\"\n        :form=\"form\"\n        :autofocus=\"autofocus\"\n        :rows=\"rows\"\n        :role=\"containerRole\"\n        @compositionstart=\"handleCompositionStart\"\n        @compositionupdate=\"handleCompositionUpdate\"\n        @compositionend=\"handleCompositionEnd\"\n        @input=\"handleInput\"\n        @focus=\"handleFocus\"\n        @blur=\"handleBlur\"\n        @change=\"handleChange\"\n        @keydown=\"handleKeydown\"\n      />\n      <span\n        v-if=\"isWordLimitVisible\"\n        :style=\"countStyle\"\n        :class=\"nsInput.e('count')\"\n      >\n        {{ textLength }} / {{ maxlength }}\n      </span>\n    </template>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  nextTick,\n  onMounted,\n  ref,\n  shallowRef,\n  toRef,\n  useAttrs as useRawAttrs,\n  useSlots,\n  watch,\n} from 'vue'\nimport { useResizeObserver } from '@vueuse/core'\nimport { isNil } from 'lodash-unified'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { Hide as IconHide, View as IconView } from '@element-plus/icons-vue'\nimport {\n  useFormDisabled,\n  useFormItem,\n  useFormItemInputId,\n  useFormSize,\n} from '@element-plus/components/form'\nimport {\n  NOOP,\n  ValidateComponentsMap,\n  debugWarn,\n  isClient,\n  isObject,\n} from '@element-plus/utils'\nimport {\n  useAttrs,\n  useComposition,\n  useCursor,\n  useFocusController,\n  useNamespace,\n} from '@element-plus/hooks'\nimport {\n  CHANGE_EVENT,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { calcTextareaHeight } from './utils'\nimport { inputEmits, inputProps } from './input'\n\nimport type { StyleValue } from 'vue'\n\ntype TargetElement = HTMLInputElement | HTMLTextAreaElement\n\nconst COMPONENT_NAME = 'ElInput'\ndefineOptions({\n  name: COMPONENT_NAME,\n  inheritAttrs: false,\n})\nconst props = defineProps(inputProps)\nconst emit = defineEmits(inputEmits)\n\nconst rawAttrs = useRawAttrs()\nconst attrs = useAttrs()\nconst slots = useSlots()\n\nconst containerKls = computed(() => [\n  props.type === 'textarea' ? nsTextarea.b() : nsInput.b(),\n  nsInput.m(inputSize.value),\n  nsInput.is('disabled', inputDisabled.value),\n  nsInput.is('exceed', inputExceed.value),\n  {\n    [nsInput.b('group')]: slots.prepend || slots.append,\n    [nsInput.m('prefix')]: slots.prefix || props.prefixIcon,\n    [nsInput.m('suffix')]:\n      slots.suffix || props.suffixIcon || props.clearable || props.showPassword,\n    [nsInput.bm('suffix', 'password-clear')]:\n      showClear.value && showPwdVisible.value,\n    [nsInput.b('hidden')]: props.type === 'hidden',\n  },\n  rawAttrs.class,\n])\n\nconst wrapperKls = computed(() => [\n  nsInput.e('wrapper'),\n  nsInput.is('focus', isFocused.value),\n])\n\nconst { form: elForm, formItem: elFormItem } = useFormItem()\nconst { inputId } = useFormItemInputId(props, {\n  formItemContext: elFormItem,\n})\nconst inputSize = useFormSize()\nconst inputDisabled = useFormDisabled()\nconst nsInput = useNamespace('input')\nconst nsTextarea = useNamespace('textarea')\n\nconst input = shallowRef<HTMLInputElement>()\nconst textarea = shallowRef<HTMLTextAreaElement>()\n\nconst hovering = ref(false)\nconst passwordVisible = ref(false)\nconst countStyle = ref<StyleValue>()\nconst textareaCalcStyle = shallowRef(props.inputStyle)\n\nconst _ref = computed(() => input.value || textarea.value)\n\n// wrapperRef for type=\"text\", handleFocus and handleBlur for type=\"textarea\"\nconst { wrapperRef, isFocused, handleFocus, handleBlur } = useFocusController(\n  _ref,\n  {\n    disabled: inputDisabled,\n    afterBlur() {\n      if (props.validateEvent) {\n        elFormItem?.validate?.('blur').catch((err) => debugWarn(err))\n      }\n    },\n  }\n)\n\nconst needStatusIcon = computed(() => elForm?.statusIcon ?? false)\nconst validateState = computed(() => elFormItem?.validateState || '')\nconst validateIcon = computed(\n  () => validateState.value && ValidateComponentsMap[validateState.value]\n)\nconst passwordIcon = computed(() =>\n  passwordVisible.value ? IconView : IconHide\n)\nconst containerStyle = computed<StyleValue>(() => [\n  rawAttrs.style as StyleValue,\n])\nconst textareaStyle = computed<StyleValue>(() => [\n  props.inputStyle,\n  textareaCalcStyle.value,\n  { resize: props.resize },\n])\nconst nativeInputValue = computed(() =>\n  isNil(props.modelValue) ? '' : String(props.modelValue)\n)\nconst showClear = computed(\n  () =>\n    props.clearable &&\n    !inputDisabled.value &&\n    !props.readonly &&\n    !!nativeInputValue.value &&\n    (isFocused.value || hovering.value)\n)\nconst showPwdVisible = computed(\n  () => props.showPassword && !inputDisabled.value && !!nativeInputValue.value\n)\nconst isWordLimitVisible = computed(\n  () =>\n    props.showWordLimit &&\n    !!props.maxlength &&\n    (props.type === 'text' || props.type === 'textarea') &&\n    !inputDisabled.value &&\n    !props.readonly &&\n    !props.showPassword\n)\nconst textLength = computed(() => nativeInputValue.value.length)\nconst inputExceed = computed(\n  () =>\n    // show exceed style if length of initial value greater then maxlength\n    !!isWordLimitVisible.value && textLength.value > Number(props.maxlength)\n)\nconst suffixVisible = computed(\n  () =>\n    !!slots.suffix ||\n    !!props.suffixIcon ||\n    showClear.value ||\n    props.showPassword ||\n    isWordLimitVisible.value ||\n    (!!validateState.value && needStatusIcon.value)\n)\n\nconst [recordCursor, setCursor] = useCursor(input)\n\nuseResizeObserver(textarea, (entries) => {\n  onceInitSizeTextarea()\n  if (!isWordLimitVisible.value || props.resize !== 'both') return\n  const entry = entries[0]\n  const { width } = entry.contentRect\n  countStyle.value = {\n    /** right: 100% - width + padding(15) + right(6) */\n    right: `calc(100% - ${width + 15 + 6}px)`,\n  }\n})\n\nconst resizeTextarea = () => {\n  const { type, autosize } = props\n\n  if (!isClient || type !== 'textarea' || !textarea.value) return\n\n  if (autosize) {\n    const minRows = isObject(autosize) ? autosize.minRows : undefined\n    const maxRows = isObject(autosize) ? autosize.maxRows : undefined\n    const textareaStyle = calcTextareaHeight(textarea.value, minRows, maxRows)\n\n    // If the scrollbar is displayed, the height of the textarea needs more space than the calculated height.\n    // If set textarea height in this case, the scrollbar will not hide.\n    // So we need to hide scrollbar first, and reset it in next tick.\n    // see https://github.com/element-plus/element-plus/issues/8825\n    textareaCalcStyle.value = {\n      overflowY: 'hidden',\n      ...textareaStyle,\n    }\n\n    nextTick(() => {\n      // NOTE: Force repaint to make sure the style set above is applied.\n      textarea.value!.offsetHeight\n      textareaCalcStyle.value = textareaStyle\n    })\n  } else {\n    textareaCalcStyle.value = {\n      minHeight: calcTextareaHeight(textarea.value).minHeight,\n    }\n  }\n}\n\nconst createOnceInitResize = (resizeTextarea: () => void) => {\n  let isInit = false\n  return () => {\n    if (isInit || !props.autosize) return\n    const isElHidden = textarea.value?.offsetParent === null\n    if (!isElHidden) {\n      resizeTextarea()\n      isInit = true\n    }\n  }\n}\n// fix: https://github.com/element-plus/element-plus/issues/12074\nconst onceInitSizeTextarea = createOnceInitResize(resizeTextarea)\n\nconst setNativeInputValue = () => {\n  const input = _ref.value\n  const formatterValue = props.formatter\n    ? props.formatter(nativeInputValue.value)\n    : nativeInputValue.value\n  if (!input || input.value === formatterValue) return\n  input.value = formatterValue\n}\n\nconst handleInput = async (event: Event) => {\n  recordCursor()\n\n  let { value } = event.target as TargetElement\n\n  if (props.formatter && props.parser) {\n    value = props.parser(value)\n  }\n\n  // should not emit input during composition\n  // see: https://github.com/ElemeFE/element/issues/10516\n  if (isComposing.value) return\n\n  // hack for https://github.com/ElemeFE/element/issues/8548\n  // should remove the following line when we don't support IE\n  if (value === nativeInputValue.value) {\n    setNativeInputValue()\n    return\n  }\n\n  emit(UPDATE_MODEL_EVENT, value)\n  emit(INPUT_EVENT, value)\n\n  // ensure native input value is controlled\n  // see: https://github.com/ElemeFE/element/issues/12850\n  await nextTick()\n  setNativeInputValue()\n  setCursor()\n}\n\nconst handleChange = (event: Event) => {\n  let { value } = event.target as TargetElement\n\n  if (props.formatter && props.parser) {\n    value = props.parser(value)\n  }\n  emit(CHANGE_EVENT, value)\n}\n\nconst {\n  isComposing,\n  handleCompositionStart,\n  handleCompositionUpdate,\n  handleCompositionEnd,\n} = useComposition({ emit, afterComposition: handleInput })\n\nconst handlePasswordVisible = () => {\n  recordCursor()\n  passwordVisible.value = !passwordVisible.value\n  // The native input needs a little time to regain focus\n  setTimeout(setCursor)\n}\n\nconst focus = () => _ref.value?.focus()\n\nconst blur = () => _ref.value?.blur()\n\nconst handleMouseLeave = (evt: MouseEvent) => {\n  hovering.value = false\n  emit('mouseleave', evt)\n}\n\nconst handleMouseEnter = (evt: MouseEvent) => {\n  hovering.value = true\n  emit('mouseenter', evt)\n}\n\nconst handleKeydown = (evt: KeyboardEvent) => {\n  emit('keydown', evt)\n}\n\nconst select = () => {\n  _ref.value?.select()\n}\n\nconst clear = () => {\n  emit(UPDATE_MODEL_EVENT, '')\n  emit(CHANGE_EVENT, '')\n  emit('clear')\n  emit(INPUT_EVENT, '')\n}\n\nwatch(\n  () => props.modelValue,\n  () => {\n    nextTick(() => resizeTextarea())\n    if (props.validateEvent) {\n      elFormItem?.validate?.('change').catch((err) => debugWarn(err))\n    }\n  }\n)\n\n// native input value is set explicitly\n// do not use v-model / :value in template\n// see: https://github.com/ElemeFE/element/issues/14521\nwatch(nativeInputValue, () => setNativeInputValue())\n\n// when change between <input> and <textarea>,\n// update DOM dependent value and styles\n// https://github.com/ElemeFE/element/issues/14857\nwatch(\n  () => props.type,\n  async () => {\n    await nextTick()\n    setNativeInputValue()\n    resizeTextarea()\n  }\n)\n\nonMounted(() => {\n  if (!props.formatter && props.parser) {\n    debugWarn(\n      COMPONENT_NAME,\n      'If you set the parser, you also need to set the formatter.'\n    )\n  }\n  setNativeInputValue()\n  nextTick(resizeTextarea)\n})\n\ndefineExpose({\n  /** @description HTML input element */\n  input,\n  /** @description HTML textarea element */\n  textarea,\n  /** @description HTML element, input or textarea */\n  ref: _ref,\n  /** @description style of textarea. */\n  textareaStyle,\n\n  /** @description from props (used on unit test) */\n  autosize: toRef(props, 'autosize'),\n\n  /** @description is input composing */\n  isComposing,\n\n  /** @description HTML input element native method */\n  focus,\n  /** @description HTML input element native method */\n  blur,\n  /** @description HTML input element native method */\n  select,\n  /** @description clear input value */\n  clear,\n  /** @description resize textarea. */\n  resizeTextarea,\n})\n</script>\n"], "names": ["useRawAttrs", "useAttrs", "useSlots", "computed", "useFormItem", "useFormItemInputId", "useFormSize", "useFormDisabled", "useNamespace", "shallowRef", "ref", "useFocusController", "debugWarn", "IconView", "IconHide", "isNil", "useResizeObserver", "isClient", "isObject", "calcTextareaHeight", "nextTick", "textareaStyle", "event", "resizeTextarea", "input", "INPUT_EVENT", "CHANGE_EVENT", "useComposition", "UPDATE_MODEL_EVENT", "watch", "onMounted", "toRef", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref", "_normalizeStyle", "_createCommentVNode", "_Fragment", "_renderSlot", "_createElementVNode"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;uCAuMc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB;;;;;;;AAIA,IAAA,MAAM,WAAWA,YAAY,EAAA,CAAA;AAC7B,IAAA,MAAM,QAAQC,cAAS,EAAA,CAAA;AACvB,IAAA,MAAM,QAAQC,YAAS,EAAA,CAAA;AAEvB,IAAM,MAAA,YAAA,GAAeC,aAAS,MAAM;AAAA,MAClC,MAAM,IAAS,KAAA,UAAA,GAAa,WAAW,CAAE,EAAA,GAAI,QAAQ,CAAE,EAAA;AAAA,MACvD,OAAA,CAAQ,CAAE,CAAA,SAAA,CAAU,KAAK,CAAA;AAAA,MACzB,OAAQ,CAAA,EAAA,CAAG,UAAY,EAAA,aAAA,CAAc,KAAK,CAAA;AAAA,MAC1C,OAAQ,CAAA,EAAA,CAAG,QAAU,EAAA,WAAA,CAAY,KAAK,CAAA;AAAA,MACtC;AAAA,QACE,CAAC,QAAQ,CAAE,CAAA,OAAO,CAAC,GAAG,KAAA,CAAM,WAAW,KAAM,CAAA,MAAA;AAAA,QAC7C,CAAC,QAAQ,CAAE,CAAA,QAAQ,CAAC,GAAG,KAAA,CAAM,UAAU,KAAM,CAAA,UAAA;AAAA,QAC7C,CAAC,OAAA,CAAQ,CAAE,CAAA,QAAQ,CAAC,GAClB,KAAM,CAAA,MAAA,IAAU,KAAM,CAAA,UAAA,IAAc,KAAM,CAAA,SAAA,IAAa,KAAM,CAAA,YAAA;AAAA,QAC/D,CAAC,QAAQ,EAAG,CAAA,QAAA,EAAU,gBAAgB,CAAC,GACrC,SAAU,CAAA,KAAA,IAAS,cAAe,CAAA,KAAA;AAAA,QACpC,CAAC,OAAQ,CAAA,CAAA,CAAE,QAAQ,CAAC,GAAG,MAAM,IAAS,KAAA,QAAA;AAAA,OACxC;AAAA,MACA,QAAS,CAAA,KAAA;AAAA,KACV,CAAA,CAAA;AAED,IAAM,MAAA,UAAA,GAAaA,aAAS,MAAM;AAAA,MAChC,OAAA,CAAQ,EAAE,SAAS,CAAA;AAAA,MACnB,OAAQ,CAAA,EAAA,CAAG,OAAS,EAAA,SAAA,CAAU,KAAK,CAAA;AAAA,KACpC,CAAA,CAAA;AAED,IAAA,MAAM,EAAE,IAAM,EAAA,MAAA,EAAQ,QAAU,EAAA,UAAA,KAAeC,uBAAY,EAAA,CAAA;AAC3D,IAAA,MAAM,EAAE,OAAA,EAAY,GAAAC,8BAAA,CAAmB,KAAO,EAAA;AAAA,MAC5C,eAAiB,EAAA,UAAA;AAAA,KAClB,CAAA,CAAA;AACD,IAAA,MAAM,YAAYC,8BAAY,EAAA,CAAA;AAC9B,IAAA,MAAM,gBAAgBC,kCAAgB,EAAA,CAAA;AACtC,IAAM,MAAA,OAAA,GAAUC,qBAAa,OAAO,CAAA,CAAA;AACpC,IAAM,MAAA,UAAA,GAAaA,qBAAa,UAAU,CAAA,CAAA;AAE1C,IAAA,MAAM,QAAQC,cAA6B,EAAA,CAAA;AAC3C,IAAA,MAAM,WAAWA,cAAgC,EAAA,CAAA;AAEjD,IAAM,MAAA,QAAA,GAAWC,QAAI,KAAK,CAAA,CAAA;AAC1B,IAAM,MAAA,eAAA,GAAkBA,QAAI,KAAK,CAAA,CAAA;AACjC,IAAA,MAAM,aAAaA,OAAgB,EAAA,CAAA;AACnC,IAAM,MAAA,iBAAA,GAAoBD,cAAW,CAAA,KAAA,CAAM,UAAU,CAAA,CAAA;AAErD,IAAA,MAAM,OAAON,YAAS,CAAA,MAAM,KAAM,CAAA,KAAA,IAAS,SAAS,KAAK,CAAA,CAAA;AAGzD,IAAA,MAAM,EAAE,UAAA,EAAY,SAAW,EAAA,WAAA,EAAa,YAAe,GAAAQ,0BAAA,CAAA,IAAA,EAAA;AAAA,MACzD,QAAA,EAAA,aAAA;AAAA,MACA,SAAA,GAAA;AAAA,QACE,IAAU,EAAA,CAAA;AAAA,QACV,IAAY,KAAA,CAAA,aAAA,EAAA;AACV,UAAA,CAAA,EAAA,aAAyB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,UAAA,CAAA,QAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,UAAA,EAAA,MAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAAC,eAAA,CAAA,CAAA,CAAA,CAAA;AACvB,SAAY;AAAgD,OAC9D;AAAA,KACF,CAAA,CAAA;AAAA,IACF,MAAA,cAAA,GAAAT,YAAA,CAAA,MAAA;AAAA,MACF,IAAA,EAAA,CAAA;AAEA,MAAA,OAAuB,CAAA,EAAA,GAAA,MAAA,IAAA,IAAA,GAAS,KAAM,CAAA,GAAA,sBAA2B,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA;AACjE,KAAA,CAAA,CAAA;AACA,IAAA,MAAM,aAAe,GAAAA,YAAA,CAAA,MAAA,CAAA,UAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,UAAA,CAAA,aAAA,KAAA,EAAA,CAAA,CAAA;AAAA,IAAA,MACb,YAAA,GAAAA,YAAuB,CAAA,MAAA,aAAA,CAAA,mCAAyC,CAAA,aAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IACxE,MAAA,YAAA,GAAAA,YAAA,CAAA,MAAA,eAAA,CAAA,KAAA,GAAAU,aAAA,GAAAC,aAAA,CAAA,CAAA;AACA,IAAA,MAAM,cAAe,GAAAX,YAAA,CAAA,MAAA;AAAA,MAAS,QACZ,CAAA,KAAA;AAAmB,KACrC,CAAA,CAAA;AACA,IAAM,MAAA,aAAA,GAAAA,mBAA4C;AAAA,MAChD,KAAS,CAAA,UAAA;AAAA,MACV,iBAAA,CAAA,KAAA;AACD,MAAM,EAAA,MAAA,EAAA,KAAA,CAAA;AAA2C,KAAA,CAC/C,CAAM;AAAA,IAAA,MACY,gBAAA,GAAAA,YAAA,CAAA,MAAAY,mBAAA,CAAA,KAAA,CAAA,UAAA,CAAA,GAAA,EAAA,GAAA,MAAA,CAAA,KAAA,CAAA,UAAA,CAAA,CAAA,CAAA;AAAA,IAClB,MAAU,SAAA,GAAMZ,YAAO,CAAA,MAAA,KAAA,CAAA,SAAA,IAAA,CAAA,aAAA,CAAA,KAAA,IAAA,CAAA,KAAA,CAAA,QAAA,IAAA,CAAA,CAAA,gBAAA,CAAA,KAAA,KAAA,SAAA,CAAA,KAAA,IAAA,QAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IACzB,MAAC,cAAA,GAAAA,YAAA,CAAA,MAAA,KAAA,CAAA,YAAA,IAAA,CAAA,aAAA,CAAA,KAAA,IAAA,CAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,CAAA;AACD,IAAA,MAAM,kBAAmB,GAAAA,YAAA,CAAA,MAAA,KAAA,CAAA,aAAA,IAAA,CAAA,CAAA,KAAA,CAAA,SAAA,KAAA,KAAA,CAAA,IAAA,KAAA,MAAA,IAAA,KAAA,CAAA,IAAA,KAAA,UAAA,CAAA,IAAA,CAAA,aAAA,CAAA,KAAA,IAAA,CAAA,KAAA,CAAA,QAAA,IAAA,CAAA,KAAA,CAAA,YAAA,CAAA,CAAA;AAAA,IAAS,MAAA,UACpB,GAAAA,YAAA,CAAA,MAAc,sBAA4B,CAAA,MAAA,CAAA,CAAA;AAAA,IACxD,MAAA,WAAA,GAAAA,YAAA,CAAA,MAAA,CAAA,CAAA,kBAAA,CAAA,KAAA,IAAA,UAAA,CAAA,KAAA,GAAA,MAAA,CAAA,KAAA,CAAA,SAAA,CAAA,CAAA,CAAA;AACA,IAAA,MAAM,aAAY,GAAAA,YAAA,CAAA,MAAA,CAAA,CAAA,KAAA,CAAA,MAAA,IAAA,CAAA,CAAA,KAAA,CAAA,UAAA,IAAA,SAAA,CAAA,KAAA,IAAA,KAAA,CAAA,YAAA,IAAA,kBAAA,CAAA,KAAA,IAAA,CAAA,CAAA,aAAA,CAAA,KAAA,IAAA,cAAA,CAAA,KAAA,CAAA,CAAA;AAAA,IAAA,MAEd,CAAA,YACA,EAAA,8BACA,CAAA,KAAA,CAAA,CAAA;AAE6B,IACjCa,sBAAA,CAAA,QAAA,EAAA,CAAA,OAAA,KAAA;AACA,MAAA,oBAAuB,EAAA,CAAA;AAAA,MACrB,IAAA,CAAA,kBAA4B,CAAA,KAAC,gBAAuB,KAAA,MAAmB;AAAA,QACzE,OAAA;AACA,MAAA,MAA2B,KAAA,GAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AAAA,MACzB,MACE,OACA,EAAA,GAAA,KAAA,CAAA,WAAQ,CAAA;AAID,MACX,UAAA,CAAA,KAAA,GAAA;AACA,QAAA,KAAmB,EAAA,CAAA,YAAA,EAAA,KAAe,GAAA,EAAA,GAAA,CAAA,CAAA,GAAA,CAAA;AAClC,OAAA,CAAA;AAAoB,KAClB,CAAA,CAAA;AAAA,IAAA,MAAA,cAAA,GAAA,MAAA;AAAA,MAEE,MAAqB,EAAA,IAAA,EAAA,QAAA,EAAA,GAAA,KAAS;AAAyC,MAAA,IAAA,CAAAC,aAAA,IAAA,IAAA,KAAA,UAAA,IAAA,CAAA,QAAA,CAAA,KAAA;AAAA,QAC3E,OAAA;AACA,MAAA,IAAM,QAAgB,EAAA;AAAA,cAEhB,OACF,GAAAC,eAAE,CAAM,QAAA,CAAA,GAAA,mBAER,KAAA,CAAA,CAAA;AAEyC,QAC7C,MAAA,OAAA,GAAAA,eAAA,CAAA,QAAA,CAAA,GAAA,QAAA,CAAA,OAAA,GAAA,KAAA,CAAA,CAAA;AAEA,QAAA,MAAO,cAAuB,GAAAC,wBAAmB,CAAA,QAAA,CAAA,KAAA,EAAA,OAAA,EAAA,OAAA,CAAA,CAAA;AAEjD,QAAkB,iBAAA,CAAA,KAAA,GAAuB;AACvC,UAAqB,SAAA,EAAA,QAAA;AACrB,UAAI,GAAC,cAAA;AACL,SAAM,CAAA;AACN,QAAMC,YAAQ,CAAA,MAAA;AACd,UAAA,QAAmB,CAAA,KAAA,CAAA,YAAA,CAAA;AAAA,UAAA,iBAAA,CAAA,KAAA,GAAA,cAAA,CAAA;AAAA,SAEV,CAAA,CAAA;AAA6B,OACtC,MAAA;AAAA,QACD,iBAAA,CAAA,KAAA,GAAA;AAED,UAAM,mCAAuB,CAAA,QAAA,CAAA,KAAA,CAAA,CAAA,SAAA;AAC3B,SAAM,CAAA;AAEN,OAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,oBAAgB,GAAS,CAAQ,oBAAa;AAC9C,MAAA,IAAA,MAAgB,GAAA,KAAA,CAAA;AAChB,MAAA,OAAA,MAAsB;AAMtB,QAAA,IAAA,EAAA,CAAA;AAA0B,QAAA,IACb,MAAA,IAAA,CAAA,KAAA,CAAA,QAAA;AAAA,UACX,OAAGC;AAAA,QACL,MAAA,UAAA,GAAA,CAAA,CAAA,EAAA,GAAA,QAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,MAAA,IAAA,CAAA;AAEA,QAAA,IAAA,CAAA,UAAe,EAAA;AAEb,UAAA,eAAgB,EAAA,CAAA;AAChB,UAAA,MAAA,GAAA,IAAA,CAAA;AAA0B,SAC3B;AAAA,OACI,CAAA;AACL,KAAA,CAAA;AAA0B,IAAA,MACxB,oBAAW,GAAA,oBAA4B,CAAK,cAAE,CAAA,CAAA;AAAA,IAChD,MAAA,mBAAA,GAAA,MAAA;AAAA,MACF,MAAA,MAAA,GAAA,IAAA,CAAA,KAAA,CAAA;AAAA,MACF,MAAA,cAAA,GAAA,KAAA,CAAA,SAAA,GAAA,KAAA,CAAA,SAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,GAAA,gBAAA,CAAA,KAAA,CAAA;AAEA,MAAM,IAAA,CAAA,MAAA,IAAA,MAAA,CAAA,KAAuB,KAAgC,cAAA;AAC3D,QAAA,OAAa;AACb,MAAA,MAAA,CAAO,KAAM,GAAA,cAAA,CAAA;AACX,KAAI,CAAA;AACJ,IAAM,MAAA,WAAA,GAAA,OAAsBC,OAAA,KAAA;AAC5B,MAAA,YAAiB,EAAA,CAAA;AACf,MAAA,IAAAC,EAAe,KAAA,EAAA,GAAAD,OAAA,CAAA,MAAA,CAAA;AACf,MAAS,IAAA,KAAA,CAAA,SAAA,IAAA,KAAA,CAAA,MAAA,EAAA;AAAA,QACX,KAAA,GAAA,KAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OACF;AAAA,MACF,IAAA,WAAA,CAAA,KAAA;AAEA,QAAM,OAAA;AAEN,MAAA,IAAM,0BAA4B,CAAA,KAAA,EAAA;AAChC,QAAA,mBAAmB,EAAA,CAAA;AACnB,QAAM,OAAA;AAGN,OAAA;AACA,MAAAE,6BAAc,EAAA,KAAA,CAAA,CAAA;AAAA,MAChB,IAAA,CAAAC,iBAAA,EAAA,KAAA,CAAA,CAAA;AAEA,MAAM,MAAAL,YAAA,EAAA,CAAA;AACJ,MAAa,mBAAA,EAAA,CAAA;AAEb,MAAI,SAAQ,EAAA,CAAA;AAEZ,KAAI,CAAA;AACF,IAAQ,MAAA,YAAM,WAAY,KAAA;AAAA,MAC5B,IAAA,EAAA,KAAA,EAAA,GAAAE,OAAA,CAAA,MAAA,CAAA;AAIA,MAAA,IAAI,eAAmB,IAAA,KAAA,CAAA,MAAA,EAAA;AAIvB,QAAI,KAAA,GAAA;AACF,OAAoB;AACpB,MAAA,IAAA,CAAAI,kBAAA,EAAA,KAAA,CAAA,CAAA;AAAA,KACF,CAAA;AAEA,IAAA,MAAA;AACA,MAAA;AAIA,MAAA,sBAAe;AACf,MAAoB,uBAAA;AACpB,MAAU,oBAAA;AAAA,KACZ,GAAAC,sBAAA,CAAA,EAAA,IAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,CAAA,CAAA;AAEA,IAAM,MAAA,qBAAiC,GAAA,MAAA;AACrC,MAAI,YAAQ,EAAA,CAAA;AAEZ,MAAI,eAAmB,CAAA,KAAA,GAAA,CAAA,eAAc,CAAA,KAAA,CAAA;AACnC,MAAQ,UAAA,CAAA;AAAkB,KAC5B,CAAA;AACA,IAAA,MAAA;AAAwB,MAC1B,IAAA,EAAA,CAAA;AAEA,MAAM,OAAA,CAAA,EAAA,GAAA,IAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,EAAA,CAAA;AAAA,KACJ,CAAA;AAAA,IACA,MAAA,IAAA,GAAA,MAAA;AAAA,MACA,IAAA,EAAA,CAAA;AAAA,MACA,OAAA,CAAA,EAAA,GAAA,IAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,EAAA,CAAA;AAAA;AAGF,IAAA,MAAM,4BAA8B;AAClC,MAAa,QAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AACb,MAAgB,IAAA,CAAA,YAAA,EAAA,GAAA,CAAA,CAAQ;AAExB,KAAA,CAAA;AAAoB,IACtB,MAAA,gBAAA,GAAA,CAAA,GAAA,KAAA;AAEA,MAAA,QAAc,CAAA,KAAA,GAAA,IAAW,CAAA;AAEzB,MAAA,IAAM,CAAO,YAAM,EAAK,GAAA,CAAA,CAAA;AAExB,KAAM,CAAA;AACJ,IAAA,MAAA,aAAiB,GAAA,CAAA,GAAA,KAAA;AACjB,MAAA,IAAA,CAAK,cAAc,CAAG,CAAA;AAAA,KACxB,CAAA;AAEA,IAAM,MAAA,MAAA,GAAA,MAAA;AACJ,MAAA,IAAA,EAAA,CAAA;AACA,MAAA,CAAA,EAAA,kBAAsB,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,EAAA,CAAA;AAAA,KACxB,CAAA;AAEA,IAAM,MAAA,KAAA,GAAA,MAAA;AACJ,MAAA,IAAA,CAAKC,wBAAc,EAAA,EAAA,CAAA,CAAA;AAAA,MACrB,IAAA,CAAAF,kBAAA,EAAA,EAAA,CAAA,CAAA;AAEA,MAAA,IAAM,SAAS,CAAM;AACnB,MAAA,IAAA,CAAKD,iBAAc,EAAA,EAAA,CAAA,CAAA;AAAA,KACrB,CAAA;AAEA,IAAAI,SAAA,CAAM,WAAc,CAAA,UAAA,EAAA,MAAA;AAClB,MAAA,IAAA;AACA,MAAAT,iCAAqB,EAAA,CAAA,CAAA;AACrB,MAAA,IAAA,KAAY,CAAA,aAAA,EAAA;AACZ,QAAA,CAAA,EAAK,aAAa,IAAE,IAAA,GAAA,KAAA,CAAA,GAAA,UAAA,CAAA,QAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,UAAA,EAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAAR,eAAA,CAAA,CAAA,CAAA,CAAA;AAAA,OACtB;AAEA,KAAA,CAAA,CAAA;AAAA,IAAAiB,0BACc,EAAA,MAAA,mBAAA,EAAA,CAAA,CAAA;AAAA,IAAAA,SACN,CAAA,MAAA,KAAA,CAAA,IAAA,EAAA,YAAA;AACJ,MAAS,MAAAT,YAAA,EAAA,CAAM;AACf,MAAA,mBAAyB,EAAA,CAAA;AACvB,MAAY,cAAA,EAAA,CAAA;AAAkD,KAChE,CAAA,CAAA;AAAA,IACFU,aAAA,CAAA,MAAA;AAAA,MACF,IAAA,CAAA,KAAA,CAAA,SAAA,IAAA,KAAA,CAAA,MAAA,EAAA,CAUA;AAAA,MACE,mBAAY,EAAA,CAAA;AAAA,MACZV,YAAY,CAAA,cAAA,CAAA,CAAA;AACV,KAAA,CAAA,CAAA;AACA,IAAoB,MAAA,CAAA;AACpB,MAAe,KAAA;AAAA,MACjB,QAAA;AAAA,MACF,GAAA,EAAA,IAAA;AAEA,MAAA,aAAgB;AACd,MAAA,QAAK,EAAAW,SAAmB,CAAA,KAAA,EAAA,UAAc,CAAA;AACpC,MAAA,WAAA;AAAA,MACE,KAAA;AAAA,MACA,IAAA;AAAA,MACF,MAAA;AAAA,MACF,KAAA;AACA,MAAoB,cAAA;AACpB,KAAA,CAAA,CAAA;AAAuB,IACzB,OAAC,CAAA,IAAA,EAAA,MAAA,KAAA;AAED,MAAa,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,QAAA,KAAA,EAAAC,kBAAA,CAAA;AAAA,UAEXC,SAAA,CAAA,YAAA,CAAA;AAAA,UAAA;AAAA,YAEA,CAAAA,SAAA,CAAA,OAAA,CAAA,CAAA,EAAA,CAAA,OAAA,EAAA,QAAA,CAAA,GAAA,IAAA,CAAA,MAAA,CAAA,MAAA;AAAA,YAAA,CAAAA,SAAA,CAAA,OAAA,CAAA,CAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,GAAA,IAAA,CAAA,MAAA,CAAA,OAAA;AAAA,WAEK;AAAA,SAAA,CAAA;AAAA,QAEL,KAAA,EAAAC,kBAAA,CAAAD,SAAA,CAAA,cAAA,CAAA,CAAA;AAAA,QAAA,YAAA,EAAA,gBAAA;AAAA,QAGA,YAAgB,EAAA,gBAAiB;AAAA,OAAA,EAAA;AAAA,QAGjCE,sBAAA,CAAA,SAAA,CAAA;AAAA,QAAA,IAAA,CAAA,IAAA,KAAA,UAAA,IAAAL,aAAA,EAAA,EAAAC,sBAAA,CAAAK,YAAA,EAAA,EAAA,GAAA,EAAA,CAAA,EAAA,EAAA;AAAA,UAGAD,sBAAA,CAAA,gBAAA,CAAA;AAAA,UAAA,IAAA,CAAA,MAAA,CAAA,OAAA,IAAAL,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,YAEA,GAAA,EAAA,CAAA;AAAA,YAAA,KAAA,EAAAC,kBAAA,CAAAC,SAAA,CAAA,OAAA,CAAA,CAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,CAAA;AAAA,WAEA,EAAA;AAAA,YAAAI,cAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA;AAAA,WAEA,EAAA,CAAA,CAAA,IAAAF,sBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,UAAAG,sBAAA,CAAA,KAAA,EAAA;AAAA,YAEA,OAAA,EAAA,YAAA;AAAA,YACD,GAAA,EAAA,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}