{"version": 3, "file": "use-common-picker.js", "sources": ["../../../../../../../packages/components/time-picker/src/composables/use-common-picker.ts"], "sourcesContent": ["import { computed, ref } from 'vue'\nimport { isEqual } from 'lodash-unified'\nimport { useLocale } from '@element-plus/hooks/use-locale'\nimport { isArray } from '@element-plus/utils'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { dayOrDaysToDate, formatter, parseDate, valueEquals } from '../utils'\n\nimport type { Dayjs } from 'dayjs'\nimport type {\n  DateModelType,\n  DayOrDays,\n  ModelValueType,\n  PickerOptions,\n  SingleOrRange,\n  UserInput,\n} from '../common/props'\n\ninterface CommonPickerProps {\n  modelValue: ModelValueType | null\n  valueFormat?: string\n}\ntype CommonPickerEmits = (\n  event: 'update:modelValue' | 'calendar-change' | 'panel-change',\n  ...args: any[]\n) => void\n\nexport const useCommonPicker = <\n  P extends CommonPickerProps,\n  E extends CommonPickerEmits\n>(\n  props: P,\n  emit: E\n) => {\n  const { lang } = useLocale()\n  const pickerVisible = ref(false)\n  const pickerActualVisible = ref(false)\n  const userInput = ref<UserInput>(null)\n\n  const valueIsEmpty = computed(() => {\n    const { modelValue } = props\n    return (\n      !modelValue || (isArray(modelValue) && !modelValue.filter(Boolean).length)\n    )\n  })\n\n  const emitInput = (input: SingleOrRange<DateModelType> | null) => {\n    if (!valueEquals(props.modelValue, input)) {\n      let formatted\n      if (isArray(input)) {\n        formatted = input.map((item) =>\n          formatter(item, props.valueFormat, lang.value)\n        )\n      } else if (input) {\n        formatted = formatter(input, props.valueFormat, lang.value)\n      }\n      const emitVal = input ? formatted : input\n      emit(UPDATE_MODEL_EVENT, emitVal, lang.value)\n    }\n  }\n\n  const parsedValue = computed(() => {\n    let dayOrDays: DayOrDays\n    if (valueIsEmpty.value) {\n      if (pickerOptions.value.getDefaultValue) {\n        dayOrDays = pickerOptions.value.getDefaultValue()\n      }\n    } else {\n      if (isArray(props.modelValue)) {\n        dayOrDays = props.modelValue.map((d) =>\n          parseDate(d, props.valueFormat, lang.value)\n        ) as [Dayjs, Dayjs]\n      } else {\n        dayOrDays = parseDate(\n          props.modelValue ?? '',\n          props.valueFormat,\n          lang.value\n        )!\n      }\n    }\n\n    if (pickerOptions.value.getRangeAvailableTime) {\n      const availableResult = pickerOptions.value.getRangeAvailableTime(\n        dayOrDays!\n      )\n      if (!isEqual(availableResult, dayOrDays!)) {\n        dayOrDays = availableResult\n\n        // The result is corrected only when model-value exists\n        if (!valueIsEmpty.value) {\n          emitInput(dayOrDaysToDate(dayOrDays))\n        }\n      }\n    }\n    if (isArray(dayOrDays!) && dayOrDays.some((day) => !day)) {\n      dayOrDays = [] as unknown as DayOrDays\n    }\n    return dayOrDays!\n  })\n\n  const pickerOptions = ref<Partial<PickerOptions>>({})\n\n  const onSetPickerOption = <T extends keyof PickerOptions>(\n    e: [T, PickerOptions[T]]\n  ) => {\n    pickerOptions.value[e[0]] = e[1]\n    pickerOptions.value.panelReady = true\n  }\n\n  const onCalendarChange = (e: [Date, null | Date]) => {\n    emit('calendar-change', e)\n  }\n\n  const onPanelChange = (\n    value: [Dayjs, Dayjs],\n    mode: 'month' | 'year',\n    view: unknown\n  ) => {\n    emit('panel-change', value, mode, view)\n  }\n\n  const onPick = (date: any = '', visible = false) => {\n    pickerVisible.value = visible\n    let result\n    if (isArray(date)) {\n      result = date.map((_) => _.toDate())\n    } else {\n      // clear btn emit null\n      result = date ? date.toDate() : date\n    }\n    userInput.value = null\n    emitInput(result)\n  }\n\n  return {\n    parsedValue,\n    pickerActualVisible,\n    pickerOptions,\n    pickerVisible,\n    userInput,\n    valueIsEmpty,\n    emitInput,\n    onCalendarChange,\n    onPanelChange,\n    onPick,\n    onSetPickerOption,\n  }\n}\n\nexport type CommonPickerContext = ReturnType<typeof useCommonPicker>\n"], "names": ["useLocale", "ref", "computed", "isArray", "valueEquals", "formatter", "UPDATE_MODEL_EVENT", "parseDate", "isEqual", "dayOrDaysToDate"], "mappings": ";;;;;;;;;;;AAMY,MAAC,eAAe,GAAG,CAAC,KAAK,EAAE,IAAI,KAAK;AAChD,EAAE,MAAM,EAAE,IAAI,EAAE,GAAGA,eAAS,EAAE,CAAC;AAC/B,EAAE,MAAM,aAAa,GAAGC,OAAG,CAAC,KAAK,CAAC,CAAC;AACnC,EAAE,MAAM,mBAAmB,GAAGA,OAAG,CAAC,KAAK,CAAC,CAAC;AACzC,EAAE,MAAM,SAAS,GAAGA,OAAG,CAAC,IAAI,CAAC,CAAC;AAC9B,EAAE,MAAM,YAAY,GAAGC,YAAQ,CAAC,MAAM;AACtC,IAAI,MAAM,EAAE,UAAU,EAAE,GAAG,KAAK,CAAC;AACjC,IAAI,OAAO,CAAC,UAAU,IAAIC,cAAO,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;AACpF,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,SAAS,GAAG,CAAC,KAAK,KAAK;AAC/B,IAAI,IAAI,CAACC,iBAAW,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE;AAC/C,MAAM,IAAI,SAAS,CAAC;AACpB,MAAM,IAAID,cAAO,CAAC,KAAK,CAAC,EAAE;AAC1B,QAAQ,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAKE,eAAS,CAAC,IAAI,EAAE,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACxF,OAAO,MAAM,IAAI,KAAK,EAAE;AACxB,QAAQ,SAAS,GAAGA,eAAS,CAAC,KAAK,EAAE,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AACpE,OAAO;AACP,MAAM,MAAM,OAAO,GAAG,KAAK,GAAG,SAAS,GAAG,KAAK,CAAC;AAChD,MAAM,IAAI,CAACC,wBAAkB,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AACpD,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAGJ,YAAQ,CAAC,MAAM;AACrC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,SAAS,CAAC;AAClB,IAAI,IAAI,YAAY,CAAC,KAAK,EAAE;AAC5B,MAAM,IAAI,aAAa,CAAC,KAAK,CAAC,eAAe,EAAE;AAC/C,QAAQ,SAAS,GAAG,aAAa,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;AAC1D,OAAO;AACP,KAAK,MAAM;AACX,MAAM,IAAIC,cAAO,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;AACrC,QAAQ,SAAS,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,KAAKI,eAAS,CAAC,CAAC,EAAE,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7F,OAAO,MAAM;AACb,QAAQ,SAAS,GAAGA,eAAS,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,EAAE,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AACxG,OAAO;AACP,KAAK;AACL,IAAI,IAAI,aAAa,CAAC,KAAK,CAAC,qBAAqB,EAAE;AACnD,MAAM,MAAM,eAAe,GAAG,aAAa,CAAC,KAAK,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;AACnF,MAAM,IAAI,CAACC,qBAAO,CAAC,eAAe,EAAE,SAAS,CAAC,EAAE;AAChD,QAAQ,SAAS,GAAG,eAAe,CAAC;AACpC,QAAQ,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AACjC,UAAU,SAAS,CAACC,qBAAe,CAAC,SAAS,CAAC,CAAC,CAAC;AAChD,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,IAAIN,cAAO,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE;AAC7D,MAAM,SAAS,GAAG,EAAE,CAAC;AACrB,KAAK;AACL,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,aAAa,GAAGF,OAAG,CAAC,EAAE,CAAC,CAAC;AAChC,EAAE,MAAM,iBAAiB,GAAG,CAAC,CAAC,KAAK;AACnC,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACrC,IAAI,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC;AAC1C,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,CAAC,CAAC,KAAK;AAClC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;AAC/B,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,KAAK;AAC/C,IAAI,IAAI,CAAC,cAAc,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC5C,GAAG,CAAC;AACJ,EAAE,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,EAAE,OAAO,GAAG,KAAK,KAAK;AACjD,IAAI,aAAa,CAAC,KAAK,GAAG,OAAO,CAAC;AAClC,IAAI,IAAI,MAAM,CAAC;AACf,IAAI,IAAIE,cAAO,CAAC,IAAI,CAAC,EAAE;AACvB,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;AAC3C,KAAK,MAAM;AACX,MAAM,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;AAC3C,KAAK;AACL,IAAI,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC;AAC3B,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC;AACtB,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,WAAW;AACf,IAAI,mBAAmB;AACvB,IAAI,aAAa;AACjB,IAAI,aAAa;AACjB,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,gBAAgB;AACpB,IAAI,aAAa;AACjB,IAAI,MAAM;AACV,IAAI,iBAAiB;AACrB,GAAG,CAAC;AACJ;;;;"}