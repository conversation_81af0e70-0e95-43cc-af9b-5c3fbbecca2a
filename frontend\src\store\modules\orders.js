import { ordersAPI } from '@/utils/api'

const state = {
  orders: [],
  currentOrder: null,
  loading: false,
  pagination: {
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  }
}

const mutations = {
  SET_ORDERS(state, orders) {
    state.orders = orders
  },
  
  SET_CURRENT_ORDER(state, order) {
    state.currentOrder = order
  },
  
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  
  SET_PAGINATION(state, pagination) {
    state.pagination = { ...state.pagination, ...pagination }
  },
  
  ADD_ORDER(state, order) {
    state.orders.unshift(order)
  },
  
  UPDATE_ORDER(state, updatedOrder) {
    const index = state.orders.findIndex(o => o.id === updatedOrder.id)
    if (index !== -1) {
      state.orders.splice(index, 1, updatedOrder)
    }
    
    if (state.currentOrder && state.currentOrder.id === updatedOrder.id) {
      state.currentOrder = updatedOrder
    }
  }
}

const actions = {
  // 加载订单列表
  async loadOrders({ commit }, params = {}) {
    commit('SET_LOADING', true)
    try {
      const response = await ordersAPI.getOrders(params)
      const { data, page, page_size, total, total_pages } = response.data.data
      
      commit('SET_ORDERS', data)
      commit('SET_PAGINATION', {
        page,
        pageSize: page_size,
        total,
        totalPages: total_pages
      })
      
      return { success: true, data: response.data.data }
    } catch (error) {
      throw new Error(error.response?.data?.message || '加载订单失败')
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 加载单个订单
  async loadOrder({ commit }, orderId) {
    commit('SET_LOADING', true)
    try {
      const response = await ordersAPI.getOrder(orderId)
      commit('SET_CURRENT_ORDER', response.data.data)
      return { success: true, data: response.data.data }
    } catch (error) {
      throw new Error(error.response?.data?.message || '加载订单详情失败')
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 创建订单
  async createOrder({ commit, dispatch }, orderData) {
    try {
      const response = await ordersAPI.createOrder(orderData)
      const order = response.data.data
      
      commit('ADD_ORDER', order)
      
      // 显示成功通知
      dispatch('notifications/showNotification', {
        type: 'success',
        message: '订单创建成功'
      }, { root: true })
      
      // 清空购物车
      dispatch('cart/clearCart', null, { root: true })
      
      return { success: true, data: order }
    } catch (error) {
      const message = error.response?.data?.message || '创建订单失败'
      dispatch('notifications/showNotification', {
        type: 'error',
        message
      }, { root: true })
      throw new Error(message)
    }
  },
  
  // 更新订单状态
  async updateOrderStatus({ commit, dispatch }, { orderId, status }) {
    try {
      await ordersAPI.updateOrderStatus(orderId, { status })
      
      // 重新加载订单详情
      const response = await ordersAPI.getOrder(orderId)
      const updatedOrder = response.data.data
      
      commit('UPDATE_ORDER', updatedOrder)
      
      dispatch('notifications/showNotification', {
        type: 'success',
        message: '订单状态更新成功'
      }, { root: true })
      
      return { success: true, data: updatedOrder }
    } catch (error) {
      const message = error.response?.data?.message || '更新订单状态失败'
      dispatch('notifications/showNotification', {
        type: 'error',
        message
      }, { root: true })
      throw new Error(message)
    }
  }
}

const getters = {
  orders: state => state.orders,
  currentOrder: state => state.currentOrder,
  loading: state => state.loading,
  pagination: state => state.pagination,
  
  // 按状态获取订单
  ordersByStatus: state => status => {
    return state.orders.filter(order => order.status === status)
  },
  
  // 获取最近订单
  recentOrders: state => {
    return state.orders.slice(0, 5)
  },
  
  // 订单统计
  orderStats: state => {
    const stats = {
      total: state.orders.length,
      pending: 0,
      confirmed: 0,
      preparing: 0,
      shipping: 0,
      delivered: 0,
      cancelled: 0
    }
    
    state.orders.forEach(order => {
      if (stats.hasOwnProperty(order.status)) {
        stats[order.status]++
      }
    })
    
    return stats
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
