{"version": 3, "file": "color.js", "sources": ["../../../../../../../packages/components/color-picker-panel/src/utils/color.ts"], "sourcesContent": ["import { TinyColor } from '@ctrl/tinycolor'\nimport { hasOwn } from '@element-plus/utils'\n\nimport type { ColorFormats } from '@ctrl/tinycolor'\n\ninterface ColorOptions {\n  enableAlpha: boolean\n  format: string\n  value?: string | null\n}\n\nexport default class Color {\n  private _hue = 0\n  private _saturation = 100\n  private _value = 100\n  private _alpha = 100\n  private _tiny = new TinyColor()\n  private _isValid = false\n  public enableAlpha = false\n  public format = ''\n  public value = ''\n  public selected?: boolean\n\n  constructor(options: Partial<ColorOptions> = {}) {\n    for (const option in options) {\n      if (hasOwn(options, option)) {\n        this[option] = options[option]\n      }\n    }\n    if (options.value) {\n      this.fromString(options.value)\n    } else {\n      this.doOnChange()\n    }\n  }\n\n  set(prop: { [key: string]: any } | any, value?: number) {\n    if (arguments.length === 1 && typeof prop === 'object') {\n      for (const p in prop) {\n        if (hasOwn(prop, p)) {\n          this.set(p, prop[p])\n        }\n      }\n\n      return\n    }\n    ;(this as any)[`_${prop}`] = value\n    this._isValid = true\n    this.doOnChange()\n  }\n\n  get(prop: string) {\n    if (['hue', 'saturation', 'value', 'alpha'].includes(prop)) {\n      return Math.round((this as any)[`_${prop}`])\n    }\n    return (this as any)[`_${prop}`]\n  }\n\n  toRgb() {\n    return this._isValid ? this._tiny.toRgb() : { r: 255, g: 255, b: 255, a: 0 }\n  }\n\n  fromString(value: string) {\n    const color = new TinyColor(value)\n    this._isValid = color.isValid\n    if (color.isValid) {\n      const { h, s, v, a } = color.toHsv()\n      this._hue = h\n      this._saturation = s * 100\n      this._value = v * 100\n      this._alpha = a * 100\n    } else {\n      this._hue = 0\n      this._saturation = 100\n      this._value = 100\n      this._alpha = 100\n    }\n    this.doOnChange()\n  }\n\n  compare(color: this) {\n    const compareColor = new TinyColor({\n      h: color._hue,\n      s: color._saturation / 100,\n      v: color._value / 100,\n      a: color._alpha / 100,\n    })\n    return this._tiny.equals(compareColor)\n  }\n\n  doOnChange() {\n    const { _hue, _saturation, _value, _alpha, format, enableAlpha } = this\n    let _format = format || (enableAlpha ? 'rgb' : 'hex')\n    if (format === 'hex' && enableAlpha) {\n      _format = 'hex8'\n    }\n    this._tiny = new TinyColor({\n      h: _hue,\n      s: _saturation / 100,\n      v: _value / 100,\n      a: _alpha / 100,\n    })\n    this.value = this._isValid\n      ? this._tiny.toString(_format as ColorFormats)\n      : ''\n  }\n}\n"], "names": ["TinyColor", "hasOwn"], "mappings": ";;;;;;;AAEe,MAAM,KAAK,CAAC;AAC3B,EAAE,WAAW,CAAC,OAAO,GAAG,EAAE,EAAE;AAC5B,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;AAClB,IAAI,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;AAC3B,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;AACtB,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;AACtB,IAAI,IAAI,CAAC,KAAK,GAAG,IAAIA,mBAAS,EAAE,CAAC;AACjC,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC1B,IAAI,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AAC7B,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;AACrB,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACpB,IAAI,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;AAClC,MAAM,IAAIC,aAAM,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE;AACnC,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AACvC,OAAO;AACP,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE;AACvB,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACrC,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;AACxB,KAAK;AACL,GAAG;AACH,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE;AACnB,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC5D,MAAM,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE;AAC5B,QAAQ,IAAIA,aAAM,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;AAC7B,UAAU,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B,SAAS;AACT,OAAO;AACP,MAAM,OAAO;AACb,KAAK;AAEL,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AAC7B,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;AACtB,GAAG;AACH,EAAE,GAAG,CAAC,IAAI,EAAE;AACZ,IAAI,IAAI,CAAC,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AAChE,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5B,GAAG;AACH,EAAE,KAAK,GAAG;AACV,IAAI,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACjF,GAAG;AACH,EAAE,UAAU,CAAC,KAAK,EAAE;AACpB,IAAI,MAAM,KAAK,GAAG,IAAID,mBAAS,CAAC,KAAK,CAAC,CAAC;AACvC,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC;AAClC,IAAI,IAAI,KAAK,CAAC,OAAO,EAAE;AACvB,MAAM,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;AAC3C,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;AACpB,MAAM,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,GAAG,CAAC;AACjC,MAAM,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC;AAC5B,MAAM,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC;AAC5B,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;AACpB,MAAM,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;AAC7B,MAAM,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;AACxB,MAAM,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;AACxB,KAAK;AACL,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;AACtB,GAAG;AACH,EAAE,OAAO,CAAC,KAAK,EAAE;AACjB,IAAI,MAAM,YAAY,GAAG,IAAIA,mBAAS,CAAC;AACvC,MAAM,CAAC,EAAE,KAAK,CAAC,IAAI;AACnB,MAAM,CAAC,EAAE,KAAK,CAAC,WAAW,GAAG,GAAG;AAChC,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,GAAG;AAC3B,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,GAAG;AAC3B,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AAC3C,GAAG;AACH,EAAE,UAAU,GAAG;AACf,IAAI,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;AAC5E,IAAI,IAAI,OAAO,GAAG,MAAM,KAAK,WAAW,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC;AAC1D,IAAI,IAAI,MAAM,KAAK,KAAK,IAAI,WAAW,EAAE;AACzC,MAAM,OAAO,GAAG,MAAM,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,CAAC,KAAK,GAAG,IAAIA,mBAAS,CAAC;AAC/B,MAAM,CAAC,EAAE,IAAI;AACb,MAAM,CAAC,EAAE,WAAW,GAAG,GAAG;AAC1B,MAAM,CAAC,EAAE,MAAM,GAAG,GAAG;AACrB,MAAM,CAAC,EAAE,MAAM,GAAG,GAAG;AACrB,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;AACnE,GAAG;AACH;;;;"}