{"version": 3, "file": "splitter.mjs", "sources": ["../../../../../../packages/components/splitter/src/splitter.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type Splitter from './splitter.vue'\n\nexport const splitterProps = buildProps({\n  layout: {\n    type: String,\n    default: 'horizontal',\n    values: ['horizontal', 'vertical'] as const,\n  },\n  lazy: Boolean,\n} as const)\n\nexport type SplitterProps = ExtractPropTypes<typeof splitterProps>\nexport type SplitterPropsPublic = __ExtractPublicPropTypes<typeof splitterProps>\nexport type SplitterInstance = InstanceType<typeof Splitter> & unknown\n"], "names": [], "mappings": ";;AACY,MAAC,aAAa,GAAG,UAAU,CAAC;AACxC,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,YAAY;AACzB,IAAI,MAAM,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;AACtC,GAAG;AACH,EAAE,IAAI,EAAE,OAAO;AACf,CAAC;;;;"}