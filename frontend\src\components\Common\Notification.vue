<template>
  <teleport to="body">
    <transition-group name="notification" tag="div" class="notification-container">
      <div
        v-for="notification in notifications"
        :key="notification.id"
        :class="['notification', `notification-${notification.type}`]"
      >
        <el-icon class="notification-icon">
          <SuccessFilled v-if="notification.type === 'success'" />
          <WarningFilled v-else-if="notification.type === 'warning'" />
          <CircleCloseFilled v-else-if="notification.type === 'error'" />
          <InfoFilled v-else />
        </el-icon>
        <span class="notification-message">{{ notification.message }}</span>
        <el-icon class="notification-close" @click="removeNotification(notification.id)">
          <Close />
        </el-icon>
      </div>
    </transition-group>
  </teleport>
</template>

<script>
import { computed } from 'vue'
import { useStore } from 'vuex'
import { SuccessFilled, WarningFilled, CircleCloseFilled, InfoFilled, Close } from '@element-plus/icons-vue'

export default {
  name: 'Notification',
  components: {
    SuccessFilled,
    WarningFilled,
    CircleCloseFilled,
    InfoFilled,
    Close
  },
  setup() {
    const store = useStore()
    
    const notifications = computed(() => store.getters['notifications/notifications'] || [])
    
    const removeNotification = (id) => {
      store.dispatch('notifications/removeNotification', id)
    }
    
    return {
      notifications,
      removeNotification
    }
  }
}
</script>

<style lang="scss" scoped>
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.notification {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border-radius: var(--radius-sm);
  color: white;
  font-weight: 600;
  box-shadow: var(--shadow-2);
  min-width: 300px;
  cursor: pointer;
  
  .notification-icon {
    font-size: 20px;
  }
  
  .notification-message {
    flex: 1;
  }
  
  .notification-close {
    font-size: 16px;
    opacity: 0.7;
    cursor: pointer;
    
    &:hover {
      opacity: 1;
    }
  }
  
  &.notification-success {
    background: var(--color-success);
  }
  
  &.notification-error {
    background: var(--color-error);
  }
  
  &.notification-warning {
    background: var(--color-warning);
  }
  
  &.notification-info {
    background: var(--color-info);
  }
}

.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.notification-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

@include respond-to('mobile') {
  .notification-container {
    left: 20px;
    right: 20px;
  }
  
  .notification {
    min-width: auto;
  }
}
</style>
